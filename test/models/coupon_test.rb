# == Schema Information
#
# Table name: coupons
#
#  id              :integer          not null, primary key
#  name            :string(255)
#  limit           :integer
#  start_date      :datetime
#  end_date        :datetime
#  advertise       :boolean
#  code            :string(255)
#  percent_off     :integer
#  flat_off        :integer
#  min_amount      :integer
#  designer_id     :integer
#  created_at      :datetime         not null
#  updated_at      :datetime         not null
#  coupon_type     :string(255)
#  use_count       :integer
#  notified        :integer
#  source_order_id :integer
#  created_by      :integer
#

require 'test_helper'

class CouponTest < ActiveSupport::TestCase
  # test "the truth" do
  #   assert true
  # end
end
