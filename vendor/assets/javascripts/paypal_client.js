var PAYPAL_SDK = PAYPAL_SDK || {};

PAYPAL_SDK = (function(window, document, Paypal){
  Paypal.fundingSource = null
  Paypal.methods = { 
    paypalButtonRender: function(){

      paypal.Buttons({
        style: {
          size: 'medium',
          color: 'gold',
          shape: 'rect',
          label: 'pay',
          tagline: false
        },
    
        // env: gon.env_name, // Valid values are sandbox and live.
    
        onInit: function(data, actions) {
          $(document).ready(function(){
            PAYPAL_SDK.methods.paypalButtonHandler(actions)
            ACCORDIAN.methods.activateAccordion(".pay_online")
          })

        },
    
        onClick: function(data) {
          Paypal.fundingSource =  data.fundingSource
          GA_PAYMENT_INFO.events.init(Paypal.fundingSource)
        },

        createOrder: function() {
          // Set up a url on your server to create the payment
          var CREATE_URL = '/orders';
          var payload = INT_PAYMENT_OPTIONS.methods.getPaymentPayload()
          payload['order[pay_type]'] = Paypal.fundingSource
          payload['order[attempted_payment_gateway]'] = 'paypal_smartpay'
          return INT_PAYMENT_OPTIONS.methods.postApiCall(CREATE_URL, payload)
          .then(function(res) {
            if (res.has_error != undefined  ) {
              window.location.href = "/order/paypal_response_handling?error=true&messages=" + res.errors
            } else if (res.redirect_to){
              return (window.location.href = res.redirect_to);
            } else {
              sessionStorage.setItem("returnUrl", res.return_url);
              return res.id
            }
          });
        },  
    
        onApprove: function(data, actions) {
          var EXECUTE_URL = '/order/paypal_execute';
          var data = {
            orderID: data.orderID,
          };
          return fetch(EXECUTE_URL, {
            method: 'post',
            headers: {
              'content-type': 'application/json'
            },
            body: JSON.stringify(data)
            })
            .then(function (res) {
              if (res.status == 200) {
                return res.json()
              }
              else {
                window.location.href = '/order/paypal_response_handling?error=true'
              }
            })
            .then(function (res) {
              var return_url = sessionStorage.getItem("returnUrl");
              if (res.has_error){
                window.location.href = '/order/paypal_response_handling?error=true&messages=' + res.message  
              }
              else {
              window.location.href = '/order/paypal_response_handling?returnUrl=' + return_url
              }
            });
        },

        onError: function(){
          window.location.href = "/order/paypal_response_handling?error=true" 
        }
        
      }).render('#paypal-button-container');
    
    },

    paypalButtonHandler: function(actions){
      actions.enable();
    }
  }
  return Paypal; 
})(this, this.document, PAYPAL_SDK);

PAYPAL_SDK.methods.paypalButtonRender()
ACCORDIAN.methods.activateAccordion(".pay_online")