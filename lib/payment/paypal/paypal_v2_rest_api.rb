module Payment
  module Paypal
    class PaypalV2RestApi
      require 'net/http'
      require 'uri'
      def initialize(order)
        @order = order
        @paypal_smart_pay_urls = MirrawMobile::Application.config.paypal_smart_pay
      end
    
      def get_authentication_token()
        Rails.cache.fetch('paypal_auth_token', expires_in: 7.hour){
          uri = URI.parse(@paypal_smart_pay_urls[:auth_token_url])
          request = Net::HTTP::Post.new(uri)
          request.basic_auth("#{ENV['PAYPAL_CLIENT_ID']}", "#{ENV['PAYPAL_CLIENT_SECRET']}")
          request['Accept'] = "application/json"
          request["Accept-Language"] = "en_US"
          request.body = 'grant_type=client_credentials'
          req_options = {
            use_ssl: uri.scheme == "https",
          }
          response = Net::HTTP.start(uri.hostname, uri.port, req_options) do |http|
            http.request(request)
          end
          response_hash = handle_checkout_v2_errors(response)
          if response_hash.key?("has_error")
            nil
          else
            response_hash["access_token"]
          end
        }
      end
    
    
      def create_payment(response_payload, autho_token)
          uri = URI.parse(@paypal_smart_pay_urls[:create_payment_url])
          request = Net::HTTP::Post.new(uri)
          request['Content-Type'] = "application/json"
          request["Authorization"] = "Bearer #{autho_token}"
          request.body = response_payload.to_json
          req_options = {
            use_ssl: uri.scheme == "https",
          }
          response = Net::HTTP.start(uri.hostname, uri.port, req_options) do |http|
            http.request(request)
          end
          response_hash = handle_checkout_v2_errors(response)
      end

      def capture_payment
        auth_token = get_authentication_token
        execute_payment_url = @paypal_smart_pay_urls[:create_payment_url] + "/#{@order.token}" + "/capture"
        uri = URI.parse(execute_payment_url)
        request = Net::HTTP::Post.new(uri)
        request['Content-Type'] = "application/json"
        request["Authorization"] = "Bearer #{auth_token}"
        req_options = {
          use_ssl: uri.scheme == "https",
        }
        response = Net::HTTP.start(uri.hostname, uri.port, req_options) do |http|
          http.request(request)
        end
        response_hash = handle_checkout_v2_errors(response)
      end
    
      def handle_checkout_v2_errors(response)
        response_hash = JSON.parse(response.body)
        if !["200", "201"].include?(response.code)
          error_message = ''
          if response_hash.key?("error")
            error_message = "Invalid Client Credentials"
            Order.sidekiq_delay.notify_exceptions("Paypal Express Checkout V2 Identity Error", error_message, {response: response_hash, order_number: @order.number})
          else 
            error_message = response_hash['details'].collect(&:values).collect{|i| i.join(' ').humanize}.join(', ')
            Order.sidekiq_delay.notify_exceptions("Paypal Express Checkout Mobile V2 Error-", error_message, {response: response_hash, order_number: @order.number})
            new_details = [@order.payment_gateway_details, {response: response_hash}.to_json].compact.join("\n")
            @order.payment_gateway_details = new_details
            @order.paypal_error = response_hash['details'].to_json
          end  
          return response_hash.merge({"has_error" => true, "errors" => error_message})
        end
        return response_hash
      end
    end
  end
end