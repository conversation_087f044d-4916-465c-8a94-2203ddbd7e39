module Payment
  class PaymentDetails
    attr_accessor :order
    
    def initialize(order, country_code, country_symbol)
      self.order = order
      @country_code = country_code
      currency = CurrencyConvert.find_by_country_code(@country_code || 'US')
      @rate = (currency.rate || order.currency_rate)
      @subscription_plan = order.get_subscription_plan
      @country_symbol = country_symbol
    end
    
    def get_shipping_cost()
      shipping_cost = CurrencyConvert.to_currency(@country_code, order.shipping ).round(2)
    end
  
    def get_discount_price
      order.total_discount_currency(@rate).round(2)
    end

    def get_tax_amount
      order.total_tax.present?  ? CurrencyConvert.to_currency(@country_code, order.total_tax).round(2) : 0
    end

    def user_names
      @user_names ||= begin 
        complete_name = order.billing_name.split(' ')
        firstname = complete_name.first
        middlename = complete_name.second
        lastname = complete_name.last
        fullname = (middlename == lastname) ? firstname : [firstname, middlename].join(" ")
        [firstname, middlename, lastname, fullname]
      end
    end
  
    def set_regex
      @non_word_excluding_space_regex ||= Regexp.new(/[^\w\s]/)
    end
  
    def get_multi_line_address(address)
      non_word_excluding_space_regex = set_regex
      @multi_addr ||= order.multi_line_address(100,address.gsub(non_word_excluding_space_regex,''))
    end
  
    def get_email
      order.billing_email
    end

    def order_reference
      order.number
    end  
    
    def get_H_PhoneNumber
      order.billing_phone.gsub(/\D/,'')[0..49]
    end
  
    def get_first_name
      all_names = user_names
      non_word_excluding_space_regex = set_regex
      all_names[3].gsub(non_word_excluding_space_regex,'')[0..59]
    end
  
    def get_last_name
      all_names = user_names
      non_word_excluding_space_regex = set_regex
      all_names[2].gsub(non_word_excluding_space_regex,'')[0..59]
    end
  
    def get_zip
      non_word_excluding_space_regex = set_regex
      order.billing_pincode.gsub(non_word_excluding_space_regex,'')[0..49]
    end
  
    def get_city
      non_word_excluding_space_regex = set_regex
      order.billing_city.gsub(non_word_excluding_space_regex,'')[0..49]
    end
  
    def get_address1
      get_multi_line_address(order.billing_street)[0]
    end
  
    def get_address2
      get_multi_line_address(order.billing_street)[1]
    end
  
    def get_state_code
      country = order.billing_country
      country_obj = Country.find_by_namei_cached(country)
      code = country_obj.try(:[], :iso3166_alpha2)
      case code
      when 'BR','CA','IT','MX', 'US'
        if (states = country_obj.try(:[], :states)).present?
          states.find{|k,v| k.downcase == order.buyer_state.downcase}.try(:last)
        end
      when 'AR'
        order.buyer_state.to_s.mb_chars.upcase.to_s
      end || order.buyer_state
    end
  
    def get_shipping_recipient_name
      order.name
    end

    def billing_country
      order.billing_country
    end

    def reference_id
      order.id
    end

    def order_country
      order.country
    end
  
    def get_shipping_line1
      get_multi_line_address(order.street)[0]
    end
  
    def get_shipping_line2
      get_multi_line_address(order.street)[1]
    end
  
    def get_shipping_city
      non_word_excluding_space_regex = set_regex
      order.city.gsub(non_word_excluding_space_regex,'')[0..49]
    end
  
    def get_shipping_phone
      order.phone.gsub(/\D/,'')[0..49] 
    end
  
    def get_shipping_postal_code
      non_word_excluding_space_regex = set_regex
      order.pincode.gsub(non_word_excluding_space_regex,'')[0..49] 
    end

    def get_order_item_details
      line_items = order.designer_orders.collect(&:line_items).flatten
      item_hash = {}
      item_hash['items_total'] = 0
      item_hash['items'] = []
      line_items.each_with_index do |item, index|
        item_price = item.total_currency(@rate).round(2)
        item_hash['items_total'] += item_price
        item_sub_hash = {}
        item_sub_hash["quantity"] = "#{item.quantity}"
        item_sub_hash["name"] = item.design.title
        single_item_price = (item_price/item.quantity).round(2)
        item_sub_hash["unit_amount"] = {
          "value": "#{single_item_price}",
          "currency_code": @country_symbol
         }
        item_sub_hash["sku"] = item.design.design_code
        item_sub_hash["category"] = "PHYSICAL_GOODS"
        item_hash['items'] << item_sub_hash
      end
      order_addon = order.order_addon
      if order_addon.try(:gift_wrap_price)
        item_price =  CurrencyConvert.to_currency(@country_code, order_addon.gift_wrap_price).round(2)
        item_hash['items_total'] += item_price
        item_sub_hash = {}
        item_sub_hash["quantity"]= "1"
        item_sub_hash["name"]= "Gift Wrap charges"
           item_sub_hash["unit_amount"] = {
          "value": "#{item_price}",
          "currency_code": @country_symbol
         }
        item_sub_hash["sku"] = ""
        item_hash['items'] << item_sub_hash
      end

      # paypal Entry For subscription Fee
      if order.subscription_fee.present? && order.other_details['subscription_fee_applied'] == 'true'
        sub_title = @subscription_plan && @subscription_plan[:sub_title].present? ? @subscription_plan[:sub_title] : "Membership Fee"
        item_price =  CurrencyConvert.to_currency(@country_code, order.subscription_fee).round(2)
        item_hash['items_total'] += item_price
        item_sub_hash = {}
        item_sub_hash["quantity"]= "1"
        item_sub_hash["name"]= sub_title
           item_sub_hash["unit_amount"] = {
          "value": "#{item_price}",
          "currency_code": @country_symbol
         }
        item_sub_hash["sku"] = ""
        item_hash['items'] << item_sub_hash
      end

      # paypal Entry For Platform Fee
      if order.platform_fee.present?
        item_price =  CurrencyConvert.to_currency(@country_code, order.platform_fee).round(2)
        item_hash['items_total'] += item_price
        item_sub_hash = {}
        item_sub_hash["quantity"]= "1"
        item_sub_hash["name"]= "Platform Fee"
           item_sub_hash["unit_amount"] = {
          "value": "#{item_price}",
          "currency_code": @country_symbol
         }
        item_sub_hash["sku"] = ""
        item_hash['items'] << item_sub_hash
      end

      return [item_hash['items_total'].round(2), item_hash['items']] 
    end

    def get_total_amount_to_pay
      item_total, item_details = get_order_item_details
      discount_price = get_discount_price
      tax_amount = get_tax_amount
      shipping_discount = discount_price if discount_price
      shipping = get_shipping_cost
      grand_total = (item_total + shipping - shipping_discount.to_f).round(2)
      # total_with_taxes  =   (grand_total + tax_amount).round(2)
      grand_total = (grand_total + tax_amount).round(2) 
    end
  end
  
end