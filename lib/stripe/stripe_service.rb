require 'stripe'
module Stripe
  class StripeService
    attr_accessor :order

    def initialize(order)
      self.order = order 
    end
    
    def create_intent
      begin
        customer = get_customer  
        ephemeral_key = create_ephemeral_key(customer.id)
        intent = create_stripe_transaction(customer.id)
        return intent.to_hash.merge({ephemeral_secret: ephemeral_key.secret, customer_id: customer.id})
      rescue => e
        {error: e.message}
      end
    end
    
    def get_customer
      if (cus = order.user.payment_gateway_customers.stripe_customer.first).present?
        cu_id = cus.customer_id
        customer = Stripe::Customer.retrieve(cu_id)
      else
       customer =  Stripe::Customer.create(
          name: order.name,
          email: order.email,
          address: {
              line1: order.street,
              city: order.city,
              state: order.buyer_state,
              country: order.country_code,
              postal_code: order.pincode,
            }
        )
        PaymentGatewayCustomer.create({payment_gateway_type: 'stripe', customer_id: customer.id, user_id: order.user.id})
      end
      return customer
    end

    def create_stripe_transaction(customer_id)
      params = get_intent_parameters
      intent = {}
      if (st = order.payment_gateway_transaction).present? && (st.gateway_type == 'stripe')
        if(st.gateway_type == 'stripe')
          intent = Stripe::PaymentIntent.retrieve(st.transaction_id)
        else
          intent = Stripe::PaymentIntent.create(params.merge({customer: customer_id}))
          st.update_attributes({gateway_type: 'stripe', transaction_id: intent.id})
        end
      else
        intent = Stripe::PaymentIntent.create(params.merge({customer: customer_id}))
        order.create_payment_gateway_transaction({transaction_id: intent.id, customer_id: customer_id, gateway_type: 'stripe'}) if intent.id.present?
      end
      return intent
    end

    def get_intent_parameters
      hash = {
        description: "Mirraw Online - #{order.number}",
        shipping: {
          name: order.name,
          address: {
            line1: order.street,
            postal_code: order.pincode,
            city: order.city,
            state: order.buyer_state,
            country: order.country_code,
          }
        },
        receipt_email: order.email,
        amount: calculate_amount,
        currency: order.currency_code.downcase,
      }
    end

    def create_ephemeral_key(cus_id)
      key = Stripe::EphemeralKey.create(
        { customer: cus_id },
        stripe_version: Stripe.api_version
      )
    end

    def calculate_amount
      payment_details = Payment::PaymentDetails.new(order, order.country_code, order.currency_code)
      total = payment_details.get_total_amount_to_pay
      if order.currency_code.downcase == 'jpy'
        (total).to_i
      elsif order.currency_code.downcase == 'aed'
        ((order.total / order.currency_rate) * 100).to_i
      else
        ((total) * 100).to_i
      end
    end
  
  end
end
