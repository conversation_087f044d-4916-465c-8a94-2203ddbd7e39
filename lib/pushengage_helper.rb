class PushengageHelper
  include HTTParty

  base_uri 'https://api.pushengage.com/apiv1/'

  class << self

    attr_accessor :debug

    def get_segments(query={})
      response = get('/segments', query: query, **__other_options)
      response.success? ? response.parsed_response : {}
    end

    def send_notifications(options={})
      raise ArgumentError unless options[:notification_title] && options[:notification_message] && options[:notification_url]
      response = post('/notifications', body: options, **__other_options)
      response.parsed_response
    end

    def is_subscribed?(subscriber_hash)
      return false unless subscriber_hash.present?
      response = post('/subscribers/check', body: {subscriber_hash: subscriber_hash}, **__other_options)
      response.parsed_response.try{|response| response["success"] == true}
    end

    def notification_details(notification_id)
      return {} unless notification_id.present?
      response = get('/notifications', query: {notification_id: notification_id}, **__other_options)
      response.parsed_response
    end

    private

    def __default_headers
      @default_headers ||= {
        'api_key' => ENV['PUSHENGAGE_API_KEY'],
        'Content-Type' => "application/x-www-form-urlencoded"
      }
    end

    def __other_options
      options = {headers: __default_headers}
      options[:debug_output] = $stdout if debug
      options
    end

  end

end