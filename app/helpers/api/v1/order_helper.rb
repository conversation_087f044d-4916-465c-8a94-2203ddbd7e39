module Api::V1::Order<PERSON>elper 
  def paytype_response_methods(paytype, billing_country = nil, order_total, is_stripe)
    params = [:id, :number]
    params << :order_details
    return params if order_total.to_i <= 0
    return params if is_stripe
    case paytype
    when Order::PAYMENT_GATEWAY, Order::PAYU_MONEY
      if billing_country.present? && billing_country.downcase != 'india'
        params << :paypal_mobile_cc_create_params
      else
        params << :payu_mobile_create_params
      end
    when Order::PAYPAL_V2
      params << :paypal_capture_order_params
    when Order::PAYPAL
      params << :paypal_mobile_create_params
    when Order::PAYTM
      params << :paytm_sdk_create_params
    end
    params
  end
end
