module <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
    
    def get_symbol_from(hex_code)
        codes = hex_code.split(', ')
        symbol = ''
        codes.each do |code|
          symbol += [code.hex].pack('U')
        end
        symbol
    end
  
    def get_price_with_symbol(amount, currency_code)
        symbol = get_symbol_from(currency_code)
        "#{symbol}#{amount.round(2)}"
    end
    
    def get_price_in_currency(price, rate = nil)
        conversion_rate = rate || instance_variable_get(:@rate) || 1
        price_in_currency = (price.to_f/conversion_rate).round(2)
        return price_in_currency
    end
end