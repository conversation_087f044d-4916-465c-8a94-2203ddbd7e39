- content_for :page_specific_css do
  = stylesheet_link_tag 'designer'

%div.container
  %div.designer-page
    .designer-page-heading
      %h1.text-center DESIGNERS
    - @all_designers.each do |initial,designers|
      .designers-name-list-wrapper{id: "alphabet_#{initial}"}
        .designers-name
          %span.main-letter= initial
          .designer-name-list
            .name-list-box
              %ul.name-list
                -designers[:names].each_with_index do |name,index|
                  %li.list-item
                    = link_to name.titleize, designers_path(designers[:cached_slugs][index])

  %div.alphabet-list-wrapper
    %ul.alphabet-list
      - @all_designers.each do |initial,designer|
        %li.list-item
          %span.alphabet
            = link_to initial, "#alphabet_#{initial}", class: "alphabet-link"

  
:javascript
    $(document).ready(function() {
    MR.designer.setActiveEvent();
    });

- content_for :page_specific_js do
  = javascript_include_tag 'designer'
