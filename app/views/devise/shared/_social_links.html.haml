- if controller_name.in? %w(sessions registrations)
  .row
    .small-12.small-centered.columns.sign-in-links
      = link_to account_omniauth_authorize_path(:google_oauth2, state: { action: action }.to_json),class: 'sign-in-link' do
        = image_tag 'google_brand_sign_in_button.png'
      = link_to account_omniauth_authorize_path(:facebook, state: { action: action }.to_json),class: 'sign-in-link' do
        = image_tag 'facebook_brand_sign_in_button.png'