- content_for :page_specific_css do
  = stylesheet_link_tag 'sessions'

.row
  .large-6.medium-8.small-12.columns.small-centered.bordered_block{style: 'padding-bottom: 0px;'}
    %h4 Forgot your password?
    .row.bordered_block
      = form_for(resource, as: resource_name, url: password_path(resource_name), html: { method: :post }) do |f|
        - placeholder_text = @country_code == 'IN' ? "Email or Phone" : "Email"
        = f.text_field :login, autofocus: true, label: false, placeholder: placeholder_text, required: true
        = f.submit 'SEND RESET PASSWORD LINK', class: 'expand reset-password button'
    = render "devise/shared/links" 