- content_for :page_specific_css do
  = stylesheet_link_tag 'sessions', 'addresses'
- content_for :page_specific_js do
  //= javascript_include_tag 'guest_login'
  = javascript_include_tag 'addresses'

.error_message_box.alert-box.alert{style: 'text-align: center;display: none;'}
= render partial: 'addresses/address_collect_form_desktop'

- if false # ajax login + address form not required now, because it is now merge into one page
  .email_field_div
    .row
      .large-12.medium-12.small-12.columns.small-centered.guest_login_form
        %h4.text-center
          %u Enter Your Email
        .checkout_login
          = form_tag(accounts_create_guest_path, {id: 'guest_login_form', method: 'POST'}) do
            .row
              .small-12.columns
                = text_field_tag 'email', '', placeholder: '<EMAIL>', type: 'email', required: true,
                  pattern: '^((?:[-a-zA-Z0-9_\.]+)+[a-zA-Z0-9]*)@((?:[-a-zA-Z0-9]+\.)+[a-zA-Z]{2,})$'
            .row
              .small-12.columns
                = submit_tag 'Continue As Guest', class: 'button postfix secondary order_flow_submit_button'



        = link_to 'I am an existing user?', 'javascript:void(0)', class: :toggle_loggin_forms
      .large-12.medium-12.small-12.columns.small-centered.hide.existing_user_login_form
        - session["account_return_to"] = params[:account_return_to]
        = form_for(resource, as: resource_name, url: session_path(resource_name)) do |f|
          = f.email_field :email, autofocus: true, label: false, placeholder: "Email", value: params[:email]
          = f.password_field :password, autocomplete: "off", label: false, placeholder: "Password"
          - session["account_return_to"] = new_order_path
          = f.submit "Log in", class: 'button postfix secondary order_flow_submit_button'
        %br
        = link_to 'Continue As Guest...', 'javascript:void(0)', class: :toggle_loggin_forms
      %hr
    = render "devise/shared/social_links"


  - unless check_for_alternate_tab?
    .address_field_div
      .text-center Delivery Details
      .billing_address.large-6.medium-8.small-12.columns.small-centered.bordered_block
        = render partial: 'addresses/address_collect_form_desktop'
-if @cart.present?
  - pro_totals, max_total, shipping_currency = get_cart_total_information(@country_code, @rate)
  - coupon_discount_amount = pro_totals.find { |item| item[:title] == "Coupon Discounts" }&.dig(:amount) || 0
  - tax_amount = pro_totals.find { |item| item[:title] == "Tax" }&.dig(:amount) || 0
  - total_amount = pro_totals.find { |item| item[:title] == "Item Total" }&.dig(:amount) || 0
  - gift_wrap = session[:gift_wrap] && @country_code != 'IN' ? get_price_in_currency(GIFT_WRAP_PRICE.to_f) : 0
  - market_rate = CurrencyConvert.countries_marketrate[@country_code]
  - gift_wrap = (gift_wrap *= (market_rate).round(CurrencyConvert.round_to)).round(CurrencyConvert.round_to)
  - tax_amount = (tax_amount *= (market_rate).round(CurrencyConvert.round_to)).round(CurrencyConvert.round_to)
  - shipping_currency = (shipping_currency *= (market_rate).round(CurrencyConvert.round_to)).round(CurrencyConvert.round_to)
  - coupon_discount_amount = (coupon_discount_amount *= (market_rate).round(CurrencyConvert.round_to)).round(CurrencyConvert.round_to)
  -if coupon_discount_amount != 0
    -discount_percent = (coupon_discount_amount*100.0/@totalvalue)
  -else
    -discount_percent = 0
  
  :javascript
    window.dataLayer = window.dataLayer || [];
    $(document).on('click', '#address_collect_submit', function(e)  {
      if (isFormFilled()) {
        var total =  (#{@totalvalue} + #{gift_wrap}) - #{coupon_discount_amount}
        var ga4_shipping_params = #{@ga_hash_new.to_json};
        var customizationTotal = 0;
        ga4_shipping_params.items.forEach(function (item) {
          customizationTotal += item.item_customization * item.quantity;
        });
        ga4_shipping_params.customization = customizationTotal;
        ga4_shipping_params.value = total + customizationTotal;
        ga4_shipping_params.gift_wrap = #{gift_wrap}
        ga4_shipping_params.shipping = #{shipping_currency}
        ga4_shipping_params.coupon_discount = #{coupon_discount_amount}
        ga4_shipping_params.tax = #{tax_amount}
        dataLayer.push({ ecommerce: null });
        dataLayer.push({
          event: "ga4_add_shipping_info",
          ecommerce: ga4_shipping_params
        });
      }
    });
    function isFormFilled() {
      var form = $('#new_address');
      var requiredFields = form.find('input[required], textarea[required]');
      for (var i = 0; i < requiredFields.length; i++) {
        if (!requiredFields[i].value.trim()) {
          return false;
        }
      }
      return true;
    }