.review_table{class: "review_table_#{i}"}
  .image-container
    = image_tag("https://#{@image_array[i]}")
  .product-title
    -design = Design.where(id: @design_ids[i]).first
    %span{style: "color: black; font-weight: 400"}
      =design.title
  .review-question-container
    %div{style: "vertical-align: middle;"}
      =render partial: 'question_group' , locals: {i: i,issue_type: 'product', association_type: 'line_item', association_id: @line_item_ids[@design_ids[i]], questions: get_product_questions, next_classes: '.review_text', hide_class: '', style: nil}

    %div{style: "vertical-align: middle;"}
      #review_modal{id: "#{i}"}
        .review_text{class: "review_text_#{i}", style: "display:none;"}
          .modal-header
          .modal-body
            #review_issue.review_issue{id: "#{i}"}
              Please let us know the reason of your rating.
              .issues
                %ul.accordion{"data-accordion" => ""}
                  -SurveysHelper::QUESTION_METHODS.each do |bifurcation|
                    -unless (questions= send("get_#{bifurcation}_questions")).empty?
                      -if (bifurcation.titlecase == 'Stitching' && check_stitching_items(@order_id).include?((design.id))) || bifurcation.titlecase != 'Stitching' || (params[:sample_form] == 'true' && @line_item_type[i] == "stitched")
                        %li.accordion-navigation
                          %a{:href => "##{bifurcation}_#{i}"}= bifurcation.titlecase
                          .content{id: "#{bifurcation}_#{i}"}
                            -questions.each do |questionaire|
                              %ul.sub-text
                                -questionaire.child_questions.prioritized.active.group_by(&:priority).each do |priority,questions|
                                  =render partial: 'question_group' , locals: {i: i,issue_type: questionaire.qualify_as, association_type: 'line_item', association_id: @line_item_ids[@design_ids[i]],questions: questions,  next_classes: '', style: 'display:none;', hide_class: ''}
                                -#-questionaire.child_questions.each do |survey|
                                  -#%li.survey-questions-answers
                                    -#=survey.question
                                  -#%li.survey-questions-answers
                                    -#=radio_button_tag "#{survey.id}_#{i}", "yes", 0, class: "sub_issue_#{bifurcation}", data: {issue_type: bifurcation.titlecase}
                                    -#%label{for: "#{survey.id}_#{i}_yes"} Yes
                                  -#%li.survey-questions-answers
                                    -#=radio_button_tag "#{survey.id}_#{i}", "no", 0,  class: "sub_issue_#{bifurcation}", data: {issue_type: bifurcation.titlecase}
                                    -#%label{for: "#{survey.id}_#{i}_no"} No
            .review_text_box{style: 'display:none'}
              = text_area_tag "customer_review_#{i}", nil , placeholder: 'Share your thoughts about the product...', class: "customer_review_design_#{i} review-box", style: "color:black; height:100px;"
              .submit-cancel-container
                %span.button.submit_review_text_done{data: {row_number_text: "#{i}", product_id: @line_item_ids[@design_ids[i]], order_id: @order_id}}Submit
                %span.button.submit_review_text_cancel{data: {row_number_text: "#{i}", product_id: @line_item_ids[@design_ids[i]], order_id: @order_id}} Cancel
