.nps_questions{data: {condition: question.survey_terminating_condition }}
  .questions.col-lg-6
    =question.question
  .answer
    %div.ratings
    -5.times do |x|
      .rating-tab-design{class: "rating-tab-design-#{i}"}
        .grey-stars-design{name: name, value: x+1 ,data:{star_value_design: "#{x}", row_number: "#{i}", design_id: @design_ids.try(:[],i), order_id: @order_id, terminator:  x}, id: "star_#{x}_#{i}", class: "grey-stars-#{i}"}
        .number_star-design= "#{x + 1}"
        -if hint = x==0 ? 'Bad' : x==4 ? 'Good' : nil
          %p.star_hint=hint
