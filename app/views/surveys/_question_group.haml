.question_group{data: {next_classes: next_classes, hide_class: hide_class}, style: style}
  -questions.each do |question|
    -locals = {i: i,name: "#{question.id}_#{i}", question: question}
    -partial = question.question_type ? question.question_type + '_answer' : 'boolean_answer'
    =render partial: partial, locals: locals
  .submit_question_group{style: 'display:none;'}
    .submit{data: {association_id: association_id, association_type: association_type, issue_type: issue_type}}
      Submit
