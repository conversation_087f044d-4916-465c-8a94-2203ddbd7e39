= stylesheet_link_tag 'surveys'
= javascript_include_tag 'surveys'
:javascript
  var trackOutboundLink = function(url) {
  //ga('send', 'event', 'outbound', 'click', url, {
  //  'transport': 'beacon',
  //  'hitCallback': function(){document.location = url;}
  //});
  }

.survey
  - if @survey.present?
    = form_for @survey do |f|
      = f.hidden_field :email, value: @data[:email]
      = f.hidden_field :order_number, value: @data[:order_number]
      = f.hidden_field :token, value: params[:token]
      = f.hidden_field :star_value, value: 5
      - if @survey.errors.any?
        #error_explanation
          %h2= "#{pluralize(@survey.errors.count, "error")} prohibited this survey from being saved:"
          %ul
            - @survey.errors.full_messages.each do |msg|
              %li= msg
      #survey_alert.alert-box.info.radius{style: "display:none; margin: 0px 8px 10px 8px;"}
      .rating-div
        .rating-title
          How likely are you to recommend <PERSON><PERSON> to a friend or colleague?
        .rating-content
          .rating-description
            %div{style: "text-align:left;"}
              %div
                0 - Not at all likely
            %div{style: "text-align:right;"} 10 - Extremely likely
          .slider-container
            .emoji-container
              .angry-emoji
                😡
              .satisfied-emoji
                😃
            -# TODO: Add a range slider for rating
            -# %input#rating-slider{type: "range", min: "0", max: "10", value: "5", step: "1"}
            .rating-btns-container
              -11.times do |i|
                .number-btns
                  %div.grey-stars{data:{star_value: "#{i}"}, id: "star_#{i}"}
                  .number_star{id: "start_#{i}"}= "#{i}"
        .rating-tab
          #next_nps_bifurcation
            .next-text Next
      %div.text-message.hide
        .row.response-question
          #done-box.small-12.columns Your reviews are incredibly helpful to us. Thank You for Shopping! 😊
        .row.last_question
          #last-box.small-12.columns{style: 'display:none;text-align:-webkit-center;'}
            Share your review by
            =link_to 'Clicking Here','https://www.trustpilot.com/evaluate/mirraw.com',onclick: "trackOutboundLink('https://www.trustpilot.com/evaluate/mirraw.com')"
        #survey_questionaires.row.lead
          =render partial: '/surveys/survey_questions'
        #review_designs.row.response-question{style: 'display:none;'}
          .review-the-products.small-12 Please Review the Products you purchased!
          #nps_design_ratings{data: {order_id: @order_id}}
  - else
    #error_explanation
      %h2= "Survey has not been created"
.survey-message
