- content_for :page_specific_css do
  = stylesheet_link_tag 'awesomplete','pages'
- content_for :page_specific_js do
  = javascript_include_tag 'awesomplete','pages'
- if params[:api_layout] == 'true'
  :scss
    section.main-section{
      div#main-section{
        #container{
          margin-top: 0px;
        }
      }
    }
-if @seo.present?
  - title @seo.title
  - description @seo.description.to_s
  - keywords @seo.keyword.to_s
-help_center_url = params[:api_layout] == 'true' ? 'help_center?api_layout=true' : pages_help_center_path
= cache("faq_main_#{@region_wise}" , expires_in: 24.hours) do
  -questions = {}
  -@quick_links.collect{|hash| questions[hash.question] = hash.answer}
  = content_tag 'div', '', id: 'questions_hash', data: {attr_id: questions}
  .faq_main_div
    .row.faq_head
      %h1 Mirraw FAQs
    .row
      =text_field_tag :search_list,nil,placeholder: 'How can I help you?',class: 'awesomplete'
      #search_ques
      #search_answer
    %br
    -@quick_links = @quick_links.group_by(&:category_of_faq)
    -@quick_links.keys.each_slice(4).to_a.each_with_index do |quick_link_arr,arr_index|
      .row.quick_links
        -quick_link_arr.each_with_index do |quick_link, o_index|
          .columns.small-3.quick_link.left{data: {attr_id: "#{arr_index}_#{o_index}"} }
            %br
            =image_tag(ORDERING_OF_FAQ[quick_link][1]+'.png', class: "step_image", width: "36", height: "36")
            %br
            %h2=quick_link.split('& ').join("<br> & ").html_safe
      .row.faqs_row
        -quick_link_arr.each_with_index do |quick_link,o_index|
          .columns.small-12.faqs_all.navigation{id: "priority_questions-#{arr_index}_#{o_index}",style: 'display:none;overflow-y:scroll;'}
            - faqs = @quick_links[quick_link].sort_by!(&:priority)
            %ul.questions_ul
              -faqs.each do |faq|
                .faqs_qas{style: "display: #{display}"}
                  %li.faq_question{class: 'question_li_content', data: {attr_id: faq.id}}
                    %h3= faq.question
                  .faq_answer{id: "faq_answer-#{faq.id}",style: "display: none"}
                    =faq.answer.html_safe
                  %hr
    .row
      %a.button{href: '#', "data-reveal-id" => 'trackModal', style: 'width:100%;margin-top: 10px;background: #670e19'} Track Your Order
      #trackModal.reveal-modal{"data-options" => "closeOnBackgroundClick:true", "aria-hidden" => "true", "aria-labelledby" => "modalTitle", "data-reveal" => "", role: "dialog"}
        %a.close-reveal-modal{"aria-label" => "Close", href: "#", role: "button"} &#215
        Track Your Order
        %br
        %br
        = form_tag nil,id: 'order_track_form', class:'form',method: :patch,target: '_blank' do
          = text_field_tag 'order_number_field',nil,placeholder: 'Enter Order Number',class: 'form form-control small-12 columns',required: true,maxlength: 10
          = submit_tag 'Track',class: 'form-control button success',style: 'width: 100%;padding: 0.7em;'
      %h5 Still need help?
      =link_to 'Contact Us', help_center_url, class: 'button info',style: "width: 100%;margin-top: 5px;background: #d3b9be;", data: {turbolinks: 'false'}
