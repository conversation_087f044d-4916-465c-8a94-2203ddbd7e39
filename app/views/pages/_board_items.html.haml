- boards['homepage_widgets'].each do |blocks|
  - case blocks['type']
  - when 'static_banner'
    .board-item.banner-slider
      - blocks['board_items'].each do |banner|
        - ga_data = {event: "banner_click",bannerDetails: {banner_id: banner["id"],banner_name: banner["name"].titleize,banner_destination_url: banner["link"],banner_category: "Home",banner_placement: 'static_banner'.titleize}}.to_json
        = link_to banner['link'],onclick:"window.dataLayer = window.dataLayer || [];dataLayer.push({ bannerDetails: null }); dataLayer.push(#{ga_data})" do
          = picture_tag(banner['photo']['sizes'], content_type: banner['content_type'], p_style: :main, alt: "#{banner['name']} #{blocks['type']} #{blocks['id']}", type: banner['type'], style: "width: 100%")
      - if blocks['promotion_offer_end_time'].present?
        - end_date = blocks['promotion_offer_end_time']
        - board_link = blocks['link']
        .banner-timer
          = render partial: 'designs/countdown_timer', locals: {end_date: end_date, link: board_link}, cached: true

  - when 'auto_scrollable_banner'
    .board-item
      - if !blocks['name'].empty?
        .homepage-blocks
          .row.block-headers
            -if blocks['display_items_name']
              = blocks['name']
      .banner-slider-box.blaze-slider.banner-blaze-container{ class: blocks['type'] }
        .blaze-container
          .blaze-track
            - blocks['board_items'].each_with_index do |banner_slider, index|
              - lazy_load = index.eql?(0) ? false : true
              - ga_data = {event: "banner_click", bannerDetails: {banner_id: banner_slider["id"], banner_name: banner_slider["name"].titleize, banner_destination_url: banner_slider["link"], banner_category: "Home", banner_placement: 'auto_scrollable_banner'.titleize}}.to_json
              .blaze-item.banner-wrapper
                .banner-slide{ class: blocks['type']+"_slide" }
                  - if banner_slider['name'] == "Mobile_app6"
                    - if browser.platform.ios?
                      = link_to "https://itunes.apple.com/app/apple-store/id1112569519?pt=118123691&ct=Mobile_Banner_HomePage&mt=8", target: "_blank", rel: "noopener", onclick: "window.dataLayer = window.dataLayer || []; dataLayer.push({ bannerDetails: null }); dataLayer.push(#{ga_data})" do
                        - if lazy_load
                          = picture_tag(banner_slider['photo']['sizes'], content_type: banner_slider['content_type'], p_style: :main, alt: "#{banner_slider['name']} #{blocks['type']} #{blocks['id']}", lazy:{})
                        - else
                          = picture_tag(banner_slider['photo']['sizes'], content_type: banner_slider['content_type'], p_style: :main, alt: "#{banner_slider['name']} #{blocks['type']} #{blocks['id']}")
                    - else
                      = link_to "https://play.google.com/store/apps/details?id=com.mirraw.android&utm_source=homepagebanner&utm_medium=mirraw_mobile_web", target: "_blank", rel: "noopener", onclick: "window.dataLayer = window.dataLayer || []; dataLayer.push({ bannerDetails: null }); dataLayer.push(#{ga_data})" do
                        - if lazy_load
                          = picture_tag(banner_slider['photo']['sizes'], content_type: banner_slider['content_type'], p_style: :main, alt: "#{banner_slider['name']} #{blocks['type']} #{blocks['id']}", lazy:{})
                        - else
                          = picture_tag(banner_slider['photo']['sizes'], content_type: banner_slider['content_type'], p_style: :main, alt: "#{banner_slider['name']} #{blocks['type']} #{blocks['id']}")
                  - else
                    = link_to banner_slider['link'], onclick: "window.dataLayer = window.dataLayer || []; dataLayer.push({ bannerDetails: null }); dataLayer.push(#{ga_data})" do
                      - if lazy_load
                        = picture_tag(banner_slider['photo']['sizes'], content_type: banner_slider['content_type'], p_style: :main, alt: "#{banner_slider['name']} #{blocks['type']} #{blocks['id']}", lazy:{})
                      - else
                        = picture_tag(banner_slider['photo']['sizes'], content_type: banner_slider['content_type'], p_style: :main, alt: "#{banner_slider['name']} #{blocks['type']} #{blocks['id']}")
        .controls
          .blaze-pagination
  - when 'user_scrollable_banner'
    .board-item{class: blocks['type']}
      - if !blocks['name'].empty?
        .homepage-blocks
          .row.block-headers
            -if blocks['display_items_name']
              = blocks['name']
      .banner-slider-box
        - blocks['board_items'].each_with_index do |banner_slider, index|
          .banner-wrapper
            - ga_data = {event: "banner_click",bannerDetails: {banner_id: banner_slider["id"],banner_name: banner_slider["name"].titleize,banner_destination_url: banner_slider["link"],banner_category: "Home",banner_placement: 'user_scrollable_banner'.titleize}}.to_json
            .banner-slide{class: blocks['type']+"_slide"}
              - if banner_slider['name'] == "Mobile_app6"
                - if browser.platform.ios?
                  = link_to "https://itunes.apple.com/app/apple-store/id1112569519?pt=118123691&ct=Mobile_Banner_HomePage&mt=8", target: "_blank", rel: "noopener", onclick:"window.dataLayer = window.dataLayer || [];dataLayer.push({ bannerDetails: null }); dataLayer.push(#{ga_data})"do
                    = picture_tag(banner_slider['photo']['sizes'], content_type: banner_slider['content_type'], p_style: :main, alt: "#{banner_slider['name']} #{blocks['type']} #{blocks['id']}", lazy: {}, style: "width: 100%")
                - else
                  = link_to "https://play.google.com/store/apps/details?id=com.mirraw.android&utm_source=homepagebanner&utm_medium=mirraw_mobile_web", target: "_blank", rel: "noopener" , onclick:"window.dataLayer = window.dataLayer || [];dataLayer.push({ bannerDetails: null }); dataLayer.push(#{ga_data})" do
                    = picture_tag(banner_slider['photo']['sizes'], content_type: banner_slider['content_type'], p_style: :main, alt: "#{banner_slider['name']} #{blocks['type']} #{blocks['id']}", lazy: {}, style: "width: 100%")
              - else
                = link_to banner_slider['link'], onclick:"window.dataLayer = window.dataLayer || [];dataLayer.push({ bannerDetails: null }); dataLayer.push(#{ga_data})"  do
                  = picture_tag(banner_slider['photo']['sizes'], content_type: banner_slider['content_type'], p_style: "main", alt: "#{banner_slider['name']} #{blocks['type']} #{blocks['id']}", lazy: {}, style: "width: 100%")
  - when 'frontpage'
    .board-item
      - if !blocks['name'].empty?
        .homepage-blocks
          .row.block-headers
            -if blocks['display_items_name']
              = blocks['name']
      .homeblock_elements    
        - blocks['board_items'].each_slice(2) do |fp_blocks|
          .front_home_page
            - fp_blocks.each do |block|
              - ga_data = {event: "banner_click",bannerDetails: {banner_id: block["id"],banner_name: block["name"].titleize,banner_destination_url: block["link"],banner_category: "Home",banner_placement: 'front_page'.titleize}}.to_json
              = link_to block['link'] , onclick:"window.dataLayer = window.dataLayer || [];dataLayer.push({ bannerDetails: null }); dataLayer.push(#{ga_data})"do
                = picture_tag(block['photo']['sizes'], content_type: block['content_type'], p_style: "main_m", alt: "#{block['name']} #{blocks['type']} #{blocks['id']}", lazy: {})
  - when 'best_seller_designs' , 'hand_picked_designs'
    .board-item
      - designs = blocks['link'].present? ? blocks['design_items'].first(10) : blocks['design_items'].first(10)
      =render partial: 'pages/homepage_design_blocks', locals: {designs: designs, block_name: blocks['name'], path: blocks['link']}
  - when 'tag_slider'
    -if is_mobile_view?
      .board-item{class: blocks['type']}
        - if !blocks['name'].empty?
          .homepage-blocks
            .row.block-headers
              -if blocks['display_items_name']
                = blocks['name']
        .tag-slider
          - blocks['board_items'].each do |sliders|
            - ga_data = {event: "banner_click",bannerDetails: {banner_id: sliders["id"],banner_name: sliders["name"].titleize,banner_destination_url: sliders["link"],banner_category: "Home",banner_placement: 'tag_slider'.titleize}}.to_json
            = link_to sliders['link'], onclick:"window.dataLayer = window.dataLayer || [];dataLayer.push({ bannerDetails: null }); dataLayer.push(#{ga_data})" do
              .tag-slide
                = image_tag(sliders['photo']['sizes']['main_m_webp'], alt: "#{sliders['name']} #{blocks['type']} #{blocks['id']}", data: {original: sliders['photo']['sizes']['main_m']}, width: '70', height: '70',style: 'margin: auto;')
                .tag-slider-title #{sliders['name']}
  - when 'cart'
    -if is_mobile_view?
      =render partial: 'carts/line_items_info', locals: {name: blocks['name']}
  - when 'flash_deals'
    -if @flash_page.present? && @flash_page['ongoing_fd'].present?
      .board-item
        =render partial: 'pages/homepage_design_blocks', locals: {designs: @flash_page['ongoing_fd'][0]['ongoing_designs'].first(10), block_name: blocks['name'], path: blocks['link'], timer: true, end_date: @flash_page['ongoing_fd'][0]['fd_end_date'], type: blocks['type']}
    
- if UNBXD_V2_2023 && params["page"] == "2"
  -if @country_code != 'IN'
    .home-page-widget 
      #unbxd_home_recs_container_1.unbxdProductRecommendations{:data => {"page_type": "HOME", "html_id": "unbxd_home_recs_container_1",  "widget": "WIDGET1",  "uid": cookies['unbxd.userId']}}
  - else
    .home-page-widget 
      #unbxd_home_recs_container_2.unbxdProductRecommendations{:data => {"page_type": "HOME", "html_id": "unbxd_home_recs_container_2", "widget": "WIDGET2", "uid": cookies['unbxd.userId']}}


:javascript
  if (typeof(Turbolinks) != 'undefined') {
    MR.productRecommendation.initialiseRecommendations($('.unbxdProductRecommendations'))
  }
  document.addEventListener("DOMContentLoaded", function(event){
    MR.productRecommendation.initialiseRecommendations($('.unbxdProductRecommendations'))      
  });
  
  afterWindowOrTrubolinksLoad(function(){      
      MR.blazeSlider.init()
    })

