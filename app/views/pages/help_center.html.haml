- if @seo.present?
  - title @seo.title
  - description @seo.description 
  - keywords @seo.keyword
- content_for :page_specific_css do
  = stylesheet_link_tag 'pages'
- content_for :page_specific_js do
  = javascript_include_tag 'pages'
- if params[:api_layout] == 'true'
  :scss
    section.main-section{
      margin-top: 12px !important;
      div#main-section{
        #container{
          margin-top: 0px;
        }
      }
    }
.container#help_center
  .row
    .large-12.medium-8.small-12.columns.small-centered
      =content_tag 'div', '', id: 'account_presence', data: {account_present: current_account.present? ? "true" : "false",account_email_id: current_account.try(:email), api_layout: params[:api_layout].present?, app_source: @app_source.to_s}
      .page_title
        %button#nav_arrow &#x2190
        #main Help Center
        -faq_url = params[:api_layout] == 'true' ? 'faq?api_layout=true' : pages_faq_path
        =link_to 'FAQ', faq_url ,id: 'faq', data: {turbolinks: 'false'}
      %img#loadingImage{ style: 'display:none;',src: "#{asset_path('loading.gif')}"}
      %br
      .small-12
        .tabs-content
          -support_texts = @support_text_of
          -@headers.each_with_index do |header,index|
            .rows
              .category-links.columns.small-6.left{id:"category_title_#{index}", data: {attr_id: "categories_#{index}"}}
                =image_tag(header.name.gsub(/[\s\/]/,'').downcase+'_hc.png', class: "step_image", width: "38", height: "38")
                %br
                %h6=header.name
            %h6#category_title
              =header.name
            .helpcenter_qna{id:"categories_#{index}"}
              - header_p = header.try("#{support_texts}_support_texts".to_sym).sort_by(&:priority)
              -if header.name == "Order Queries"
                -if current_account.present? 
                  .rows
                    .columns.small-12
                      -if @order_details.present?
                        - @order_details.each do |order_no, order_data|
                          = render partial: 'orders_data', locals: {order_no: order_no, order_data: order_data} 
                        - header_p.each do |parent_support_text|
                          = render partial: 'helpcenter_question_answer',locals: {header: header, parent_support_text: parent_support_text}
                      -else
                        .no_order_found You Have Not Placed Any Orders Yet
                -else 
                  %center
                    .login_req
                      %div
                        %p Login to view your orders
                      %div
                        /=link_to 'Login',new_account_session_url,class: 'btn btn-lg btn-primary', style: 'color:white ;background-color:#670b19 !important; padding:1rem'
                        =link_to 'Login',(['android','ios'].include?(@app_source.to_s.downcase)) ? 'mirraw://page/login' : new_account_session_url(protocol: Rails.application.config.partial_protocol),class: 'button info', style: 'color:white !important;background-color: #670b19 !important;padding: 0.8rem 2rem'
                        %br
                        %br OR
                        %br
                        = form_tag nil,id: 'order_track_form', class:'form',method: :patch,target: '_blank' do
                          = text_field_tag 'order_number_field',nil,placeholder: 'Enter Order Number',class: 'form form-control small-12 columns',required: true,maxlength: 10, style: 'background:white'
                          = submit_tag 'Track',class: 'form-control button success',style: 'width: 50%;padding: 0.7em;background:#670b19'
              -else
                .rows
                  .columns.small-12
                    - header_p.each do |parent_support_text|
                      =render partial: 'helpcenter_question_answer',locals: {header: header, parent_support_text: parent_support_text}

  #emailUs.reveal-modal{"data-options" => "closeOnBackgroundClick:true", "aria-hidden" => "true", "aria-labelledby" => "modalTitle", "data-reveal" => "", role: "dialog"}
    %h4.modal-title{style:'font-size:large; font-weight:600; text-align:center'}
      Email Us
      %hr
    =render partial: 'freshdesk_ticket_form',locals: {designer_faq: false}

  #contactUs.reveal-modal{"data-options" => "closeOnBackgroundClick:true", "aria-hidden" => "true", "aria-labelledby" => "modalTitle", "data-reveal" => "", role: "dialog"}
    %a.close-reveal-modal{"aria-label" => "Close" , style:"text-align:right", href: "#", role: "button"} 
      &#215
    %h4.modal-title{style:'font-size:large; font-weight:600; text-align:center'}
      Contact Us
      %hr
    %table
      %tbody
        %tr
        -actual_country_code = (@country_code || Design.country_code)
        - if actual_country_code.to_s.downcase == "in"
          %td For Order Details
          %td
            %a{href: "tel:+912266484300"}+912266484300
          %tr
          %td For Sales
          %td
            %a{href: "tel:+918425049812"}+918425049812
        - else
          %td For Order Details
          %td
            -contact_number = ApplicationController.helpers.mirraw_contact_number(@symbol, actual_country_code)
            %a{href: "tel:#{contact_number.to_s.gsub(/[\s\-]/,'')}"}=contact_number
          %tr
          %td For Sales
          %td
            %a{href: "tel:+17606781780"}******-678-1780
  %a.button#track_order{href: '#', "data-reveal-id" => 'trackModal'} Track Your Order
  #trackModal.reveal-modal{"data-options" => "closeOnBackgroundClick:true", "aria-hidden" => "true", "aria-labelledby" => "modalTitle", "data-reveal" => "", role: "dialog"}
    %a.close-reveal-modal{"aria-label" => "Close", href: "#", role: "button"} &#215
    Track Your Order
    %br
    %br
    = form_tag nil,id: 'order_track_form', class:'form',method: :patch,target: '_blank' do
      = text_field_tag 'order_number_field',nil,placeholder: 'Enter Order Number',class: 'form form-control small-12 columns',required: true,maxlength: 10
      = submit_tag 'Track',class: 'form-control button success',style: 'width: 100%;padding: 0.7em;'

  -faq_url = params[:api_layout] == 'true' ? 'faq?api_layout=true' : pages_faq_path
  #faq_direct=link_to 'Read Our FAQs', faq_url,class: 'button info', data: {turbolinks: 'false'}
  .privacy-text{style: 'text-decoration: underline;'}
    =link_to 'Privacy policy', '/pages/privacy',class: 'help-center-text', style: 'color: #060505'
    %br
    =link_to 'Terms & Conditions', '/pages/terms',class: 'help-center-text terms', style: 'color: #060505'