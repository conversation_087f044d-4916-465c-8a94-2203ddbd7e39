- data_attr = { property: range_filter['name'].downcase }
- if rating_price.include?(range_filter['name'].try(:downcase))
  %div.content{id: "tab_#{range_index}", class: ('active' if params[:last_filter].present? && params[:last_filter] == "#{range_filter['name'].parameterize}") }
    - if params[range_filter['keys']['min']].present? && params[range_filter['keys']['max']].present?
      %input{id: "#{range_filter['keys']['min']}" ,class: 'range_filter_input' ,type: 'hidden' ,name: "#{range_filter['keys']['min']}", value: params[range_filter['keys']['min']]}
      %input{id: "#{range_filter['keys']['max']}" ,class: 'range_filter_input' ,type: 'hidden' ,name: "#{range_filter['keys']['max']}", value: params[range_filter['keys']['max']]}
    - else
      %input{id: "#{range_filter['keys']['min']}" ,class: 'range_filter_input' ,type: 'hidden' ,name: "#{range_filter['keys']['min']}"}
      %input{id: "#{range_filter['keys']['max']}" ,class: 'range_filter_input' ,type: 'hidden' ,name: "#{range_filter['keys']['max']}"}
    - if range_filter['list'].present?
      - range_filter['list'].each do |list|
        .single_line_div{style: "margin-bottom: 0.5rem;"}
          - if params[range_filter['keys']['min']].to_i == list['values']['min'].to_i && params[range_filter['keys']['max']].to_i == list['values']['max'].to_i && is_mobile_view?
            %a.facet-link{href: '#'}
              .on-off-radiobox.switch.tiny
                %input.filter-select{name: "#{range_filter['name']}_range" ,value: '' ,type: 'radio' ,id: "#{range_filter['name'].parameterize}_#{list['values']['min'].to_i}-#{list['values']['max'].to_i}", class: "#{range_filter['name'].parameterize}" ,checked: 'checked', data: {min_input: "#{range_filter['keys']['min']}" ,max_input: "#{range_filter['keys']['max']}",max: "#{list['values']['max'].to_i}" ,min: "#{list['values']['min'].to_i}" ,chip: "#{range_filter['name']} - #{list['name']}"}.merge(data_attr)}
                %label{for: "#{range_filter['name'].parameterize}_#{list['values']['min'].to_i}-#{list['values']['max'].to_i}"}
              %label.label-custom{for: "#{range_filter['name'].parameterize}_#{list['values']['min'].to_i}-#{list['values']['max'].to_i}", class: ('label-desktop-fix' unless browser.device.mobile?)}
                - if range_filter['name'].try(:downcase) == 'price'
                  = "#{get_symbol_from(@hex_symbol)}#{list['name']}"
                  - if list['count'] > 0
                    %span.round.success.label= list['count']
                - else
                  = list['name']
                  - if list['count'] > 0
                    %span.round.success.label= list['count']
          - else
            %a.facet-link{href: '#'}
              .on-off-radiobox.switch.tiny
                %input.filter-select{name: "#{range_filter['name']}_range" ,value: '' ,type: 'radio' ,id: "#{range_filter['name'].parameterize}_#{list['values']['min'].to_i}-#{list['values']['max'].to_i}", class: "#{range_filter['name'].parameterize}" ,data: {min_input: "#{range_filter['keys']['min']}" ,max_input: "#{range_filter['keys']['max']}",max: "#{list['values']['max'].to_i}" ,min: "#{list['values']['min'].to_i}"}.merge(data_attr)}
                %label{for: "#{range_filter['name'].parameterize}_#{list['values']['min'].to_i}-#{list['values']['max'].to_i}"}
              %label.label-custom{for: "#{range_filter['name'].parameterize}_#{list['values']['min'].to_i}-#{list['values']['max'].to_i}", class: ('label-desktop-fix' unless browser.device.mobile?)}
                - if range_filter['name'].try(:downcase) == 'price'
                  = "#{get_symbol_from(@hex_symbol)}#{list['name']}"
                  %span.round.warning.label= list['count']
                - else
                  = list['name']
                  %span.round.warning.label= list['count']