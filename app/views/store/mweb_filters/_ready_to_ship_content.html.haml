%div.content{id: "tab_#{ready_to_ship_filter['position']}"}
  - if ready_to_ship_filter['list'].present?
    - data_attr = { property: ready_to_ship_filter['name'].downcase }
    - data_attr[:key] = "#{ready_to_ship_filter['key']}"
    - ready_to_ship_filter['list'].each do |list|
      .single_line_div{style: "margin-bottom: 0.5rem;"}
        - checked = params[ready_to_ship_filter['key']].present? && params[ready_to_ship_filter['key']].include?(list['value']) ? 'checked' : nil
        - if checked
          %a.facet-link{href: '#'}
            .on-off-checkbox.switch.tiny
              %input.filter-select{name: "ready_to_ship[]" ,value: list['value'] ,type: 'checkbox' ,id: "#{ready_to_ship_filter['name'].parameterize}_#{list['value']}", class: "#{ready_to_ship_filter['name'].parameterize}" ,checked: 'checked', data: {ready_to_ship: "#{list['value']}",chip: "#{ready_to_ship_filter['name']} - #{list['name']}"}.merge(data_attr)}
              %label{for: "#{ready_to_ship_filter['name'].parameterize}_#{list['value']}"}
            %label.label-custom{for: "#{ready_to_ship_filter['name'].parameterize}_#{list['value']}", class: ('label-desktop-fix' unless browser.device.mobile?)}
              = list['name']
              - if list['count'] > 0
                %span.round.success.label= list['count']
        - else
          %a.facet-link{href: '#'}
            .on-off-checkbox.switch.tiny
              %input.filter-select{name: "ready_to_ship[]" ,value: list['value'] ,type: 'checkbox' ,id: "#{ready_to_ship_filter['name'].parameterize}_#{list['value']}", class: "#{ready_to_ship_filter['name'].parameterize}" ,data: {ready_to_ship: "#{list['value']}"}.merge(data_attr)}
              %label{for: "#{ready_to_ship_filter['name'].parameterize}_#{list['value']}"}
            %label.label-custom{for: "#{ready_to_ship_filter['name'].parameterize}_#{list['value']}", class: ('label-desktop-fix' unless browser.device.mobile?)}
              = list['name']
              - if list['count'] > 0
                %span.round.warning.label= list['count']
