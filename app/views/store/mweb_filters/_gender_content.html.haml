%div.content{id: "tab_#{gender_filter['position']}"}
  - if gender_filter['list'].present?
    - data_attr = { property: gender_filter['name'].downcase }
    - data_attr[:key] = "#{gender_filter['key']}"
    - gender_filter['list'].each do |list|
      .single_line_div{style: "margin-bottom: 0.5rem;"}
        - if params[gender_filter['key']] == list['value']
          %a.facet-gen{href: '#'}
            .on-off-radiobox.switch.tiny
              %input.filter-select{name: "gender" ,value: list['value'] ,type: 'radio' ,id: "#{gender_filter['name'].parameterize}_#{list['value']}", class: "#{gender_filter['name'].parameterize}" ,checked: 'checked', data: {gender: "#{list['value']}",chip: "#{gender_filter['name']} - #{list['name']}"}.merge(data_attr)}
              %label{for: "#{gender_filter['name'].parameterize}_#{list['value']}"}
            %label.label-custom{for: "#{gender_filter['name'].parameterize}_#{list['value']}", class: ('label-desktop-fix' unless browser.device.mobile?)}
              = list['name']
        - else
          %a.facet-gen{href: '#'}
            .on-off-radiobox.switch.tiny
              %input.filter-select{name: "gender" ,value: list['value'] ,type: 'radio' ,id: "#{gender_filter['name'].parameterize}_#{list['value']}", class: "#{gender_filter['name'].parameterize}" ,data: {gender: "#{list['value']}"}.merge(data_attr)}
              %label{for: "#{gender_filter['name'].parameterize}_#{list['value']}"}
            %label.label-custom{for: "#{gender_filter['name'].parameterize}_#{list['value']}", class: ('label-desktop-fix' unless browser.device.mobile?)}
              = list['name']