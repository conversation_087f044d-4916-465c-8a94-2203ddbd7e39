- if allow_facet_for_view?(params[:kind], id_filter['name'], id_filter['priority'])
  %a.button.tiny.tab-filter-fix{href: "#tab_check_#{index}", id: "#{id_filter['name'].parameterize}"}
    = id_filter['name']
    - if (filter_keys = params[id_filter['key']]).present?
      - active_filter = get_active_filters_count(id_filter['list'].map{|x| x['value']}, filter_keys)
      - if active_filter > 0
        %span.tiny-green= "#{active_filter}"