%ul.accordion{"data-accordion" => ""}
  - if allow_facet_for_view?(params[:kind], id_filter['name'], id_filter['priority']) # Not Sure Why Created This allow Facet for View 
    - filter_name = id_filter['name'].try(:downcase).to_s.gsub(' ', '-')
    %li.accordion-navigation.accordion-item
      %a.accordion-header{href: "#accordionFilter#{filter_name}", class: "accordion-title"}
        %div.accordion-button.collapsed.filter-header-title{"aria-controls" => "accordionFilter#{filter_name}Collapse", "aria-expanded" => "true", "data-bs-target" => "#accordionFilter#{filter_name}Collapse", "data-bs-toggle" => "collapse", :type => "button"}
          = id_filter['name']
      .content{"aria-labelledby" => "accordionFilter#{filter_name}", "data-bs-parent" => "#accordionExample",id: "accordionFilter#{filter_name}"}
        %button.clear-all-btn{"data-filter-name" => filter_name}
          %span.clear-icon ×
          %span.clear-text Clear All
        .filter-search-input-field
          %i.fi-magnifying-glass
          %input.search-field{type: 'text', placeholder: "Search #{id_filter['name']}"}
        .accordion-body.filterCustomScrollbar.mCustomScrollbar 
          - if id_filter['list'].present?
            - data_attr = { property: id_filter['name'].downcase }
            - id_filter['list'].each do |list|
              - box_type = id_filter['key'] == 'property_value_ids' && facets_info && !id_filter['name'].match(/color/i).present? ? 'radio' : 'checkbox'
              - box_type = id_filter['key'] == 'property_value_ids' && id_filter['name'] == 'Gender' ? 'radio' : 'checkbox'
              - if facets_info
                - current_prop = { name: list['pv_name'], priority: id_filter['priority'], type: box_type }
                - data_attr.merge!(current_prop)
                - if id_filter['priority'].to_i > 0 && create_url_for_facets?(box_type, @facet_properties)
                  - current_prop[:property] = id_filter['prop_name']
                  - link = create_faceted_url(params[:kind], current_prop, @facet_properties, page_base_url) + url_params.to_s
              - data_attr[:key] = "#{id_filter['key']}[]"
              - if id_filter['name'].match(/color/i).present?
                - color_val = (list['color_code'] == "-1") ? "null" : "rgba(#{list['color_code']})"
                .single_line_div
                  - if params[id_filter['key']].present? && params[id_filter['key']].split(',').include?("#{list['value']}")
                    - data_attr[:chip] = "#{id_filter['name']} - #{list['name']}"
                    %a.facet-link-desktop{href: link}
                      .on-off-checkbox.color-switch.switch-desk
                        %input.filter-select.color-input{name: "#{id_filter['key']}-#{id_filter['name']}[]" ,id: "#{id_filter['key']}_#{list['value']}" ,type: 'checkbox', placeholder: "#{list['name']}".downcase.tr(' ','-'), value: "#{list['value']}", checked: 'checked', class: [('color-append' if ['saree-color','kameez-color','color', 'lehenga-color'].include?(id_filter['name'].parameterize)),("#{id_filter['name'].parameterize}")], data: data_attr }
                        %label.label-custom-color-desktop{for: "#{id_filter['key']}_#{list['value']}", class: ('multicolor-value' if list['color_code'] == "-1"), style: "background: #{color_val}"}
                      %label.label-custom{for: "#{id_filter['key']}_#{list['value']}", class: ('label-desktop-fix' unless browser.device.mobile?)}
                        = list['name']
                  - else
                    %a.facet-link-desktop{href: link}
                      .on-off-checkbox.color-switch.switch-desk
                        %input.filter-select.color-input{name: "#{id_filter['key']}-#{id_filter['name']}[]" ,id: "#{id_filter['key']}_#{list['value']}" ,type: 'checkbox', placeholder: "#{list['name']}".downcase.tr(' ','-'), data: data_attr, value: "#{list['value']}", class: [("color-append" if ['saree-color','kameez-color','color', 'lehenga-color'].include?(id_filter['name'].parameterize)),("#{id_filter['name'].parameterize}")]}
                        %label.label-custom-color-desktop{for: "#{id_filter['key']}_#{list['value']}", class: ('multicolor-value' if list['color_code'] == "-1"), style: "background: #{color_val}"}
                      %label.label-custom{for: "#{id_filter['key']}_#{list['value']}", class: ('label-desktop-fix' unless browser.device.mobile?)}
                        = list['name']
              - else
                - class_type = box_type == 'radio' ? 'on-off-radiobox' : 'on-off-checkbox'
                .single_line_div
                  - if params[id_filter['key']].present? && params[id_filter['key']].split(',').include?("#{list['value']}")
                    - data_attr[:chip] = "#{id_filter['name']} - #{list['name']}"
                    %a.facet-link-desktop{href: link}
                      .switch-desk.tiny{class: class_type}
                        %input.filter-select{name: "#{id_filter['key']}-#{id_filter['name']}[]" ,id: "#{id_filter['key']}_#{list['value']}" ,class: "#{id_filter['name'].parameterize}" ,type: box_type, value: "#{list['value']}", checked: 'checked', data: data_attr}
                        %label{for: "#{id_filter['key']}_#{list['value']}"}
                      %label.label-custom{for: "#{id_filter['key']}_#{list['value']}", class: ('label-desktop-fix' unless browser.device.mobile?)}
                        = list['name']
                  - else
                    %a.facet-link-desktop{href: link}
                      .switch-desk.tiny{class: class_type}
                        %input.filter-select{name: "#{id_filter['key']}-#{id_filter['name']}[]" ,id: "#{id_filter['key']}_#{list['value']}" ,class: "#{id_filter['name'].parameterize}" ,type: box_type, value: "#{list['value']}", data: data_attr}
                        %label{for: "#{id_filter['key']}_#{list['value']}"}
                      %label.label-custom{for: "#{id_filter['key']}_#{list['value']}", class: ('label-desktop-fix' unless browser.device.mobile?)}
                        = list['name']
                        //%span.round.warning.label= list['count']