- cache ["landing_page/mobile/#{Design.country_code}/#{params[:api_layout] || false}", dynamic_landing_page] do
  #main-container.container
    .row.main-banner-block
      .columns.small-12
        = link_to dynamic_landing_page.links["Banner1"] do
          = image_tag(dynamic_landing_page.imageurls["Banner1"], class: 'block-image')
    .row.margin-top-bottom
      .columns.small-12.medium-6.large-6
        .design-block
          = link_to dynamic_landing_page.links["Banner2"] do
            = image_tag(dynamic_landing_page.imageurls["Banner2"], class: 'block-image')
      .columns.small-12.medium-6.large-6
        .row.design-one
          .small-12.columns.medium-12.large-12
            .design-block
              = link_to dynamic_landing_page.links["Banner3"] do
                = image_tag(dynamic_landing_page.imageurls["Banner3"], class: 'block-image')
          .small-12.columns.medium-12.large-12.box-margin-top
            .row
              .small-6.columns.medium-6.large-6.pad-right
                .design-block
                  = link_to dynamic_landing_page.links["Block1"] do
                    = image_tag(dynamic_landing_page.imageurls["Block1"], class: 'block-image')
              .small-6.columns.medium-6.large-6.pad-left
                .design-block
                  = link_to dynamic_landing_page.links["Block2"] do
                    = image_tag(dynamic_landing_page.imageurls["Block2"], class: 'block-image')
    .row.margin-top-bottom
      .small-12.columns.medium-3.large-3
        .design-block
          = link_to dynamic_landing_page.links["FBlock1"] do
            = image_tag(dynamic_landing_page.imageurls["FBlock1"], class: 'block-image')
      .small-12.columns.medium-3.large-3
        .design-block
          = link_to dynamic_landing_page.links["FBlock2"] do
            = image_tag(dynamic_landing_page.imageurls["FBlock2"], class: 'block-image')
      .small-12.columns.medium-3.large-3
        .design-block
          = link_to dynamic_landing_page.links["FBlock3"] do
            = image_tag(dynamic_landing_page.imageurls["FBlock3"], class: 'block-image')
      .small-12.columns.medium-3.large-3
        .design-block
          = link_to dynamic_landing_page.links["FBlock4"] do
            = image_tag(dynamic_landing_page.imageurls["FBlock4"], class: 'block-image')
    .row
      .columns.small-12
        .design-block
          = image_tag(dynamic_landing_page.imageurls["Spec1"], class: 'spec-banner')
    .row.margin-top-bottom
      .columns.small-12.medium-4.large-4
        .design-block
          = link_to dynamic_landing_page.links["BlockMed1"] do
            = image_tag(dynamic_landing_page.imageurls["BlockMed1"], class: 'block-image')
      .columns.small-12.medium-4.large-4
        .design-block
          = link_to dynamic_landing_page.links["BlockMed2"] do
            = image_tag(dynamic_landing_page.imageurls["BlockMed2"], class: 'block-image')
      .columns.small-12.medium-4.large-4
        .design-block
          = link_to dynamic_landing_page.links["BlockMed3"] do
            = image_tag(dynamic_landing_page.imageurls["BlockMed3"], class: 'block-image')
    .row.margin-top-bottom
      .columns.small-12.medium-4.large-4
        .design-block
          = link_to dynamic_landing_page.links["BlockMed4"] do
            = image_tag(dynamic_landing_page.imageurls["BlockMed4"], class: 'block-image')
      .columns.small-12.medium-4.large-4
        .design-block
          = link_to dynamic_landing_page.links["BlockMed5"] do
            = image_tag(dynamic_landing_page.imageurls["BlockMed5"], class: 'block-image')
      .columns.small-12.medium-4.large-4
        .design-block
          = link_to dynamic_landing_page.links["BlockMed6"] do
            = image_tag(dynamic_landing_page.imageurls["BlockMed6"], class: 'block-image')
    - if designs.present?
      .row#product-box.margin-top-bottom
        .featured-products
          .columns.small-12
            .bestseller-title BESTSELLER PRODUCTS
          %ul#bestsellers.home_page_designs.small-block-grid-2.medium-block-grid-4.large-block-grid
            = render partial: 'designs/design_blocks', locals: {bestseller_designs: designs, from_bestseller: false}
          - if dynamic_landing_page.view_more_url.present?
            %div
              .view-more
                %a.button.primary.tiny.red-button{href: dynamic_landing_page.view_more_url} View More