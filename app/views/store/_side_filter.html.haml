- index = request.path.index(params[:facets]) - 2 if params[:facets].present?
- page_base_url = index ? request.path[0..index] : request.path
- facets_info = FACETED_URL_KINDS[params[:kind]]
- if facets_info
  - url_params = create_url_parameters(@facet_properties, params)
- rating_price = RATING_ENABLE ? ['rating','price','discount percent'] : ['price','discount percent']
.filter-accordion-wrapper
  -if @store_page['filters']['list'].present?
    - @store_page['filters']['list'].each do |filter|
      - if filter['type'] == 'id_filters'
        = render partial: 'id_filter', locals: {id_filter: filter, page_base_url: page_base_url, url_params: url_params,facets_info: facets_info}
      - elsif filter['type'] == 'range_filter'
        = render partial: 'range_filter', locals: {range_filter: filter, rating_price: rating_price,  page_base_url: page_base_url, url_params: url_params,facets_info: facets_info}
      - elsif filter['type'] == 'gender_filter'
        = render partial: 'gender_filter', locals: {gender_filter: filter,  page_base_url: page_base_url, url_params: url_params,facets_info: facets_info}
      - elsif filter['type'] == 'ready_to_ship_filter' && filter['list'].first['count'] >  0
        = render partial: 'ready_to_ship_filter', locals: {ready_to_ship_filter: filter,  page_base_url: page_base_url, url_params: url_params,facets_info: facets_info} 