%ul.accordion{"data-accordion" => ""}
  - filter_name = gender_filter['name'].try(:downcase).to_s
  %li.accordion-navigation.accordion-item
    %a.accordion-header{href: "#accordionFilter#{filter_name}", class: "accordion-title"}
      %button.accordion-button.collapsed.filter-header-title{"aria-controls" => "accordionFilter#{filter_name}Collapse", "aria-expanded" => "true", "data-bs-target" => "#accordionFilter#{filter_name}Collapse", "data-bs-toggle" => "collapse", :type => "button"}
        = gender_filter['name']
    .content{"aria-labelledby" => "accordionFilter#{filter_name}", "data-bs-parent" => "#accordionExample",id: "accordionFilter#{filter_name}"}
      %button.clear-all-btn{"data-filter-name" => filter_name}
        %span.clear-icon ×
        %span.clear-text Clear All
      .accordion-body.filterCustomScrollbar.mCustomScrollbar 
        - if gender_filter['list'].present?
          - data_attr = { property: gender_filter['name'].downcase }
          - data_attr[:key] = "#{gender_filter['key']}"
          - gender_filter['list'].each do |list|
            .single_line_div{style: "margin-bottom: 0.5rem;"}
              - if params[gender_filter['key']] == list['value']
                -data_attr[:chip] = "#{gender_filter['name']} - #{list['name']}"
                .facet-link-desktop
                  .on-off-checkbox.switch-desk.tiny
                    %input.filter-select{name: "gender-desktop" ,value: list['value'] ,type: 'radio' ,id: "#{gender_filter['name'].parameterize}_#{list['value']}", class: "#{gender_filter['name'].parameterize}" ,checked: 'checked', data: {gender: "#{list['value']}"}.merge(data_attr)}
                    %label{for: "#{gender_filter['name'].parameterize}_#{list['value']}"}
                  %label.label-custom{for: "#{gender_filter['name'].parameterize}_#{list['value']}", class: ('label-desktop-fix' unless browser.device.mobile?)}
                    = list['name']
              - else
                .facet-link-desktop
                  .on-off-checkbox.switch-desk.tiny
                    %input.filter-select{name: "gender-desktop" ,value: list['value'] ,type: 'radio' ,id: "#{gender_filter['name'].parameterize}_#{list['value']}", class: "#{gender_filter['name'].parameterize}" ,data: {gender: "#{list['value']}"}.merge(data_attr)}
                    %label{for: "#{gender_filter['name'].parameterize}_#{list['value']}"}
                  %label.label-custom{for: "#{gender_filter['name'].parameterize}_#{list['value']}", class: ('label-desktop-fix' unless browser.device.mobile?)}
                    = list['name']