- cache ["landing_page/mobile/#{Design.country_code}/#{params[:api_layout] || false}", dynamic_landing_page] do
  #main-container.container
    .row.main-banner-block
      .columns.small-12
        = link_to dynamic_landing_page.links["Banner1"] do
          = image_tag(dynamic_landing_page.imageurls["Banner1"], class: 'block-image')
    .row.margin-top-bottom
      .columns.small-12.medium-5.large-5
        .design-block
          = link_to dynamic_landing_page.links["Banner2"] do
            = image_tag(dynamic_landing_page.imageurls["Banner2"], class: 'block-image')
      .columns.small-12.medium-7.large-7
        .row.design-one
          .small-12.columns.medium-12.large-12
            .design-block
              = link_to dynamic_landing_page.links["Banner3"] do
                = image_tag(dynamic_landing_page.imageurls["Banner3"], class: 'block-image')
          .small-12.columns.medium-12.large-12.box-margin-top
            .row
              .small-6.columns.medium-6.large-6.pad-right
                .design-block
                  = link_to dynamic_landing_page.links["Block1"] do
                    = image_tag(dynamic_landing_page.imageurls["Block1"], class: 'block-image')
              .small-6.columns.medium-6.large-6.pad-left
                .design-block
                  = link_to dynamic_landing_page.links["Block2"] do
                    = image_tag(dynamic_landing_page.imageurls["Block2"], class: 'block-image')
    .row.margin-top-bottom
      .small-12.medium-push-3.columns.medium-6.large-6
        .design-block
          = link_to dynamic_landing_page.links["Block4"] do
            = image_tag(dynamic_landing_page.imageurls["Block4"], class: 'block-image')
      .small-6.medium-pull-6.columns.medium-3.large-3.pad-right
        .design-block
          = link_to dynamic_landing_page.links["Block3"] do
            = image_tag(dynamic_landing_page.imageurls["Block3"], class: 'block-image')
      .small-6.columns.medium-3.large-3.pad-left
        .design-block
          = link_to dynamic_landing_page.links["Block5"] do
            = image_tag(dynamic_landing_page.imageurls["Block5"], class: 'block-image')
    .row.margin-top-bottom
      .columns.small-12.medium-4.large-4
        .design-block
          = link_to dynamic_landing_page.links["BlockMed1"] do
            = image_tag(dynamic_landing_page.imageurls["BlockMed1"], class: 'block-image')
      .columns.small-12.medium-4.large-4
        .design-block
          = link_to dynamic_landing_page.links["BlockMed2"] do
            = image_tag(dynamic_landing_page.imageurls["BlockMed2"], class: 'block-image')
      .columns.small-12.medium-4.large-4
        .design-block
          = link_to dynamic_landing_page.links["BlockMed3"] do
            = image_tag(dynamic_landing_page.imageurls["BlockMed3"], class: 'block-image')
    .row.margin-top-bottom
      .columns.small-12.medium-4.large-4
        .design-block
          = link_to dynamic_landing_page.links["BlockMed4"] do
            = image_tag(dynamic_landing_page.imageurls["BlockMed4"], class: 'block-image')
      .columns.small-12.medium-4.large-4
        .design-block
          = link_to dynamic_landing_page.links["BlockMed5"] do
            = image_tag(dynamic_landing_page.imageurls["BlockMed5"], class: 'block-image')
      .columns.small-12.medium-4.large-4
        .design-block
          = link_to dynamic_landing_page.links["BlockMed6"] do
            = image_tag(dynamic_landing_page.imageurls["BlockMed6"], class: 'block-image')
    - if designs.present?
      .row#product-box.margin-top-bottom
        .featured-products{style: 'border:none;'}
          %ul#bestsellers.home_page_designs.small-block-grid-2.medium-block-grid-4.large-block-grid
            = render partial: 'designs/design_blocks', locals: {bestseller_designs: designs, from_bestseller: false}
    .row.margin-top-bottom
      .small-12.columns.medium-6.large-6
        .design-block
          = link_to dynamic_landing_page.links["BannerBottom1"] do
            = image_tag(dynamic_landing_page.imageurls["BannerBottom1"], class: 'block-image')
      .small-12.columns.medium-6.large-6
        .design-block
          = link_to dynamic_landing_page.links["BannerBottom2"] do
            = image_tag(dynamic_landing_page.imageurls["BannerBottom2"], class: 'block-image')
    .row
      .small-12.columns.medium-9.large-9
        .design-block
          = link_to dynamic_landing_page.links["BannerBottom3"] do
            = image_tag(dynamic_landing_page.imageurls["BannerBottom3"], class: 'block-image')
      .small-12.columns.medium-3.large-3
        .design-block
          = link_to dynamic_landing_page.links["BannerBottom4"] do
            = image_tag(dynamic_landing_page.imageurls["BannerBottom4"], class: 'block-image')