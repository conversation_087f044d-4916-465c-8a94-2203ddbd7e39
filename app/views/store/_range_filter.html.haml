%ul.accordion{"data-accordion" => ""}
  - filter_name = range_filter['name'].try(:downcase).to_s
  - filter_name = 'price_range' if filter_name == 'price'
  - filter_name = 'discount_percent' if filter_name == 'discount percent'
  - data_attr = { property: range_filter['name'].downcase }
  - if rating_price.include?(range_filter['name'].try(:downcase))
    %li.accordion-navigation.accordion-item
      %a.accordion-header{href: "#accordionFilter#{filter_name}", class: "accordion-title"}
        %div.accordion-button.collapsed.filter-header-title{"aria-controls" => "accordionFilter#{filter_name}Collapse", "aria-expanded" => "true", "data-bs-target" => "#accordionFilter#{filter_name}Collapse", "data-bs-toggle" => "collapse", :type => "button"}
          = range_filter_name(range_filter)
          / <span class="active-dot"></span> <span class="clear-btn">Clear</span>
      .content.accordion-collapse.collapse{"aria-labelledby" => "accordionFilter#{filter_name}", "data-bs-parent" => "#accordionExample",id: "accordionFilter#{filter_name}"}
        %button.clear-all-btn{"data-filter-name" => filter_name}
          %span.clear-icon ×
          %span.clear-text Clear All
        .accordion-body.filterCustomScrollbar
          - if params[range_filter['keys']['min']].present? && params[range_filter['keys']['max']].present?
            %input{id: "#{range_filter['keys']['min']}" ,class: 'range_filter_input' ,type: 'hidden' ,name: "#{range_filter['keys']['min']}", value: params[range_filter['keys']['min']]}
            %input{id: "#{range_filter['keys']['max']}" ,class: 'range_filter_input' ,type: 'hidden' ,name: "#{range_filter['keys']['max']}", value: params[range_filter['keys']['max']]}
          - else
            %input{id: "#{range_filter['keys']['min']}" ,class: 'range_filter_input' ,type: 'hidden' ,name: "#{range_filter['keys']['min']}"}
            %input{id: "#{range_filter['keys']['max']}" ,class: 'range_filter_input' ,type: 'hidden' ,name: "#{range_filter['keys']['max']}"}
          - if range_filter['list'].present?
            - range_filter['list'].each do |list|
              .single_line_div
                - if params[range_filter['keys']['min']].to_i == list['values']['min'].to_i && params[range_filter['keys']['max']].to_i == list['values']['max'].to_i
                  .facet-link-desktop
                    .on-off-radiobox.switch-desk.tiny
                      %input.filter-select{name: "#{range_filter['name']}_range" ,value: '' ,type: 'radio' ,id: "#{range_filter['name'].parameterize}_#{list['values']['min'].to_i}-#{list['values']['max'].to_i}", class: "#{range_filter['name'].parameterize}" ,checked: 'checked', data: {min_input: "#{range_filter['keys']['min']}" ,max_input: "#{range_filter['keys']['max']}",max: "#{list['values']['max'].to_i}" ,min: "#{list['values']['min'].to_i}" ,chip: (range_filter['name'].try(:downcase) == 'discount percent' ? "Discount - #{list['name']}" : "#{range_filter['name']} - #{list['name']}")}.merge(data_attr)}
                      %label{for: "#{range_filter['name'].parameterize}_#{list['values']['min'].to_i}-#{list['values']['max'].to_i}"}
                    %label.label-custom{for: "#{range_filter['name'].parameterize}_#{list['values']['min'].to_i}-#{list['values']['max'].to_i}", class: ('label-desktop-fix' unless browser.device.mobile?)}
                      - if range_filter['name'].try(:downcase) == 'price'
                        = "#{get_symbol_from(@hex_symbol)} #{list['name']}"
                      - else
                        = list['name']
                - else
                  .facet-link-desktop
                    .on-off-radiobox.switch-desk.tiny
                      %input.filter-select{name: "#{range_filter['name']}_range" ,value: '' ,type: 'radio' ,id: "#{range_filter['name'].parameterize}_#{list['values']['min'].to_i}-#{list['values']['max'].to_i}", class: "#{range_filter['name'].parameterize}", data: {min_input: "#{range_filter['keys']['min']}" ,max_input: "#{range_filter['keys']['max']}",max: "#{list['values']['max'].to_i}" ,min: "#{list['values']['min'].to_i}"}.merge(data_attr)}
                      %label{for: "#{range_filter['name'].parameterize}_#{list['values']['min'].to_i}-#{list['values']['max'].to_i}"}
                    %label.label-custom{for: "#{range_filter['name'].parameterize}_#{list['values']['min'].to_i}-#{list['values']['max'].to_i}", class: ('label-desktop-fix' unless browser.device.mobile?)}
                      - if range_filter['name'].try(:downcase) == 'price'
                        = "#{get_symbol_from(@hex_symbol)} #{list['name']}"
                        //%span.round.warning.label= list['count']
                      - else
                        = list['name']