- if @store_page.present? && @store_page['designs'].present?
  $('ul.store_page_design').append("#{escape_javascript(render(partial: 'designs', locals: {store_designs: @store_page['designs']}))}");
  - if @store_page['next_page'].present?
    nextPageUrl = window.location.href.replace(/&?page=[^\&]*/, '') + '&more_designs=true&page=' + "#{ @store_page['next_page']}";
    $('.next').attr('id', "page_#{@store_page['next_page']}");
    $('.current_page').data('page',"#{params[:page]}");
    $('.next').attr('href', nextPageUrl);
  - else
    $(window).off('scroll');
    $('.current_page').data('page',"#{params[:page]}");
    $('.navigate_page').remove();
  -# ga('set', 'page', "#{request.fullpath}".replace(/&amp;/g, "&"));
  - if CRITEO_ACCOUNT_ID[@country_code].present?
    criteo_dynamic_object= {event: "viewList", item:  #{raw(@store_page['designs'].collect{|ds| ds['id'].to_s})}};
    criteo_push_data();