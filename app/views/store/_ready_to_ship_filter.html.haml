%ul.accordion{"data-accordion" => ""}
  - filter_name = ready_to_ship_filter['name'].try(:downcase).to_s
  %li.accordion-navigation.accordion-item
    %a.accordion-header{href: "javascript:void(0)", class: "accordion-title"}
      %button.accordion-button.collapsed.filter-header-title{"aria-controls" => "accordionFilter#{filter_name}Collapse", "aria-expanded" => "true", "data-bs-target" => "#accordionFilter#{filter_name}Collapse", "data-bs-toggle" => "collapse", :type => "button"}
        = ready_to_ship_filter['name']
    .content.active{"aria-labelledby" => "accordionFilter#{filter_name}", "data-bs-parent" => "#accordionExample", id: "accordionFilter#{filter_name}"}
      .accordion-body.filterCustomScrollbar.mCustomScrollbar 
        - if ready_to_ship_filter['list'].present?
          - data_attr = { property: ready_to_ship_filter['name'].downcase }
          - data_attr[:key] = "#{ready_to_ship_filter['key']}"
          - ready_to_ship_filter['list'].each do |list|
            .single_line_div{style: "margin-bottom: 0.5rem;"}
              - if params[ready_to_ship_filter['key']].present? && params[ready_to_ship_filter['key']].include?(list['value'])
                - data_attr[:chip] = "#{ready_to_ship_filter['name']} - #{list['name']}"
                .facet-link-desktop
                  .on-off-checkbox.switch-desk.tiny
                    %input.filter-select{name: "ready_to_ship_checkbox[]", value: list['value'], type: 'checkbox', id: "#{ready_to_ship_filter['name'].parameterize}_#{list['value']}", class: "#{ready_to_ship_filter['name'].parameterize}", checked: 'checked', data: {ready_to_ship: "#{list['value']}"}.merge(data_attr)}
                    %label{for: "#{ready_to_ship_filter['name'].parameterize}_#{list['value']}"}
                  %label.label-custom{for: "#{ready_to_ship_filter['name'].parameterize}_#{list['value']}", class: ('label-desktop-fix' unless browser.device.mobile?)}
                    = list['name']
              - else
                .facet-link-desktop
                  .on-off-checkbox.switch-desk.tiny
                    %input.filter-select{name: "ready_to_ship_checkbox[]", value: list['value'], type: 'checkbox', id: "#{ready_to_ship_filter['name'].parameterize}_#{list['value']}", class: "#{ready_to_ship_filter['name'].parameterize}", data: {ready_to_ship: "#{list['value']}"}.merge(data_attr)}
                    %label{for: "#{ready_to_ship_filter['name'].parameterize}_#{list['value']}"}
                  %label.label-custom{for: "#{ready_to_ship_filter['name'].parameterize}_#{list['value']}", class: ('label-desktop-fix' unless browser.device.mobile?)}
                    = list['name']