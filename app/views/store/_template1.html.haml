- cache ["landing_page/mobile/#{Design.country_code}/#{params[:api_layout] || false}", dynamic_landing_page] do
  #main-container.container
    .row.main-banner-block
      .columns.small-12
        = link_to dynamic_landing_page.links["Banner1"] do
          = image_tag(dynamic_landing_page.imageurls["Banner1"], class: 'block-image')
    .row.margin-top-bottom
      .columns.small-12.medium-8.large-8
        .design-block
          = link_to dynamic_landing_page.links["Banner2"] do
            = image_tag(dynamic_landing_page.imageurls["Banner2"], class: 'block-image')
      .columns.small-12.medium-4.large-4
        .row.design-one
          .small-6.columns.medium-12.large-12.pad-right
            .design-block
              = link_to dynamic_landing_page.links["Banner3"] do
                = image_tag(dynamic_landing_page.imageurls["Banner3"], class: 'block-image')
          .small-6.columns.medium-12.large-12.pad-left.box-margin-top
            .design-block
              = link_to dynamic_landing_page.links["Banner4"] do
                = image_tag(dynamic_landing_page.imageurls["Banner4"], class: 'block-image')
    .row.margin-top-bottom
      .columns.small-12.medium-4.large-4
        .design-block
          = image_tag(dynamic_landing_page.imageurls["Spec1"], class: 'spec-banner')
      .columns.small-12.medium-4.large-4.med-block
        .design-block
          = image_tag(dynamic_landing_page.imageurls["Spec2"], class: 'spec-banner')
      .columns.small-12.medium-4.large-4.med-block
        .design-block
          = image_tag(dynamic_landing_page.imageurls["Spec3"], class: 'spec-banner')
    .row.margin-top-bottom
      .columns.small-12.medium-4.large-4
        .row
          .small-6.columns.medium-12.large-12.pad-right
            .design-block
              = link_to dynamic_landing_page.links["Block1"] do
                = image_tag(dynamic_landing_page.imageurls["Block1"], class: 'block-image')
          .small-6.columns.medium-12.large-12.pad-left.box-margin-top
            .design-block
              = link_to dynamic_landing_page.links["Block2"] do
                = image_tag(dynamic_landing_page.imageurls["Block2"], class: 'block-image')
      .columns.small-12.medium-4.large-4
        .row
          .columns.small-12.no-col-padding
            .design-block
              = link_to dynamic_landing_page.links["BlockBig"] do
                = image_tag(dynamic_landing_page.imageurls["BlockBig"], class: 'block-image')
      .columns.small-12.medium-4.large-4
        .row
          .small-6.columns.medium-12.large-12.pad-right
            .design-block
              = link_to dynamic_landing_page.links["Block3"] do
                = image_tag(dynamic_landing_page.imageurls["Block3"], class: 'block-image')
          .small-6.columns.medium-12.large-12.pad-left.box-margin-top
            .design-block
              = link_to dynamic_landing_page.links["Block4"] do
                = image_tag(dynamic_landing_page.imageurls["Block4"], class: 'block-image')
    .row.margin-top-bottom
      .columns.small-12.medium-4.large-4
        .design-block
          = link_to dynamic_landing_page.links["BlockMed1"] do
            = image_tag(dynamic_landing_page.imageurls["BlockMed1"], class: 'block-image')
      .columns.small-12.medium-4.large-4.med-block
        .design-block
          = link_to dynamic_landing_page.links["BlockMed2"] do
            = image_tag(dynamic_landing_page.imageurls["BlockMed2"], class: 'block-image')
      .columns.small-12.medium-4.large-4.med-block
        .design-block
          = link_to dynamic_landing_page.links["BlockMed3"] do
            = image_tag(dynamic_landing_page.imageurls["BlockMed3"], class: 'block-image')
    .row.margin-top-bottom
      .columns.small-12
        .design-block
          = link_to dynamic_landing_page.links["BannerBottom"] do
            = image_tag(dynamic_landing_page.imageurls["BannerBottom"], class: 'block-image')
    - if @designs.present?
      .row#product-box.margin-top-bottom
        .featured-products
          .columns.small-12
            .bestseller-title 
              %h1 BESTSELLER PRODUCTS
          %ul#bestsellers.home_page_designs.small-block-grid-2.medium-block-grid-4.large-block-grid
            = render partial: 'designs/design_blocks', locals: {bestseller_designs: designs, from_bestseller: false}
          - if dynamic_landing_page.view_more_url.present?
            %div
              .view-more
                %a.button.primary.tiny.red-button{href: dynamic_landing_page.view_more_url} View More