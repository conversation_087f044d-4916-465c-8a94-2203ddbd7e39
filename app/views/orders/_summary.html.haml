- if (@order.cod? || @order.cbd?) && (COD_NOTE_MESSAGE != 'false')
  .row
    .columns
      .alert-box.radius.info
        = "#{COD_NOTE_MESSAGE}"
.text-center Order Summary :
.row
  .columns.bordered_block
    .row
      .columns
        %table
          %tbody
            %tr
              %td State :
              %td= @order.customer_state
            %tr
              %td Item Total :
              %td= get_price_with_symbol(@order.item_total_with_subscription_fee(currency_details[:rate]), currency_details[:hex_symbol])
            %tr
              %td Discounts :
              %td.green-discount= "- " + get_price_with_symbol(@order.total_discount_currency(currency_details[:rate]), currency_details[:hex_symbol])
            
            - if @order.subscription_fee > 0
              - subscription_plan = @order.get_subscription_plan
              %tr
                %td Discount applied due to Membership :
                %td.green-discount=  "- " + get_price_with_symbol(@order.subscription_fee_currency(currency_details[:rate]), currency_details[:hex_symbol])
              %tr
                %td #{subscription_plan[:sub_title]} :
                %td= - get_price_with_symbol(@order.subscription_fee_currency(currency_details[:rate]), currency_details[:hex_symbol])
            - addons = @order.addons_total(currency_details[:rate])
            - if addons > 0
              %tr
                %td Customisation :
                %td= get_price_with_symbol(addons, currency_details[:hex_symbol])
            %tr
              - shipping_cost = @order.shipping_currency(currency_details[:rate])
              - shipping_cost += @order.express_delivery_currency(currency_details[:rate]) if @order.express_delivery?
              %td Shipping Charges :
              %td= get_price_with_symbol(shipping_cost, currency_details[:hex_symbol])
            - if @order.cod?  
              %tr
                %td COD Charges :
                %td= get_price_with_symbol(@order.cod_charge_currency(currency_details[:rate]), currency_details[:hex_symbol])
            - if (order_addon = @order.order_addon).present?
              %tr
                %td Gift Wrap Charges :
                %td= get_price_with_symbol(@order.order_addon.gift_wrap_price_currency(currency_details[:rate]), currency_details[:hex_symbol])
            - if @order.platform_fee > 0
              %tr
                %td Platform Fee :
                %td= get_price_with_symbol(@order.platform_fee_currency(currency_details[:rate]), currency_details[:hex_symbol])
            - if @order.total_tax > 0
              %tr
                %td #{tax_title} :
                %td= get_price_with_symbol(@order.total_tax_currency(currency_details[:rate]), currency_details[:hex_symbol])
            %tr
              %td Total :
              %td= get_price_with_symbol(@order.total_currency(currency_details[:rate]), currency_details[:hex_symbol])
            -if @order.state == 'dispatched'
              %tr
                %td Courier Company :
                %td= @order.courier_company
              %tr
                %td Tracking Number :
                %td= @order.tracking_number
    - if @order.subscription_fee_applied? && @order.user.active_subscription.present?  
      - active_subscription = @order.user.active_subscription
      .row
        .columns
          .success-message-container.text-center
            .alert-box.radius.success.fade-in
              %span.emoji 🎉
              %b Congratulations!
              %span Your #{active_subscription.plan_name} Membership is active and the platform fee has been waived off. Valid till #{active_subscription.end_date.strftime('%d %b %Y')}.

      - if RAKHI_PRE_ORDER[0] == 'true'
        .columns
          - rakhi_pre = @order.rakhi_pre_order
          - if @order.country == 'India' && rakhi_pre['rakhi_with_other_designs'].present?
            .panel.callout.radius= "Your Order has multiple items, only Rakhi will be delivered between #{RAKHI_PRE_ORDER[1]}."
          - elsif rakhi_pre['rakhi_all_schedule'].present? || rakhi_pre['rakhi_with_other_designs'].present?
            .panel.callout.radius= "Your order will be delivered between #{RAKHI_PRE_ORDER[1]}."

:javascript
  $(document).ready(function(){
    $(".success-message-container").delay(8000).fadeOut("slow");
  });