.text-center Order Details :
.row
  .columns.bordered_block
    - @order.designer_orders.each do |designer_order|
      .row
        .columns
          .panel_block.designer-order-block
            .panel_heading
              .text-center= designer_order.designer.name
            .panel_content
              -if show_domestic_pipeline
                .row
                  =render :partial => '/orders/domestic_order_status',locals: {designer_order: designer_order}
              -else
                .row
                  .columns
                    - if retry_state == false
                      = designer_order.state.try(:humanize)
                .row
                  .columns
                    = designer_order.tracking_num
              .row
                .columns
                  %ul.small-block-grid-2.medium-block-grid-3.large-block-grid-4
                    - @total_addons_price = 0
                    - designer_order.line_items.each do |line_item|
                      - original_price = line_item.other_details['original_price']
                      - total_original_price = nil
                      - if original_price.present?
                        - original_price = (original_price / @order.currency_rate).round(2)
                        - total_original_price = original_price * line_item.quantity 
                      %li
                        = link_to designer_design_path(line_item.design.designer, line_item.design), target: '_blank' do
                          - if line_item.design.master_image.present?
                            = image_tag(IMAGE_PROTOCOL + line_item.design.master_image.photo(:small), alt: line_item.design.title, class: 'designer_order_image')
                          - else
                            = image_tag('default_image.jpg', alt: line_item.design.title, class: 'designer_order_image')
                        - display_price = (original_price || line_item.snapshot_price_currency(currency_details[:rate]))
                        -if @track
                          .line_item_details.panel{ unbxdattr:"order", unbxdparam_sku:line_item.design.id, unbxdparam_price:line_item.design.price, unbxdparam_qty:line_item.quantity }
                            .truncate= line_item.design.title
                            .text-center= get_price_with_symbol(display_price, currency_details[:hex_symbol])
                            .text-center= "Quantity : #{line_item.quantity}"
                            .text-center= "Notes : #{line_item.note_without_bmgn}" if line_item.note_without_bmgn.present?
                            .text-center= "Total : #{get_price_with_symbol(line_item.total_currency(currency_details[:rate]), currency_details[:hex_symbol])}"
                        -else
                          .line_item_details.panel
                            .truncate= line_item.design.title
                            .text-center= get_price_with_symbol(display_price, currency_details[:hex_symbol])
                            .text-center= "Quantity : #{line_item.quantity}"
                            .text-center= "Notes : #{line_item.note_without_bmgn}" if line_item.note_without_bmgn.present?
                            .text-center= "Total : #{get_price_with_symbol(total_original_price || line_item.total_currency(currency_details[:rate]), currency_details[:hex_symbol])}"
                        - if (@addons = get_addons(line_item.id))
                          - @line_item = line_item
                          = render :partial => 'orders/order_addon', locals: {line_item: @line_item}
                        -if !@order.international? && line_item.return.present?
                          %button#return-track-button{href: '#', "data-reveal-id" => 'returnStatusModal' } Track Return
                          #returnStatusModal.reveal-modal{"data-options" => "closeOnBackgroundClick:true", "aria-hidden" => "true", "aria-labelledby" => "modalTitle", "data-reveal" => "", role: "dialog"}
                            %a.close-reveal-modal{"aria-label" => "Close" , style:"text-align:right", href: "#", role: "button"} &#215
                            %h4
                              .reveal-model.modal-title{style:'font-size:large; font-weight:600; text-align:center'}
                                Return Status
                                %hr
                                .reveal-model.modal-body
                                  = render :partial => '/line_items/return_status', locals: {line_item: line_item}
              .row
                / - if @total_addons_price != 0 && @cart.mirraw_payable_addons?
                /   .notice_class= "*Due to Festival Rush, estimated time to deliver stitched product is 25 days from date of order."