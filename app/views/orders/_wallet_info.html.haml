- content_for :page_specific_css do
  = stylesheet_link_tag 'wallet'
.row
  -used_return_checked = cart.wallet.present? && cart.wallet_details(@country_code)[:return_amount] > 0
  -user_return_amount = @user.try(:wallet).try(:return_amount).to_f
  - total = 0 if total < 0
  -if (user_return_amount > 0 && total > 0) || used_return_checked
    Wallet :
    .payment_options.bordered_block.columns
      .row
        .columns.order_wallet
          %label
            #wallet_payment_option.wallet_message{data: {total: total, referral_amount: referral_amount, cod_charges: cod_charges, shipping: shipping, symbol: get_symbol_from(@hex_symbol) }}
              =check_box_tag 'wallet_return', 'use_return',used_return_checked
              #show_used_wallet_message.message{style: "display:#{used_return_checked ? 'block' : 'none'}"}
                ="#{get_price_with_symbol(cart.wallet_details(country_code)[:return_amount], @hex_symbol)} will be used for payment."
              #show_unused_wallet_message.message{style: "display:#{used_return_checked ? 'none' : 'block'}"}
                ="#{get_price_with_symbol(user_return_amount, @hex_symbol)} available for payment."
            #wallet_cant_be_used_for_cod{style:"display:none;"}
              Wallet cannot be used for COD orders.
            #wallet_cant_be_used_for_express_delivery{style:"display:none;"}
              Refund Wallet amount cannot be used for Express Delivery orders.
  #wallet_error{style:"color:red !important; display:none;"}

- is_browser_not_supported =  opera_mini_browser? || uc_browser?
:javascript
  function setWalletDiscount(){
    var data, element;
    element = $('#wallet_return');
    data = {
      no_template: true,
      cart_id: "#{cart.id}",
      location: {
        billing: {
          pincode: "#{billing_address.pincode}",
          country: "#{billing_address.country}",
          id: "#{billing_address.id}"
        },
        shipping: {
          pincode: "#{shipping_address.pincode}",
          country: "#{shipping_address.country}",
          id: "#{shipping_address.id}"
        }
      }
    };
    if ((element.prop('checked')) && ($('input[name="order[pay_type]"]:checked').attr('id') !== 'order_pay_type_cash_on_delivery')){
      return $.ajax({
        data: data,
        method: 'PUT',
        url: '/api/v1/user/wallets/apply_wallet_refund',
        beforeSend: function(){
           $(".message").text("Please Wait...");
        },
        success: function(response) { 
          $('.cod_charges').hide()
          if (response){
            if ("#{is_browser_not_supported.to_s}" == "true"){
              window.location.reload();
            }
            else{
              grand_total = parseFloat(response.total);
              if ($('#foo').length > 0) {
                grand_total += $('#foo').data('lat');
              }
              if ($('#cashondelivery').is(":checked")){
                WALLET.methods.UncheckRadioButtons()
              }
              discount =  parseFloat(response.discount)
              wallet_discount = parseFloat(response.wallet_discounts.total)
              wallet_return_discount =  parseFloat(response.wallet_discounts.return_amount)
              if (grand_total <= 0){
                WALLET.methods.UncheckAccordian()
                WALLET.methods.UncheckRadioButtons()
              }
              selected_option = $('[name=payment_option]').filter(":checked").lenght > 1 ? true : $(".accordion-navigation.active").length > 1;  
              if (selected_option && !$('#cashondelivery').is(":checked")){
                grand_total -= gon.prepaid_discount 
              }
              if(grand_total < 0){
                grand_total = 0
              }
              $('#grand_total_without_cod').text("Grand Total : #{get_symbol_from(@hex_symbol)}"+ grand_total.toFixed(2));
              $('#grand_total_without_cod').attr('value', grand_total.toFixed(2));
              $('#you_pay_grand_total').text("#{get_symbol_from(@hex_symbol)} "+ grand_total.toFixed(2));
              $('#grand_total_without_cod').show()
              $('.grand_total_with_cod').hide()
              if (wallet_discount > 0){
                $('#wallet_discount_order_page').show();
                $('#wallet_discount_order_page').text("Wallet Discount : #{get_symbol_from(@hex_symbol)}"+ wallet_discount);
              }
              if (discount > 0){
                $('#discount_order_page').text("Discount : #{get_symbol_from(@hex_symbol)}"+ discount);
                $('#discount_order_page').css('text-align','right');
              }
              wallet_text = $('#show_used_wallet_message');
              wallet_text.text("#{get_symbol_from(@hex_symbol)}"+wallet_return_discount+" will be used for payment.");
              wallet_text.show();
              $('#show_unused_wallet_message').hide();
              WALLET.methods.updateGrandTotal(grand_total)
              if(!response.total > 0 && gon.country_code != 'IN'){
                WALLET.methods.enablePlaceOrderButton();
              }
              WALLET.methods.togglePrepaidPromotion()
            }
          }
          else{
            $('#wallet_error').show();
            wallet_error(element);
          }
        },
        error: function(error) {
          $('#wallet_error').show();
          wallet_error(element);
        }
      });
    } else {
      return $.ajax({
        data: data,
        method: 'PUT',
        url: '/api/v1/user/wallets/remove_wallet_refund',
        beforeSend: function(){
           $(".message").text("Please Wait...");
        },
        success: function(response) {
          if(gon.country_code != 'IN'){
            ACCORDIAN.methods.activateAccordion(".pay_online")
          }
          if (response){
            if ("#{is_browser_not_supported.to_s}" == "true"){
              window.location.reload();
            }
            else{
              grand_total = parseFloat(response.total);
              if ($('#foo').length > 0) {
                grand_total += $('#foo').data('lat');
              }
              discount =  parseFloat(response.discount)
              wallet_discount =  parseFloat(response.wallet_discounts.total)
              $('#grand_total_without_cod').text("Grand Total : #{get_symbol_from(@hex_symbol)}"+ grand_total.toFixed(2));
              $('#grand_total_without_cod').attr('value', grand_total.toFixed(2));
              $('#you_pay_grand_total').text("#{get_symbol_from(@hex_symbol)}"+ grand_total.toFixed(2));
              $('#wallet_discount_order_page').text("Wallet Discount: #{get_symbol_from(@hex_symbol)}"+ wallet_discount.toFixed(2));
              if (discount > 0){
                $('#discount_order_page').text("Discount : #{get_symbol_from(@hex_symbol)}"+ discount.toFixed(2));
                $('#discount_order_page').css('text-align','right');
              }
              wallet_text = $('#show_unused_wallet_message');
              wallet_text.text("#{get_symbol_from(@hex_symbol)}#{user_return_amount} is available for payment.");
              wallet_text.show();
              $('#show_used_wallet_message').hide();
                WALLET.methods.updateGrandTotal(grand_total)
            }
            WALLET.methods.togglePrepaidPromotion()
          }
          else{
            $('#wallet_error').show();
            wallet_error(element);
          }
        },
        error: function(error) {
          $('#wallet_error').show();
          wallet_error(element);
        }
      });
    }
  }
  afterWindowOrTrubolinksLoad(function(){
    WALLET.methods.togglePrepaidPromotion()
  })