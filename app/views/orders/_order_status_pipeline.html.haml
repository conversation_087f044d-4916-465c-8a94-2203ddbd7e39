- if browser.opera? && browser.version.to_i <= 16
  - opera_browser = true
  -class_style = 'square_stage'
- else
  -class_style = 'circle_stage'
.text-center Order Status :
.row
  .columns.bordered_block
    .row
      - if (['new','pending','fraud'].include? @order.state)
        -stages = ['Order Placed-yellowgreen','Order Confirmed-#D4A025-order_confirmed','In Warehouse-white',"Quality Check-white",'Ready To Ship-white','Order Dispatched-white']
      - else
        -stages=@order.order_status(@all_line_items)

      .row#order_status
        -stages.each_with_index do |status,index|
          -values = status.split('-')
          #circle_div
            - if values[2].present?
              - circle_width = 50
              - margin_left = 30
              - margin_top = 0
            - else
              - circle_width = 25
              - margin_left = 43
              - if opera_browser
                - margin_top = -8
              - else
                - margin_top = -13
            %span.span-24{class: "#{class_style}",style: "width:#{circle_width}px;height:#{circle_width}px;background-color:#{values[1]};margin-left: #{margin_left}px;"}
              - if values[2].present?
                %span
                  = image_tag("#{values[2]}.png", :alt => "#{values[0]}", :class => "step_image", :width => "36", :height => "36")
              - else
                .step_number=(index+1)
            -unless stages.count == index+1
              %span.vertical_line{style:"background-color:#{values[1] == '#D4A025' ? 'white' : values[1]};margin-top: #{margin_top}px;"}
          .row#notes_div
            - color = (cookies[:theme] == 'black_theme') ? values[1] :  (values[1] == 'white' ? '#383737' : values[1])
            .span.status_note{style: "color: #{color};"}
              %b=values[0]
              %br
              -if values[1] == 'yellowgreen'
                done
              -elsif values[1] == 'white'
                pending
              -else
                processing
