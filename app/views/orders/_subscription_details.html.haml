- waived_off_platform_fee = @cart.get_platform_fee(@country_code,'mobile',@rate, false, false)
.subscription-container{:data => {:subscription_plan_id => @subscription_plan.id}}
  .subscription-header
    %input{:type => "checkbox", :id => "subscription_enabled", :name => "enable_subscription", :checked => true}
    %label{:for => "subscription_enabled"}
    %h3 #{@subscription_plan.name}
    %span.saved-label You Saved #{get_price_with_symbol(waived_off_platform_fee, @hex_symbol)}
  .subscription-details
    %p
      #{@subscription_plan.description}
      - if @subscription_plan.read_more_link.present?
        = link_to "Read More", @subscription_plan.read_more_link, target: "_blank"
    -# .saved-amount #{get_price_with_symbol(waived_off_platform_fee, @hex_symbol)}
  .breakdown-link
    %a.toggle-breakdown{:href => "javascript:void(0);"} View Breakdown
  .breakdown-details
    %table
      %tr
        %td Products
        %td.price #{get_price_with_symbol(item_total, @hex_symbol)}
      - if discounts > 0
        %tr
          %td Discount
          %td.price.discount - #{get_price_with_symbol(discounts, @hex_symbol)}
      %tr
        %td Discount applied due to Membership
        %td.price.discount - #{get_price_with_symbol(@subscription_plan.price, @hex_symbol)}
      %tr
        %td
          Membership Fee - (#{@subscription_plan.name} - #{@subscription_plan.get_plan_date_in_days} days)
          %span.membership-fee-description #{@subscription_plan.membership_description}
        %td.price #{get_price_with_symbol(@subscription_plan.price, @hex_symbol)}
      %tr
        %td Customisation
        %td.price #{get_price_with_symbol(addons_charges, @hex_symbol)}
      %tr
        - if shipping_charges >= 0 || @cart.ready_to_ship_designs?
          %td Shipping
          %td.price #{get_price_with_symbol(shipping_charges, @hex_symbol)} 
      - if session[:gift_wrap] && GIFT_WRAP_PRICE.to_f >= 0
        %tr
          %td Gift Wrap Charges
          %td.price #{get_price_with_symbol(get_price_in_currency(GIFT_WRAP_PRICE.to_f), @hex_symbol)}
      - if tax_details.present? && tax_details[:tax_enable] 
        %tr
          %td #{tax_title}
          %td.price #{get_price_with_symbol(tax_details[:tax_amount], @hex_symbol)}
      %tr.final-payable
        %td Final Payable
        %td.price #{get_price_with_symbol(grandtotal,@hex_symbol)}