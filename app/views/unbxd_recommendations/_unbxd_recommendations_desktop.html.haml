.container.recommended-design-box{id: response['boxType'].downcase}
  .title-block
    %h1= response['boxType'].titleize
  .row
  
    - if response['Recommendations'].present?
      .recommended-products{id: "box-#{response['boxType'].downcase}"}
      .home-unbox-slider
        .foo-slider.blaze-slider.unbxd-blaze-container
          .blaze-container
            .blaze-prev
              %button.prev-btn{type: "button"} &#x2039;
            .blaze-next
              %button.next-btn{type: "button"} &#x203A;
            .blaze-track
              - response['Recommendations'].each.with_index(1) do |design, index|
                .blaze-item
                  - ga_data = {id: "#{design['uniqueId']}", name: "#{design['uniqueId']}_", category: design['category_name'], brand: design['designer'], list: "Widget / Unbxd / #{response['boxType'].downcase}", position: index}.to_json.html_safe
                  .product-list.ga_design_container{id: "unbxd_#{design['uniqueId']}", unbxdattr: 'product', unbxdparam_sku: design['uniqueId'], unbxdparam_prank: index, unbxdParam_boxType: response['oldBoxType'], data: {id: design['uniqueId'], brand: design['designer'], box_type: "Unbxd-#{response['boxType'].downcase}", price: design['discount_price'], ga_data: ga_data, ga_pending: true}}
                    - image_path = design['imageUrl'] || asset_path('default_image.jpg')
                    = link_to designer_design_path(design['designer_slug'], design['design_slug'], ici: "#{response['boxType']}_mo", icn_term: "recommend_#{Date.today.strftime('%m_%d_%Y')}" ), id: design['uniqueId'], class: 'unbxd-product-list' do
                      .product-box
                        .image-box
                          %img.error_img.js-lazy{alt: "#{design['title']}", src: BASE_64_PLACHOLDER_IMAGE, data: {original: IMAGE_PROTOCOL + image_path}}
                          - if RATING_ENABLE
                            -average_rating = design['rating'].to_f
                            -color_condition = {one: average_rating < 2.0, two: (average_rating >= 2.0 && average_rating < 3.5), three:  average_rating >= 3.5}
                            .rating_div.catalog-rating
                              -if (average_rating > 0.0)
                                %span.small_rating{class: [('red-rating' if color_condition[:one]), ('orange-rating' if color_condition[:two]), ('green-rating' if color_condition[:three])]}
                                  ='★'+average_rating.round(1).to_s
                              -else
                                %span.small_rating
                                  ='New'
                        .panel.design_desc
                          .truncate{style: "font-size:0.8rem !important;"}
                            = design['title'].titleize
                          .row
                            .price-block="#{@symbol} #{is_domestic? ? design['discount_price'].to_i : design['discount_price']}"
    - else
      .no-design-box{id: "no-#{response['boxType'].downcase}"} Sorry Not Able to Find Recommendations

:javascript
  lazyLoad()
  if (typeof(Turbolinks) != 'undefined') {
      MR.product.unbxdSwiperRecommendation();
    }
    document.addEventListener('turbolinks:load', function() {
      MR.product.unbxdSwiperRecommendation();
    });
