- count = 1
- wishlist_page ||= false
- designs ||= bestseller_designs
- designs.each do |design|
  - if design.present? && design.state == 'in_stock'
    %li
      - ga_data = design.ga_data(dimension2: cookies[:theme], dimension6: design.experiment_id(session[:exp_category]), position: count, list: @ga_list).to_json.html_safe
      .fr_page.ga_design_container{data: {ga_data: ga_data, ga_pending: true}}
        - discount = design.effective_discount(PromotionPipeLine.active_promotions).to_i
        = link_to 'X', user_wishlists_path(design_id: design.id), method: :delete, class: 'wishlist-remove' if wishlist_page
        %a{href: "#{designer_design_path(design.designer.cached_slug, design.cached_slug)}?icn=#{params[:action]}_mobile&ici=bestsellers&page_type=detail", data: {turbolinks: 'false'}}
          - if discount > 0 && cookies[:theme] == 'red_theme' && !wishlist_page
            .design-col2.details_block= "#{discount}% OFF"
          .plp-image-boxs  
            %div{class: "image_placeholder design_#{(count)%9}"}
              - if LAZY_IMAGE_SCROLL
                = image_tag(BASE_64_PLACHOLDER_IMAGE, alt: "#{design.title}", width: 151, height: 173, class: 'js-lazy', data: {original: IMAGE_PROTOCOL + design.master_image.photo(:long)})
              - else
                = image_tag(BASE_64_PLACHOLDER_IMAGE, alt: "#{design.title}", width: 151, height: 173, class: 'lazy', data: {src: IMAGE_PROTOCOL + design.master_image.photo(:long)})
        - if discount > 0 && cookies[:theme] != 'red_theme'
          .design-col2.details_block= "#{discount}% OFF"
        .design_desc
          .row.collapse
            .small-10.columns
              .truncate
                %a{href: "#{designer_design_path(design.designer.cached_slug, design.cached_slug)}?icn=#{params[:action]}_mobile&ici=bestsellers"}
                  = design.title.titleize
            .small-2.columns
              =render partial: '/wishlists/wishlist_forms', locals: {design: design} unless wishlist_page
          .design-col1.details_block
            = "#{get_symbol_from(@hex_symbol)}#{number_with_delimiter(design.effective_price_currency(@rate).to_s)}"
            - if discount > 0
              .actual_price.wishlist-price #{get_symbol_from(@hex_symbol)}#{design.price_currency(@rate)}
              .discount-wishlist= "#{discount}% OFF"
          - unless params[:api_layout] == 'true'
            .add-to-cart-bst.button.tiny.add_to_cart_link.red-button{unbxdattr: "AddToCart", unbxdparam_qty: "1", unbxdparam_sku: "#{design['id']}", unbxdparam_requestId: session[:unbxdparam_requestId], data: {id: design.id}}
              = 'ADD TO CART'
    - count = count+1