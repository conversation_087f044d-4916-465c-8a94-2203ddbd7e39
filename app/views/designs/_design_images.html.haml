- show_stitching_label = STITCHING_LABEL_DESIGNABLE_TYPE.to_a.include?(@design['designable_type']) && stitching_label.present?
.design-breadcrumb.pdp-page-breadcrumb
  = render partial: 'sitemaps/breadcrumb', locals: {breadcrumb: @breadcrumbs}
-if opera_mini_browser?
  %span.opera_prev.opera_arrows &larr;
  %span.opera_next.opera_arrows &rarr;
  %div#design_images_opera
    - if images.present? && images.length > 0
      - images.each do  |designImage|
        - if show_stitching_label
          %span{style: 'background: red'}=stitching_label.titleize
        - if designImage['sizes']
          %div
            %img.design_image{ src: IMAGE_PROTOCOL + designImage['sizes']['large_desktop'], alt: @design['title']}
        - else
          %div
            %img.design_image{ src: asset_path('default_image.jpg'), alt: "Mirraw Logo"}
    - else
      %div
        %img.design_image{ src: asset_path('default_image.jpg') , alt: "Mirraw Logo"}   
-else
  .main-flex-item-1.desk_web
    #desktopLightboxGalleryContainer.desktop-view-product-slider-wrapper
      .container-fluid
        .row
          -if true
            .small-12.medium-12.large-12.columns.nopadding.large_device_img_div
              .large_device_img
                .small-2.medium-2.sub-image.columns.thumb_image_div
                  #design_gallery
                    #gal1.thumb_images
                      - images.each_with_index do  |designImage, index|
                        - if designImage['sizes']
                          - original =  designImage['sizes']['original'].empty? ? asset_path('default_image.jpg') : designImage['sizes']['original']
                          - thumb = designImage['sizes']['thumb'].empty? ? asset_path('default_image.jpg') : designImage['sizes']['thumb']
                        -if designImage['sizes'].key?("video_src")
                          - designImageFirst = images[0]
                          - thumb =  designImageFirst['sizes']['original'].empty? ? asset_path('default_image.jpg') : designImageFirst['sizes']['thumb']
                          #play_video.play_button.fancybox-thumbs{style: "position: relative", "data-reveal-id" => "videoModal"}
                            %img#image_thumbnail.videos{alt: @design['title'], remote: "true", src: IMAGE_PROTOCOL + thumb, :id => 'image_thumbnail_' + IMAGE_PROTOCOL + thumb}/
                            %span.play_button{style: "bottom: 0px"}
                              %svg.play_button{height: "100", viewbox: "0 0 100 100", width: "100", xmlns: "http://www.w3.org/2000/svg"}
                                %path{d: "M85.527 80.647a4.971 4.971 0 0 0 4.973-4.974V24.327a4.971 4.971 0 0 0-4.973-4.974H14.474A4.972 4.972 0 0 0 9.5 24.327v51.346a4.972 4.972 0 0 0 4.974 4.974h71.053zm-4.974-9.948H19.446V29.301h61.107v41.398z", fill: "currentColor"}
                                %path{d: "m64.819 50.288l-11.98 6.913l-11.974 6.917V36.462l11.974 6.918z", fill: "currentColor"}
                          #videoModal.reveal-modal.small.fade{"aria-hidden" => "true", "aria-labelledby" => "videoModalTitle", "data-reveal" => "", role: "dialog"}
                            .flex-video.widescreen.vimeo
                              %video{controls: "", width: "930", height: "350", muted: "", class: "video", autoplay: ""}
                                %source{src: designImage['sizes']['video_src'], type: "video/mp4"}
                              -# .amount
                              -#   .discount_old_price.floatl.video-box
                              -#     - if @design['discount_percent'] > 0
                              -#       .floatl.video-price= get_price_with_symbol(@design['discount_price'], @design['symbol'])
                              -#       .old_price_label.video-price.floatl=get_price_with_symbol(@design['price'], @design['symbol'])
                              -#       .discount_percent.floatl.video-price #{@design['discount_percent']}% off

                            %a.close-reveal-modal{"aria-label" => "Close", href: "#", role: "button"} ×
                        -else
                          %a.fancybox-thumbs{"data-fancybox-group" => "thumb", "data-image" => IMAGE_PROTOCOL + original, "data-image-id" => IMAGE_PROTOCOL + original, "data-zoom-image" => IMAGE_PROTOCOL + original,  "aria-label"=> "View larger version of this image", href: "#", role: "button"}
                            %img#image_thumbnail_10011963{alt: @design['title'], src: IMAGE_PROTOCOL + thumb}
                .small-10.medium-9.large-10.columns.large_img_div{style: "padding: 0px;"}
                  .large_img
                    - if STITCHING_LABEL_DESIGNABLE_TYPE.include?(@design['designable_type']) && stitching_label.present?
                      .label_for_image_box
                        .label_text= stitching_label.titleize
                    - elsif @design['designable_type'] == 'Kurti' && @design['package_details'].try(:gsub, /[^\d]/, '').try(:length) == 1
                      .label_for_image_box
                        .label_text Top Only
                    - designImage = images[0]
                    - original = designImage.present? && !designImage['sizes']['original'].empty? ? designImage['sizes']['original'] : asset_path('default_image.jpg') 
                    %img#master.lazyload.cmn-img-fluid.p-img.p-medium-img{alt: @design['title'], "data-id" => IMAGE_PROTOCOL + original, "data-zoom-image" => IMAGE_PROTOCOL + original, itemprop: "image", src: IMAGE_PROTOCOL + original}/
                    %ul
                      %li{unbxdattr: "product", unbxdparam_prank: "1", unbxdparam_sku: @design['id']}
                    .clr
                .img_text.small-12.medium-9.large-10.columns Disclaimer: Slight variation in actual color vs. image is possible due to the screen resolution.
  
          -else
            - images.each_with_index do  |designImage, index|
              - if designImage['sizes']
                - original =  designImage['sizes']['original'].empty? ? asset_path('default_image.jpg') : designImage['sizes']['original']
                - thumb = designImage['sizes']['thumb'].empty? ? asset_path('default_image.jpg') : designImage['sizes']['thumb'] 
                - large = designImage['sizes']['large_desktop'].empty? ? asset_path('default_image.jpg') : designImage['sizes']['large_desktop']
              - if index == 0
                .col-12.columns.large-12
                  .product-img-box.big
                    %span.zoomImgWrap.zoomImgSelectorContainer
                      %img.lazyload.cmn-img-fluid.p-img.p-medium-img{:alt => "Mirraw", :src =>IMAGE_PROTOCOL + original}/
              -else
                .col-6.columns.large-6
                  .product-img-box.medium
                    %span.zoomImgWrap.zoomImgSelectorContainer
                      -if index == 1 && designImage['sizes'].key?("video_src")
                        %video{:class =>"design_video " , :id=>"product_video" ,:controls => "", :height => 300, :loop => "", :width => 300, :muted => "",:playsinline => "", preload: 'metadata', :autoplay => ""}
                          %source.design_image{id:"product_video_src" ,src: designImage['sizes']['video_src'], data: {src:designImage['sizes']['video_src'],index: index} ,type:"video/mp4",}
                      -else
                        %img.lazyload.cmn-img-fluid.p-img.p-medium-img{:alt => "Mirraw", :src => IMAGE_PROTOCOL + large}/
  .image-slider.m_web.swiper.ProductSwiperContainer{:style => "position: relative;max-height: 32rem;"}
    - if UNBXD_V2_2023
      %img.image_modal.recommendations_modal_btn{:alt => "Recommendations",:src => "#{asset_path('similar_recommendations_btn.png')}"}
    %ul#design_images.gallery.list-unstyled.ProductSwiperContainer.swiper-wrapper{style: ('min-height: 406px' if images.length > 1 )}
      - if images.present? && images.length > 0
        - images.each_with_index do  |designImage, index|
          - if designImage['sizes']
            - original =  designImage['sizes']['original'].empty? ? asset_path('default_image.jpg') : designImage['sizes']['original']
            - original_webp =  designImage['sizes']['original'].empty? ? asset_path('default_image.jpg') :  webp_format_image(designImage['sizes']['zoom'])
            - thumb = designImage['sizes']['thumb'].empty? ? asset_path('default_image.jpg') : designImage['sizes']['thumb']
            - large = designImage['sizes']['large_desktop'].empty? ? asset_path('default_image.jpg') : designImage['sizes']['large_desktop']
            %li.swiper-slide{:data => {thumb: IMAGE_PROTOCOL + thumb}, style: 'display: inline-block;'}
              .design_wrap
                - if show_stitching_label
                  .label_for_image_box
                    .label_text= stitching_label.titleize
                - elsif @design['designable_type'] == 'Kurti' && package_details.try(:gsub, /[^\d]/, '').try(:length) == 1
                  .label_for_image_box
                    .label_text Top Only
                - if index == 1 && designImage['sizes'].key?("video_src")
                  %video{:class =>"design_video " , :id=>"product_video" ,:controls => "", :height => 600, :loop => "", :width => 350, :muted => "",:playsinline => "", preload: 'metadata', :autoplay => ""}
                    %source.design_image{id:"product_video_src" ,src: designImage['sizes']['video_src'], data: {src:designImage['sizes']['video_src'],index: index} ,type:"video/mp4",}
                - elsif index == 0
                  -if original_webp.size > 1
                    %picture
                      %source#myImg{:srcset => IMAGE_PROTOCOL + original_webp, :type => "image/webp", :alt => "#{@design['title']}"}
                      %img#myImg.design_image{src: IMAGE_PROTOCOL + original_webp, :alt => "#{@design['title']}", data: {src: IMAGE_PROTOCOL + original_webp, index: index}, fetchpriority: "high"}
                  -else
                    %img#myImg.design_image{src: IMAGE_PROTOCOL + original, alt: "#{@design['title']}", data: {src: IMAGE_PROTOCOL + original, index: index}, fetchpriority: "high"}
                - else
                  -if original_webp.size > 1
                    %picture
                      %source#myImg{:srcset => IMAGE_PROTOCOL + original_webp, :type => "image/webp", :alt => "#{@design['title']}"}
                      %img#myImg.design_image{src: IMAGE_PROTOCOL + original_webp, :alt => "#{@design['title']}", data: {src: IMAGE_PROTOCOL + original_webp, index: index}, fetchpriority: "high"}
                  -else
                    %img#myImg.design_image.js-lazy{src: 'data:image/gif;base64,R0lGODlhAQABAIAAAAUEBAAAACwAAAAAAQABAAACAkQBADs=', alt: "#{@design['title']}", data: {src: IMAGE_PROTOCOL + original, original: IMAGE_PROTOCOL + original, index: index}, fetchpriority: "low"}
          - else
            %li{ href: asset_path('default_image.jpg') }
              %img.design_image{ src: asset_path('default_image.jpg') , alt: "Mirraw Logo", fetchpriority: "high"}
      - else
        %li{ href: asset_path('default_image.jpg') }
          %img.design_image{ src: asset_path('default_image.jpg') ,  alt: "Mirraw Logo", fetchpriority: "high"}
    .swiper-pagination
  %div.tap_text      
    %small Tap on Image to Zoom

/ Photo Swipe slider 
.pswp{"aria-hidden" => "true", :role => "dialog", :tabindex => "-1"}
  .pswp__bg
  .pswp__scroll-wrap
    .pswp__container
      .pswp__item
      .pswp__item
      .pswp__item
    .pswp__ui.pswp__ui--hidden
      .pswp__top-bar
        .pswp__counter
        %button.pswp__button.pswp__button--close{:title => "Close (Esc)"}
        %button.pswp__button.pswp__button--share{:title => "Share"}
        %button.pswp__button.pswp__button--fs{:title => "Toggle fullscreen"}
        %button.pswp__button.pswp__button--zoom{:title => "Zoom in/out"}
        .pswp__preloader
          .pswp__preloader__icn
            .pswp__preloader__cut
              .pswp__preloader__donut
      .pswp__share-modal.pswp__share-modal--hidden.pswp__single-tap
        .pswp__share-tooltip
      %button.pswp__button.pswp__button--arrow--left{:title => "Previous (arrow left)"}
      %button.pswp__button.pswp__button--arrow--right{:title => "Next (arrow right)"}
      .pswp__caption
        .pswp__caption__center