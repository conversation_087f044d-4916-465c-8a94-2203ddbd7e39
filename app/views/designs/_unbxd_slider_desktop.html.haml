- count = 1
.home-unbox-slider
  .foo-slider.blaze-slider.blaze-container-class.unbxd-blaze-container
    .blaze-container
      .blaze-prev
        %button.prev-btn{type: "button"} &#x2039;
      .blaze-next
        %button.next-btn{type: "button"} &#x203A;
      .blaze-track
        - designs.each do |design|
          .blaze-item
            - ga_data = { id: design['id'].to_s, name: "#{design['id']}_", price: design['inr_discount_price'], position: count, category: design['category_name'], list: @ga_list, brand: design['brand'] }.to_json.html_safe
            .design-slides.ga_design_container{id: "bst_#{design['id']}", data: { ga_data: ga_data, ga_pending: true }}
              - discount = design['discount_percent']
              - url = type == 'flash_deals' ? path : design['design_path']
              = link_to url, data: { turbolinks: false } do
                = image_tag(BASE_64_PLACHOLDER_IMAGE, alt: design['title'], class: 'js-lazy blaze-image', data: { original: IMAGE_PROTOCOL + design['sizes']['long_webp'] }, height: "290", width: "auto")
              .design_desc
                .row.collapse
                  .small-12.columns
                    = link_to design['design_path'] do
                      .truncate #{design['title']}
                  .small-2.columns= render partial: '/wishlists/wishlist_forms', locals: { design: design }
                .details_block.custom-home-page
                  #{get_symbol_from(@hex_symbol)}#{design['discount_price']}
                  - if design['discount_percent'] > 0
                    .actual_price #{get_symbol_from(@hex_symbol)}#{design['price']}
                  - if discount > 0
                    .custom-discount
                      .custom-design-discount= "(#{discount}% OFF)"
                  - else 
                    .custom-discount
                      .custom-design-discount= "\u00A0"
          - count = count + 1

:javascript
  if (typeof(Turbolinks) != 'undefined') {
    MR.blazeSlider.initializeSwiper();
  }
  document.addEventListener('turbolinks:load', function() {
    MR.blazeSlider.initializeSwiper();
  });