#modal-size-chart
  .modal-dialog.modal-sm-size
    .modal-content
      .btn_close_top.button{onClick: 'closeSizeChart()'} &times;
      .modal-header{style: 'text-align: center; padding: 10px'}
        %h4#exampleModalLabel.modal-title
          - unless size_chart['is_variant']
            = "#{@design['designable_type'].underscore.titleize}"
          Size Chart
      .modal-body{style: "text-align: -webkit-center; clear: both"}
        - unless size_chart['is_variant']
          .row.size-label Ready-Made Measurements
          .row.size-label All values in inches-#{@design['designable_type'].underscore.titleize}-(Ready garment may have 0.5 to 1 inch difference)
        .size-box-modal  
          .row.table-responsive.table-inches
            %table.table.table-size-bordered.std_size_chart
              %thead
                %tr
                  -if @design['designable_type'] == 'Lehenga'
                    %th.head_style Size
                    -size_chart["heads"].each do |head|
                      %th.head_style= head
                  -else
                    %th.head_style Bust Size
                    -size_chart["heads"].each do |head|
                      %th.head_style= head
              %tbody{style:'text-align: center;'}
                -  size_chart["Sizes"].each do |size|
                  - next if !size_chart['is_variant'] && availabel_sizes.exclude?(size['size'].to_s) && !@design['designable_type'].to_s.downcase == "saree" 
                  %tr
                    %td.head_style= "#{size['size']}"
                    -size['columns'].count.times do |v|
                      %td.head_style= size['columns'][v]
          - if size_chart['image'].present?
            .size_chart_modal_image
              %img{src: IMAGE_PROTOCOL + size_chart['image']}
          - elsif false#'SalwarKameez' == @design['designable_type']
            .size_chart_modal_image
              =image_tag("kameez.jpg")
          - elsif 'Kurti' == @design['designable_type']
            .size_chart_modal_image
              =image_tag("stitching/kurti.jpg")