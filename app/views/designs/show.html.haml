= render 'layouts/flash_msg'
%link{:href => "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css", :rel => "stylesheet"}
=render partial: '/layouts/initialise_grading'
- unless @design.empty?
  - content_for :page_specific_css do
    = stylesheet_link_tag 'designs', rel: 'preload', as: 'style', onload: "this.onload=null;this.rel='stylesheet'"
  - if firefox_browser?
    = async_stylesheet_tag('designs')
  :javascript
    afterWindowOrTrubolinksLoad(function(event){
      loadScript('#{asset_url("social-share.js")}');
      if (window.performance && window.performance.getEntriesByType('navigation').length > 0) {
        const navEntry = window.performance.getEntriesByType('navigation')[0];
        if (navEntry.type === 'back_forward') {
          $('.addon_types').trigger('change');
        }
      }
    });
  - if opera_mini_browser?
    :javascript
      afterWindowOrTrubolinksLoad(function(){
        loadScript('#{asset_url("slider_opera.js")}')
      })
  - designable_type = @design['designable_type']
  - design_specs = DESIGN_SPEC_SUB_GROUP[designable_type].to_a
  - design_spec_hash = get_spec_data(@design['specifications'],@design['type'],design_specs)
  - package_details = @design['package_details']
  = render partial: 'seo_og'
  = render partial: 'microdata', locals: {design: @design, seo_design_spec: design_spec_hash[:seo_keys], package_details: package_details}
  - if Wallet.cashback_percent > 0
    = render :partial => '/designs/loyalty_tnc_in_detail'
  - if @design['ready_to_ship']
    = render partial: '/designs/rts_in_detail'
  - if @design['product_offer'].present? && @design['product_offer']['type'] == 'flash_deals'
    = render :partial => '/designs/flash_deals_tnc_in_detail', locals: {discount: @design['discount_percent']}
  - if PrepaidPaymentPromotion.active?(@country_code)
    = render :partial => '/designs/pd_tnc_in_detail'
  - if DOMESTIC_PREPAID_SHIPPING_PROMOTION && @country_code == 'IN'
    = render :partial => '/designs/prepaid_shipping_tnc_in_detail'
  - elsif (bmgnx_hash = PromotionPipeLine.bmgnx_hash).present?
    = render :partial => '/designs/b1g1_tnc_in_detail', locals: {bmgnx_hash: bmgnx_hash}
  - elsif @design['product_offer'].present? && @design['product_offer']['type'] == 'qpm'
    = render :partial => '/designs/quantity_discount_pomotion_tnc_in_detail'
  - if @country_code == 'IN' && SHARE_AND_EARN_REWARD > 0
    - design_link = designer_design_url(@design['designer_cached_slug'], @design['cached_slug'])
    = render :partial => '/designs/share_and_earn', locals: {design_link: design_link}
  - if Design::STITCHING_ALLOWED_DESIGNABLE_TYPES.include?( @design['designable_type'] ) && Design::UNSTITCH_POPUP_ENABLE
    = render partial: 'designs/stitching_popup_modal', locals: {addon_types: @design['addon_types']}

  -# Manipulating the code to add product_video at index = 1 in the carousel.
  - if @design['video_available'] == true
    - design_index = 0
    - video_link = Hash[ "sizes" => {"video_src" => "https://assets0.mirraw.com/product-video/#{@design['id']}.mp4" , "original" => "" , "thumb"=>"","large_desktop" => "" , "height" => "" , "width" => ""}]
    - add_video_to_carousel = Array.new( @design['images'].length + 1 )
    - for index in ( 0...add_video_to_carousel.length )
      - if index == 1
        - add_video_to_carousel[index] = video_link
      - else
        - add_video_to_carousel[index] = @design['images'][design_index]
        - design_index = design_index + 1
  %end



  - carousel_data = ( @design['video_available'] == true ) ? add_video_to_carousel : @design['images']
  .row#design_image_block
    .columns
      %ul#design_image_div.small-block-grid-1.medium-block-grid-2
        %li.design-image-container
          = render partial: 'designs/design_images', locals: {images: carousel_data, stitching_label: design_spec_hash['other'].try(:[],'stitching'), package_details: package_details}
        %li.design-content-container
          = render partial: 'designs/design_image_side_block', locals: {stitching_label: design_spec_hash['other'].try(:[],'stitching').to_s.downcase.gsub('_', ' ')}
          #action_buttons.fixed_button
            #place_holder_for_action_btns
            #add_to_cart_message.overflow_messages.fixed_cart_buttons.hidden
              Item added to cart
            = render partial: 'designs/add_to_cart_button', locals: {stitching_label: design_spec_hash['other'].try(:[],'stitching').to_s.downcase.gsub('_', ' ')}
          
          .help-box
            = render partial: 'designs/help_box'
            
          #price-match-promise
            %p
              Price Match Promise
            .price-match-content
              If you find the product for less we'll match it!
              %a{href: "/price_match"} Know More

          - if !is_mobile_view?
            .description-content
              = render partial: 'designs/designable', locals: {designable_type: designable_type, design_spec_hash: design_spec_hash}
        -# - if @design['stitching_testimonials'].present?
          -# %li#stitching_testimonials= render partial: 'designs/stitching_testimonials'
      - if @design['addon_products'].present?
        = render partial: 'designs/design_pairs'

      - if RATING_ENABLE
        .designer-panel
          .row
            .designer-title.designer-name
              Designer Name
              =link_to @design['designer']['name'].try(:titleize), "/designers/#{@design['designer_cached_slug']}"
              -designer_rating = @design['designer_rating'].to_f
              -color_condition = {one: designer_rating < 2.0, two: (designer_rating >= 2.0 && designer_rating < 3.5), three:  designer_rating >= 3.5}
              -if (designer_rating > 0.0)
                %span.small_rating{class: [('red-rating' if color_condition[:one]), ('orange-rating' if color_condition[:two]), ('green-rating' if color_condition[:three])]}
                  ='★ ' +designer_rating.round(1).to_s
              -else
                %span.small_rating New
          -if (review_text = @designer.try(:review_text)).present?
            .row
              .columns.small-12.designer-sub-panel= review_text
      - else
        .row.designer-panel
          .columns.small-12.designer-name{style: "text-align:center"}
            = link_to "More from #{@design['designer']['name'].try(:titleize)}", "/designers/#{@design['designer_cached_slug']}"
      - end_date = @design['promotion_offer_end_time'] if @design['promotion_offer_end_time'].present?

  - if !is_mobile_view?
    -if @design['state'] == 'in_stock'
      #line_items_count{pid: @design['id'], data:{quantity: @design['quantity']}}
        %span.line_items_count_text
        %span.line_item_close &#10006;
          
  - if is_mobile_view?
    .description-content
      = render partial: 'designs/designable', locals: {designable_type: designable_type, design_spec_hash: design_spec_hash}
  #unbxd-data-div{data: {pid: @design['id'], category: @design['category_name'], brand: @design['designer']['name']}}
  %br
  .pdp-whatsapp-logo
    -if ENABLE_WIZGO["enable"] == true
      = render partial: '/layouts/whatsapp_partial', locals: {text: "Hi! Could you help me with the product                                                   #{@design['design_whatsapp_url']}"}
  -if @country_code != 'IN' && !UNBXD_V2_2023
    #more_from_category_container

  - if UNBXD_V2_2023
    - if @country_code != 'IN' 
      .unbxd_row
        #unbxd_pdp_recs_container_1.unbxdProductRecommendations{:data => {"page_type": 'PRODUCT', "html_id": "unbxd_pdp_recs_container_1",  "widget": "WIDGET1" ,"id": @design['id'], "uid": cookies['unbxd.userId']}}
      .unbxd_row
        #unbxd_pdp_recs_container_2.unbxdProductRecommendations{:data => {"page_type": 'PRODUCT', "html_id": "unbxd_pdp_recs_container_2", "widget": "WIDGET2" ,"id": @design['id'], "uid": cookies['unbxd.userId']}}
    - else
      .unbxd_row
        #unbxd_pdp_recs_container_3.unbxdProductRecommendations{:data => {"page_type": 'PRODUCT', "html_id": "unbxd_pdp_recs_container_3", "widget": "WIDGET3" ,"id": @design['id'], "uid": cookies['unbxd.userId']}}
      .unbxd_row
        #unbxd_pdp_recs_container_4.unbxdProductRecommendations{:data => {"page_type": "CART", "html_id": "unbxd_pdp_recs_container_4", "widget": "WIDGET1" ,"id": @design['id'], "uid": cookies['unbxd.userId']}}

#more-items-loader

- content_for :page_specific_js_body do
  :javascript
    if (typeof(Turbolinks) != 'undefined') {
      MR.productRecommendation.initialiseRecommendations($('.unbxdProductRecommendations'))
      MR.productRecommendation.recommendationsClickAction()
      MR.product.init()
      MR.product.unbxdSwiperRecommendation();
    }
    document.addEventListener('turbolinks:load', function() {
      MR.product.init()
    });
    document.addEventListener("DOMContentLoaded", function(event){
      MR.product.init()
      MR.productRecommendation.initialiseRecommendations($('.unbxdProductRecommendations'))
      MR.productRecommendation.recommendationsClickAction()
    });
    document.addEventListener('turbolinks:load', function() {
    loadAllLazyImages(); 
    })
    if (typeof(Turbolinks) != 'undefined') {
      loadAllLazyImages(); 
    }
:javascript
  closeSizeChart = function() {
    $('body').removeClass('modal-open');
    $('body').css('overflow','auto');
    $('body').css('position','');
    $('#sizeChartModal, #dynamicSizeChartModal').foundation('reveal', 'close');
  };

  openSizeChart = function() {
    $('body').css('position','relative');
    $('body').css('overflow','hidden');
    if (!$(event.target).closest('.reveal-modal').length && !$(event.target).is('.reveal-modal')) {
      closeSizeChart();
    }
  }
- content_for :head do
  :javascript
    branchData = {
      '$deeplink_path': "product?productId=#{@design['id']}&listingType=deepLink",
      'product_id':"#{@design['id']}",
      'listingType':'deeplink',
      '~campaign':"web_design_id_#{@design['id']}"
    }
:css
  #branch-banner-iframe{
    top: 105px !important;
  }

- if @ga_hash_new.present?
  :javascript
    window.dataLayer = window.dataLayer || [];
    var gads_items_id = #{@googe_add_hash_new.to_json};
    var ga4_view_item_params = #{@ga_hash_new.to_json};
    dataLayer.push({ ecommerce: null });
    dataLayer.push({
      event: "ga4_view_item",
      ecommerce:{
      currency: "INR",
      country_code: '#{@country_code}',
      value: ga4_view_item_params.price,
      items: [ga4_view_item_params],
      item_ids: #{@googe_add_hash_new.to_json}.item_ids
      }
    });

-# :javascript
  fbq('track', 'ViewContent', {
    content_ids: [#{@design['id']}], 
    content_type : 'product', 
    content_category: '#{@design['category_name']}'
  });
