#unbxd_rec
  .title-block 
    %h1 #{details["widget_title"]}
  .item-container
    .list-item 
      - designs.each do |design|
        .design-slides{id: "bst_#{design['id']}"}
          - discount = design['discount_percent']
          - url = design['design_path']
          = link_to url, data: { turbolinks: false } do
            - if design['sizes'] && design['sizes']['long']
              %img.error_img.js-lazy{alt: "#{design['title']}", src: BASE_64_PLACHOLDER_IMAGE, data: {original: IMAGE_PROTOCOL + design['sizes']['long']}, width: "204", height: "290"}
            - else
              = image_tag('default_image.jpg', alt: design['title'])
          .design_desc
            .row.collapse
              .small-12.columns
                = link_to design['design_path'] do
                  .truncate #{design['title']}
            .details_block.custom-home-page
              #{get_symbol_from(@hex_symbol)}#{design['discount_price']}
              - if design['discount_percent'] > 0
                .actual_price #{get_symbol_from(@hex_symbol)}#{design['price']}
              - if discount > 0
                .custom-discount
                  .custom-design-discount= "(#{discount}% OFF)"
              - else 
                .custom-discount
                  .custom-design-discount= "\u00A0"
:javascript
  lazyLoad()