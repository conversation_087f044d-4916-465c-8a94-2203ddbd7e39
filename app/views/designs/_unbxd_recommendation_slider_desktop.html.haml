.widget-title
  - widget_title = details["widget_title"]
  .design_blocks
    .row.widget-title-recs
      #{widget_title}
    .home-unbox-slider
      .foo-slider.blaze-slider.unbxd-blaze-container
        .blaze-container
          .blaze-prev
            %button.prev-btn{type: "button"} &#x2039;
          .blaze-next
            %button.next-btn{type: "button"} &#x203A;
          .blaze-track
            - designs.each do |design|
              .blaze-item.blaze-slide
                .product-container
                  - discount = design['discount_percent']
                  - url = design['design_path']
                  = link_to url, {unbxdattr: "product", unbxdparam_sku: "#{design['id']}", unbxdparam_experience_widget: "#{details["widget"]}", unbxdparam_experience_pagetype: "HOME"} do
                    - if design['sizes'] && design['sizes']['long']
                      = image_tag(IMAGE_PROTOCOL + design['sizes']['long'], alt: design['title'], height: "290", width: "auto")
                    - else
                      = image_tag('default_image.jpg', alt: design['title'])
                    .p-detail-box #{design["title"]}
                    .price-flex.d-flex.price-container
                      .discount_price.title_wrap #{get_symbol_from(@hex_symbol)}#{design['discount_price']}
                      - if design['discount_percent'] > 0
                        .actual_price.discount-price #{get_symbol_from(@hex_symbol)}#{design['price']}
                        .design-col2.details_block.percent_discount= "(#{design['discount_percent']}% OFF)"
:javascript
  if (typeof(Turbolinks) != 'undefined') {
    MR.blazeSlider.initializeSwiper();
  }
  document.addEventListener('turbolinks:load', function() {
    MR.blazeSlider.initializeSwiper();
  });