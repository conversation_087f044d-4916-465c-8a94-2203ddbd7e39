-color_classes = ['#FF5252', '#64B5F6', '#81C784', '#FF8A65', '#90A4AE', '#AED581', '#4DD0E1', '#9575CD', '#8D6E63', '#757575']
-@reviews ||= reviews
.review-container#review_of_design
  .heading_review
    Reviews
    -if defined?(review_count)
      (#{review_count})
    -if RATING_ENABLE && @design.present? && @reviews.present? && review_count > 3
      .view_all
        = link_to 'View all', design_review_path(@design['designer_cached_slug'],@design['cached_slug'])
  #add_review_button
    .add-new-review
      -if current_account.present?
        %button#add_review.btn.btn-custom-blue.btn-md{"data-reveal-id" => "write_review", href: "#"}
          -if @review.present?
            + Edit Review
          -else
            + Write a Review
      -else
        %a{href: new_account_session_url(protocol: Rails.application.config.partial_protocol), style:"color:white !important;", data: {turbolinks: 'false'}}
          %button.btn.btn-custom-blue.btn-md + Write a Review
      #write_review.reveal-modal.black-content{data: {reveal:"", v_offset: "0"}, style: 'overflow: scroll; height: 30%; top: 250px; min-height: 25vh;'}
        %h4#modalTitle
          Share your review
        - if defined?(@design) && @design['can_review']
          .ratings-score
            #form-rating-star{data: {star: (@review.try(:rating) || 0)}}
          #rating-alert-message Please give rating
          %textarea#review-text.black{cols: 75, rows: 10,placeholder: 'Write a Review',style:'padding:8px;margin-top:10px;'}= @review.try(:review).to_s
          #review-alert-message Please write a review (atleast 30 characters)
          %button#review-submit-btn.btn.btn-custom-dark-red{"data-dismiss": "modal", type: "button", style: "margin-top:15px !important;", "data-design-id": "#{@design['id']}"}
            Submit
          #save-alert-message{data: {rating_given: @review.try(:rating)}}
        - else
          .review-message
            = "To submit a customer review you need to use a Mirraw account that has successfully been charged for the purchase of this product. If you have another account and you have already used it to make a purchase, you can"
            = link_to "Sign Out", destroy_account_session_path
            and sign into that account to write a review.
        %a#write-review-close.close-reveal-modal{"aria-label" => "Close", onclick: 'closeSizeChart();'} ×

  #product_reviews.tabbed-view.all-products
    -color_classes.shuffle!
    -@reviews.each_with_index do |review, index|
      -if defined? review_count
        -reviewers_name = review['reviewers_name']
        -user_image_url = review['user_image']
      -elsif (user = review.user).present?
        -reviewers_name = "#{user['first_name'].to_s} #{user['last_name'].to_s}"
        -user_image_url = user['image_url']
      .review-row
        .block{id: "block_#{review['id']}"}
          .user-sign{style: "background-color: #{color_classes[index]} !important;"}
            %span.user-name
              -if user_image_url.present?
                =image_tag "#{user_image_url}",class: 'user_image'
              -else
                = reviewers_name.present? ? reviewers_name[0] : 'G'
          .stars.margin-left-top
            .ratings-score.row
              .star.col-sm-12{dscore: review['rating'].to_i}
            .user-name-full.row
              .col-sm-12
                = reviewers_name.present? ? reviewers_name : 'Guest Review'
            .rated-ago.row
              .col-sm-12
                = review['time_ago'] || time_ago_in_words(review['updated_at']) + ' ago'
          .review-text.margin-left-top
            -if review['review'].to_s.length > 150 && !check_for_alternate_tab?
              -show_view_more =true
              = truncate(review['review'].to_s, length: 200)
            -else
              -show_view_more =false
              =review['review'].to_s
          -if show_view_more
            %a{"data-reveal-id" => "view_more_content_#{review['id']}", href: "#"}
              .toggle_scroll.view-more-btn
                view more
            #view_more_content.reveal-modal.black-content{id: "#{review['id']}", "aria-hidden" => "true", "aria-labelledby" => "modalTitle", "data-reveal" => "", role: "dialog"}
              %h4#modalTitle
                = reviewers_name.present? ? reviewers_name : 'Guest Review'
              .rated-ago.row
                .col-sm-12
                  = review['time_ago'] || time_ago_in_words(review['updated_at']) + ' ago'
              #modal_star.stars.margin-left-top
                .ratings-score.row
                  .star.col-sm-12{dscore: review['rating'].to_i}
              .modal-review-text
                %p
                  =review['review']
              %a.close-review-more.close-reveal-modal{"aria-label" => "Close"} ×
    -if @reviews.size > 3 || ! (defined? review_count)
      .review-row
        = paginate @reviews
-if check_for_alternate_tab?
  :javascript
    $('.review-container .tabbed-view .review-row .block .review-text').css('height', 'auto');
-elsif (params[:controller] == 'designs' && params[:action] == 'reviews')
  = javascript_include_tag "reviews"

- unless design_details_page?
  :javascript
    $('.star').raty({readOnly: true, score: function() {return $(this).attr('dscore');},
      starOff: "#{asset_path('star-off.jpg')}",
      starOn: "#{asset_path('star-on.jpg')}",
      starHalf: "#{asset_path('star-half.jpg')}",
      path: ''});
