= render 'layouts/flash_msg'
.row
  .wishlist-block
    .heading_title
      %h1.product-title
        My Wishlist
        %span.product_count (#{@designs.total_count})
    - if @designs.present?
      %ul.small-block-grid-2.medium-block-grid-3.large-block-grid-4.home_page_designs.wishlist-page
        =render partial: 'designs/design_blocks', locals: {designs: @designs, wishlist_page: true}
      .navigate= paginate @designs
- content_for :page_specific_css do
  = stylesheet_link_tag 'catalog'

:css
  @media only screen and (min-width: 1024px){
    .home_page_designs>li .fr_page {
    border: none;
    position: relative;
    width: 80%;
    margin-right: 1.25rem;
    }
    .home_page_designs>li img {
    height: 100% !important;
    max-height: 100%;
    }
  }