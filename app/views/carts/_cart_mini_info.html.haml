.row
  .cart_mini_info.bordered_block.columns{data: {:billing_country => @billing_address.country, :shipping_country => @shipping_address.country}}
    .row
      %b Order Summary :
      %br
      -delivery_time, show_rts_message, available_in_warehouse = @cart.get_delivery_time(@shipping_address.try(:country), @shipping_address.try(:city))
      #estd_days Estimated Delivery : #{delivery_time} days
      -if show_rts_message
        .notice_rts (Your cart contains both Ready To Ship and Non - Ready To Ship products and hence the Estimated delivery date has been changed.)
    - @total_line_item_count = 0
    - @total_addon = 0
    - for line_item in @cart.line_items
      - @total_line_item_count += line_item.quantity
      .row.item_block{id: "item_#{line_item.id}"}
        .large-2.medium-3.small-3.columns
          = link_to designer_design_path(line_item.design.designer, line_item.design) do
            - if line_item.design.master_image.present?
              = image_tag(IMAGE_PROTOCOL + line_item.design.master_image.photo(:large), alt: line_item.design.title)
            - else
              = image_tag('default_image.jpg', alt: line_item.design.title)
          .truncate= line_item.design.designer.name

        .large-10.medium-9.small-9.columns
          .row
            %div.truncate= line_item.design.title.titleize
          - if line_item.variant.present?
            .row
              - option_type_value = line_item.variant.option_type_values.first
              = "#{option_type_value.option_type.p_name} : #{option_type_value.p_name}"
          .row
            - price = get_price_with_symbol(line_item.snapshot_price_currency(@rate), @hex_symbol)
            .left Price :
            .right= price
          - if line_item.line_item_addons.present?
            = render partial: 'carts/line_item_addons', locals: {line_item: line_item}
          .row.quantity_total
            - total = get_price_with_symbol(line_item.total_currency(@rate), @hex_symbol)
            .left= "Quantity : #{line_item.quantity}"
            .right= total
          - if (bmgnx_hash = PromotionPipeLine.bmgnx_hash).present? && line_item.buy_get_free == 1
            .row
              - bmgnx_msg = Promotion.bmgnx_offer_message(bmgnx_hash)
              =link_to bmgnx_msg, '/buy-m-get-n-free', title: "View More - #{bmgnx_msg} Products", class: 'b1g1'
    #totals_block
      .row
        .columns.no-of-items
          .h5.text-right

            = "Number Of Items ( #{@total_line_item_count} )"
      .row
        .item-total.columns
          .h5.text-right.item_total
            - item_total = @cart.items_total_without_addons(rate)
            = "Item Total : #{get_price_with_symbol(item_total, @hex_symbol)}"
            
      - if addons_charges > 0
        .row
          .h5.text-right.addon_charges
            = "Customisation : #{get_price_with_symbol(addons_charges , @hex_symbol)}"
      - if shipping_charges >= 0 || @cart.ready_to_ship_designs?
        .row
          #shipping_charges_order_page.shipping.columns
            .h5.text-right#shipping_charge
              = "Shipping : #{get_price_with_symbol(shipping_charges, @hex_symbol)}"
      - if discounts > 0
        .row
          #discount_order_page.discounts.columns
            .h5.text-right
              = "Discounts : -#{get_price_with_symbol(discounts, @hex_symbol)}"
      - if prepaid_discount > 0
        .row.prepaid_discount.hide
          .discounts.columns
            .h5.text-right
              = "Prepaid Discount(#{@prepaid_payment_promotion.percent}%) : -#{get_price_with_symbol(prepaid_discount, @hex_symbol)}"
      - if mastercard_discount.to_f > 0
        .row.mastercard_discount.hide
          .discounts.columns
            .h5.text-right
              = "Mastercard Discount(#{Promotion.mastercard_discount_percent.to_i}%) : -#{get_price_with_symbol(mastercard_discount, @hex_symbol)}"
      - if cod_charges > 0
        .row.cod_charges.hide
          .discounts.columns
            .h5.text-right
              = "COD Charges : #{get_price_with_symbol(cod_charges, @hex_symbol)}"

      - if session[:gift_wrap] && GIFT_WRAP_PRICE.to_f >= 0
        .row
          .gift_wrap.columns
            .h5.text-right
              = "Gift Wrap Charges : #{get_price_with_symbol(get_price_in_currency(GIFT_WRAP_PRICE.to_f), @hex_symbol)}"
              #foo{"data-lat" => "#{get_price_in_currency(GIFT_WRAP_PRICE.to_f)}"}
      .row
        .taxes.columns
          .h5.text-right.platform_fee
            = "Platform Fee : #{get_price_with_symbol(platform_fee, @hex_symbol)}"     
      - if tax_details.present? && tax_details[:tax_enable] 
        .row
          .taxes.columns
            .h5.text-right.total_tax
              = "#{tax_title} : #{get_price_with_symbol(tax_details[:tax_amount], @hex_symbol)}"
      .row
        .discounts.columns
          #wallet_discount_order_page.h5.text-right
            -if wallet_discount > 0
              ="Wallet discount: #{get_price_with_symbol(wallet_discount , @hex_symbol)}"
      .row
        .total.columns
          #grand_total_without_cod.h5.text-right.grand_total{:value => grandtotal}
            = "Grand Total : #{get_price_with_symbol(grandtotal, @hex_symbol)}"
            -price_in_currency = grandtotal
            -if @country_code == 'US' || @country_code == 'AU' || @country_code == 'GB'
              .paypal_message_pdp{:data => { "pp-message": "", "pp-amount":"#{sprintf('%.2f', price_in_currency)}" ,"pp-layout":"text", "pp-buyercountry":@country_code}}
          .h5.text-right.grand_total_with_cod.hide
            = "Grand Total : #{get_price_with_symbol(cod_total + cod_charges + referral_amount, @hex_symbol)}"
      %br
      .row
        / - if @total_addon != 0 && @cart.mirraw_payable_addons?
        /   .notice_class= "*Due to Festival Rush, estimated time to deliver stitched product is 25 days from date of order."
    - if @subscription_plan.present?
      = render partial: 'orders/subscription_details', locals: {grandtotal: grandtotal, discounts: discounts, shipping_charges: shipping_charges, addons_charges: addons_charges, cod_charges: cod_charges, wallet_discount: wallet_discount, prepaid_discount: prepaid_discount, tax_details: tax_details, cod_total: cod_total, item_total: item_total}
  