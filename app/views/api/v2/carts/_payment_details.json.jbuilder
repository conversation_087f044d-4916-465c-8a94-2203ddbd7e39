json.item_count details[:item_count]
json.hex_symbol @hex_symbol
json.symbol @symbol
json.string_symbol @currency_symbol
json.item_total details[:item_total]
json.item_total_without_addons details[:items_total_without_addons]
json.shipping_total details[:shipping_total]
json.prepaid_shipping_promo details[:prepaid_shipping_promo]
json.addon_charges details[:addon_charges]
json.discount details[:discount]
json.prepaid_discount details[:prepaid_discount]
json.prepaid_percent details[:prepaid_percent]
json.mastercard_discount details[:mastercard_discount]
json.mastercard_discount_percent details[:mastercard_discount_percent]
json.wallet_discounts details[:wallet_discounts]
json.wallet_money details[:wallet_money]
json.payment_options_key details[:payment_options_key]
json.juspay_enable details[:juspay_enable]
json.juspay_payment_options do
  json.partial! '/api/v1/juspay/payment_options', locals: { juspay_payment_opt: details[:juspay_payment_opt] }
end
json.juspay_saved_cards details[:juspay_saved_cards]
json.payment_options details[:payment_options]
json.default_payment details[:default]
json.conditional_offers details[:conditional_offers]
json.tax_rate details[:tax_rate]
json.tax_amount details[:tax_amount]
json.tax_status details[:tax_enable]
json.tax_label Cart.tax_title(@country_code)
json.total details[:total]
json.total_with_prepaid_discount details[:total_with_prepaid_discount]
json.total_with_mastercard_discount details[:total_with_mastercard_discount]
json.min_cart_value_criteria_msg details[:min_cart_value_criteria_failed] ? "International Orders should have a minimum order value of #{@symbol} #{CurrencyConvert.to_currency(@country_code, @min_cart_value).round(2)}. Your current Order Value is #{@symbol} #{details[:item_total]} (shipping charges excluded)." : nil
json.show_mail_us details[:show_mail_us]
json.prepaid_promo_msg details[:prepaid_promo_msg]

if details[:subscription_plan][:id].present?
  json.subscription_details do
    json.id details[:subscription_plan][:id]
    json.plan_name details[:subscription_plan][:name]
    json.plan_description details[:subscription_plan][:description]
    json.amount details[:subscription_plan][:price]
    json.subscription_discount details[:subscription_plan][:discount]
    json.read_more details[:subscription_plan][:read_more_link]
    json.waived_off_platform_fee details[:subscription_plan][:waived_off_platform_fee]
    json.subscription_tax_amount details[:subscription_plan][:tax_amount]
    json.final_payable_amount details[:subscription_plan][:final_payable_amount]
    json.sub_description details[:subscription_plan][:sub_description]
    json.sub_title details[:subscription_plan][:sub_title]
  end
else
  json.subscription_details nil
end
json.subscription_applied details[:subscription_plan][:applied]
json.subscription_active details[:subscription_plan][:subscription_active]
json.platform_fee details[:platform_fee]