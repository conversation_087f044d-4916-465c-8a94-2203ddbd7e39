json.search do
  json.results results.total
  json.solr_debug @debug
  json.total_pages results.results.total_pages
  json.previous_page results.results.prev_page
  json.next_page results.results.next_page
  json.hex_symbol @hex_symbol
  json.symbol @symbol
  json.string_symbol @currency_symbol
  json.promotion_offer_end_time params[:timer] == 'true' ? ((offer = PromotionPipeLine.get_active_promotion(@country_code)).present? && offer.end_date.to_datetime <= 120.hours.from_now ? offer.end_date.to_datetime.strftime('%Q') : nil) : nil
  json.promotion_offer_landing params[:timer] == 'true' ? Promotion.landing_page : nil
  valid_designs = results.results.select(&:valid_design?)
  json.designs do
    json.cache_collection! valid_designs, key: "api/v1/designs/preview_design_#{@country_code}", expires_in: API_CACHE_LIFESPAN.minutes do |design|
      json.partial! 'api/v1/designs/preview', locals: { design: design, all_images: false }
    end
  end
  json.partial! 'api/v1/store/filter', locals: { results: results }
  json.sorts do
    json.key 'sort'
    if @country_code == 'IN'
      json.list Api::V1::StoreHelper.domestic_sorts.except('10', '11', '12', '13') do |id, params|
        json.name params[:name]
        json.value id
      end
    else
      json.list Api::V1::StoreHelper.international_sorts.except('10', '11', '12', '13') do |id, params|
        json.name params[:name]
        json.value id
      end
    end
  end
  if MOBILE_SOURCE
    json.uuid SecureRandom.uuid
  end
end
