category = design.categories.first
json.id design.id
json.title design.title
json.average_rating design.average_rating
json.total_review design.total_review
json.mirraw_recommended design.mirraw_recommended?
json.mirraw_certified design.mirraw_certified
json.premium design.premium
json.category_name category.try(:name).try(:singularize)
json.category_id category.try(:id)
json.breadcrumb_path design.categories.first.try(:breadcrumb_path).present? ? design.categories.first.breadcrumb_path : []
json.design_path designer_design_path(design.designer, design)
json.hex_symbol @hex_symbol
json.symbol @symbol
json.string_symbol @currency_symbol
json.price design.price_currency(@rate)
json.inr_price Design.inr_value(design.price, @country_code, @rate)
json.discount_price design.effective_price_currency(@rate)
json.inr_discount_price Design.inr_value(design.effective_price, @country_code, @rate)
json.discount_percent design.effective_discount(PromotionPipeLine.active_promotions)
json.designer design.designer.name
json.updated_at design.updated_at
json.created_at design.created_at.strftime('%Y-%m-%d')
json.brand design.designer.cached_slug
json.color design.color
json.image_url design.master_img.try(:photo, :zoom)
json.image_thumb_url design.master_img.try(:photo, :thumb)
json.cached_slug design.cached_slug
json.designer_rating design.designer.average_rating
json.state design.available? ? design.state : 'sold_out'
json.stock design.quantity
json.boosted design.currently_boosted?(@category.id) if @category.present?
json.type design.categories.first.try(:title)
json.likes_count design.likes_count.to_i
json.product_offer design.product_offer(@country_code)
json.ready_to_ship (design.rts_available(@actual_country))
json.custom_tags design.custom_tags(@country_code)
json.promotion_offer_end_time design.applicable_promotions_end_date(PromotionPipeLine.get_active_promotion(Design.country_code), @country_code)
if (d_end_date = design.designer.additional_discount_end_date).present? && design.designer.additional_discount_start_date <= Date.today && d_end_date.end_of_day < (15.days.from_now)
  json.designer_discount_end_time (d_end_date.end_of_day.getlocal.utc).strftime("%Y/%m/%d %H:%M:%S")
end
if @country_code == 'IN'
  json.grade design.grade
else
  json.grade design.international_grade
end
if all_images
   json.partial! '/api/v1/designs/images', locals: { design: design }
else
   json.partial! '/api/v1/designs/master_image', locals: { design: design }
end
json.rating design.average_rating
json.wishlist_id nil
json.is_variant_present design.variants.present?
json.categories design.categories do |category|
  json.id category.id
  json.name category.name
  json.weight category.weight
end
