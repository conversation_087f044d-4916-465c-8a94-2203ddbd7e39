json.cache! ["api/v1/#{@symbol}", @similar_designs], expires_in: API_CACHE_LIFESPAN.minutes do
  ActiveRecord::Associations::Preloader.new.preload(@similar_designs, [:master_image,:designer])
  json.designs @similar_designs do |design|
    if design.designer && design.master_image.present?
      json.id design.id
      json.design_path designer_design_path(design.designer, design)
      json.cache! ['api/v1/master_image', design], expires_in: API_CACHE_LIFESPAN.minutes do
        json.partial! '/api/v1/designs/master_image', locals: { design: design }
      end
    end
  end
end