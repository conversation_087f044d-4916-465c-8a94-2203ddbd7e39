json.cart do
  json.partial! 'cart', locals: { cart: @cart, messages: @messages}
  json.wallet_discount @cart.wallet_discounts(@country_code, 1)
  json.wallet_usage_msg @cart.get_wallet_usable_msg
  # json.shipping_total @cart.shipping_cost_currency(@shipping_country, @rate).to_f + essential_shipping.to_f
  #for shipping calculation
  if @shipping_country == 'India' && @country_code.to_s == 'IN'
    essential_shipping_charges, essential_total, all_essential = Order.get_essential_shipping(@cart.line_items)
    if all_essential
      json.shipping_total essential_shipping_charges.to_f
    else
      final_price = @cart.item_total_with_discount(1, 'India' )
      json.shipping_total @cart.shipping_cost_currency('India', 1, (final_price - essential_total) ).to_f + essential_shipping_charges.to_f
    end
  else
    json.shipping_total @cart.shipping_cost_currency(@shipping_country, @rate).to_f
  end
  json.addon_charges @cart.addons_total(@rate)
  json.total @cart.total_currency(@rate, @country_code)
  grand_total = @cart.total_currency(@rate, @country_code, @shipping_country, false, false)
  tax_rate , tax_amount, grand_total, tax_enable = @cart.get_total_with_tax(@country_code, grand_total)
  grand_total -= @cart.wallet_discounts(@country_code, 1)
  json.tax_status tax_enable
  json.tax_amount tax_amount.to_f
  json.tax_rate tax_rate.to_f
  json.tax_label Cart.tax_title(@country_code)
  json.grand_total grand_total.to_f
  json.add_more_items_value @cart.add_more_items_value(@country_code, @rate)
  json.shipping_message @cart.get_shipping_message(@shipping_country, @shipping_city)
  if CART_COUPON_CONSTANT['Cart_coupon_Enable'] && @api_account && @cart.cart_coupon_applicable?(@api_account) && cart_coupon = Coupon.find_by_name('user_cart_coupon')
    json.cart_coupon_offer_msg Cart.get_cart_coupon_offer_msg(cart_coupon)
    json.cart_coupon_timer_msg 'This offer is available only for '.to_s
    json.cart_coupon_promotion_time Cart.get_cart_coupon_countdown(@api_account)
  end
end
json.cart_wallet do
  json.partial! 'api/v1/wallets/wallet_details', locals: { wallet: @wallet, currency: @wallet.currency_convert} if @wallet.present? && @wallet.currency_convert.country_code == @country_code
end