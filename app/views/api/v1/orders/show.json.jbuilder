currency_details = currency_params(@order)

confirmed_order = USER_CONFIRMED_ORDER_STATE.include?(@order.state)
# Need to update round_to as per customer currency
CurrencyConvert.round_to = currency_details[:round_to]

json.id @order.number
json.created_at @order.created_at.as_json
json.state @order.customer_state
json.courier_company @order.courier_company
json.tracking_number @order.tracking_number
json.tracking_link Shipment.get_shipper_tracking_link(@order)
json.hex_symbol currency_details[:hex_symbol]
json.symbol @symbol
json.show_cancel_order_button @order.cancellable?
json.cancel_order_reasons Order.order_cancel_reasons
json.string_symbol @currency_symbol
json.return_button @order.payment_state == 'completed'
json.return_policy RETURN_POLICY
json.reasons Return.get_return_reasons
json.refund_type RETURN_REFUND_TYPE
json.require_bank_info ['Cash On Delivery','Bank Deposit','Cash Before Delivery'].include?(@order.pay_type)
json.designer_orders @order.designer_orders do |designer_order|
  if designer_order.line_items.present?
    json.state designer_order.state.try(:humanize)
    json.courier_company @order_status.present? ? nil : designer_order.tracking_partner
    json.tracking_number @order_status.present? ? nil : designer_order.tracking_num
    json.tracking_link @order_status.present? ? nil : Shipment.get_shipper_tracking_link(designer_order)
    json.designer_id designer_order.designer_id
    json.designer_order_status @order_status.present? ? nil : designer_order.order_status
    json.line_items designer_order.line_items do |line_item|
      if confirmed_order
        form_link = ''
        line_item.get_forms.each do |key, value|
          if value.present?
            form_link = stitching_form_url(order_number: @order.number, design_id: line_item.design_id, product_designable_type: ((key.to_sym == :blouse) ? 'lehenga_choli' : (['kurti','islamic'].include?(line_item.design.designable_type.try(:downcase)) ? 'kurti' : 'anarkali' )))
          end
        end
        json.stitching_form form_link
      end
      json.line_item_id line_item.id
      json.design_id line_item.design_id
      json.returnable confirmed_order ? (@returnable_line_items_ids.include?(line_item.id)) : false
      json.non_returnable_reason @item_status[line_item.id]
      json.title line_item.design.title
      json.cache! ['api/v1/master_image', line_item.design] do
        json.partial! '/api/v1/designs/master_image', locals: { design: line_item.design }
      end
      json.designer_name line_item.design.designer.name
      json.quantity line_item.quantity
      json.hex_symbol currency_details[:hex_symbol]
      json.currency_symbol currency_details[:currency_symbol]
      json.symbol @symbol
      json.string_symbol @currency_symbol
      json.price line_item.snapshot_price_currency(currency_details[:rate])
      json.market_rate currency_details[:market_rate]
      json.line_item_addons line_item.line_item_addons do |line_item_addon|
        json.name line_item_addon.addon_type_value.name
        json.currency_symbol currency_details[:currency_symbol]
        json.hex_symbol currency_details[:hex_symbol]
        json.symbol @symbol
        json.string_symbol @currency_symbol
        json.snapshot_price line_item_addon.snapshot_price_currency(currency_details[:rate])
        json.market_rate currency_details[:market_rate]
      end
      json.notes line_item.note
      json.partial! '/api/v1/carts/variants', locals: { line_item: line_item}
      json.total line_item.total_currency(currency_details[:rate])
      json.market_rate currency_details[:market_rate]
      json.status line_item.status
      json.mirraw_certified line_item.design.mirraw_certified
    end
  end
end
json.order_status @order_status
json.currency_symbol currency_details[:currency_symbol]
json.market_rate currency_details[:market_rate]
json.item_total @order.item_total(currency_details[:rate])
json.item_total_without_addons @order.item_total_with_subscription_fee(currency_details[:rate])
json.addon_charges @order.addons_total(currency_details[:rate])
json.discounts @order.total_discount_currency(currency_details[:rate])
json.referral_discount @order.referral_discount
json.refund_discount @order.refund_discount
json.shipping @order.shipping_currency(currency_details[:rate])
json.cod @order.cod_charge_currency(currency_details[:rate])
json.platform_fee @order.platform_fee_currency(currency_details[:rate])
json.subscription_fee @order.subscription_fee_currency(currency_details[:rate])
if @order.subscription_fee > 0
  subscription_plan = @order.get_subscription_plan
  json.sub_description subscription_plan[:membership_description]
  json.sub_title subscription_plan[:sub_title]
  json.subscription_discount @order.subscription_fee_currency(currency_details[:rate])
  json.subscription_discount_title "Discount applied due to Membership"
  if @order.user.active_subscription.present?
    json.membership_message @order.user.active_subscription.membership_description
  end
end
json.total @order.total_currency(currency_details[:rate])
json.total_tax @order.total_tax_currency(currency_details[:rate])
json.tax_label Cart.tax_title(@country_code)
json.cashgram_config AUTOMATED_COD_REFUND
