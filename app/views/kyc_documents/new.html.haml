= render partial: 'layouts/flash_msg'

.user-document-card
  .user-document-header
    %i.icon-shield
    %h2 Upload #{@document_type}
    %p.secure-text Secure verification process

  .user-document-body
    %h3 Please Upload Your #{@document_type}
    %p.info-text To proceed, kindly upload a scanned copy or a clear photo of your #{@document_type}.

    = form_for @kyc_document, method: :post, multipart: true, html: { novalidate: true } do |f|
      = hidden_field_tag :order_number, params[:order_number]
      = f.hidden_field :name, value: @document_type
      = hidden_field_tag :redirect_back_url, params[:redirect_back_url]

      .upload-wrapper
        %label.upload-box{ for: "file_input" }
          %i.icon-upload
          %p.upload-title Upload File
          %span.upload-sub Supports JPG, PNG, PDF (Max 5MB)

        = f.file_field :document, 
          id: "file_input", 
          accept: '.pdf, .jpg, .jpeg, .png', 
          label: false, 
          required: true, 
          style: "display:none;"

      %p#file-error

      %button.submit-btn{ type: "submit" } Upload Document

  .user-document-footer
    %i.icon-lock
    .footer-box
      %strong Secure & Confidential
      %p.footer-text Your documents are encrypted and processed securely. We never store your personal information.

:javascript
  $(document).ready(function() {
    MR.order.validateDocument();
  })

= stylesheet_link_tag 'kyc_document'
= javascript_include_tag 'order'
