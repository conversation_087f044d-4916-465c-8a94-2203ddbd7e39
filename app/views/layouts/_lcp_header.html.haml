%link{rel: "preconnect", href: "https://assets0.mirraw.com"}
-# Added Preconnect to External Domains and fetch priority high for LCP Image PDP 
- if params[:controller] == 'designs' && params[:action] == 'show'
  - if @design.present? && @design["images"].present? 
    - designImage = @design["images"][0]
    - original =  designImage['sizes']['original'].empty? ? asset_path('default_image.jpg') : designImage['sizes']['original']
    - original_webp =  designImage['sizes']['original'].empty? ? asset_path('default_image.jpg') :  webp_format_image(designImage['sizes']['zoom'])
    - if original_webp.size > 1
      %link{rel:"preload", href: IMAGE_PROTOCOL + original_webp,  as:"image", fetchpriority:'high'}
    - else
      %link{rel:"preload", href: IMAGE_PROTOCOL + original,  as:"image", fetchpriority:'high'}
  %link{:href => "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css", :rel => "stylesheet"}

%link{rel: "preconnect", href: "https://assetsm0.mirraw.com"}
