%body
  - if storefront_page?
    .deal_timer_header
      .custom_deal_timer
        = render 'layouts/campaign_timer'
  %header.desktop-header-component-container
    .header-with-searchbar-wrapper
      .empty-container
      .logo-box
        %a{href: '/', 'aria-label' => "Home"}
          = image_tag('logo-red.png', alt: '', class: 'logo-img')
      .search-and-profile-container
        .search-box
          .searchbar-icon-wrapper.search_margin
            = render partial:  'layouts/search_form'   
        .actions-box
          .dropdown
            .profile-action-wrap.m-pointer.dropbtn.dropdown.logo-wrap
              / prettier-ignore
              .logo-icon
                - if !account_signed_in?
                  %a{href: '/accounts/sign_in'}
                    %svg{fill: "none", height: "20", viewBox: "0 0 20 20", width: "20", xmlns: "http://www.w3.org/2000/svg"}
                      %path{"clip-rule" => "evenodd", d: "M19.0304 16.79C19.0304 16.4129 18.9311 16.0429 18.7426 15.7164C18.7418 15.715 18.7411 15.7136 18.7397 15.7121C14.8047 9.02357 4.68541 9.03072 0.7547 15.715C0.753985 15.7164 0.752555 15.7186 0.752555 15.7186C0.565412 16.0421 0.466127 16.4086 0.464698 16.7814C0.458984 17.0593 0.458984 17.4579 0.458984 17.8571C0.458984 18.4257 0.6847 18.9707 1.08684 19.3721C1.48827 19.7743 2.03327 20 2.60184 20C5.83041 20 13.659 20 16.8876 20C17.4561 20 18.0011 19.7743 18.4026 19.3721C18.8047 18.9707 19.0304 18.4257 19.0304 17.8571V16.79ZM17.6018 16.7893V17.8571C17.6018 18.0464 17.5268 18.2286 17.3926 18.3621C17.259 18.4964 17.0768 18.5714 16.8876 18.5714H2.60184C2.41256 18.5714 2.23041 18.4964 2.09684 18.3621C1.96256 18.2286 1.88756 18.0464 1.88756 17.8571C1.88756 17.4657 1.88756 17.075 1.89327 16.8043C1.89327 16.7993 1.89327 16.795 1.89327 16.79C1.89327 16.6657 1.92541 16.5436 1.98756 16.4364C5.38041 10.6714 14.1097 10.665 17.5068 16.4329C17.569 16.5414 17.6018 16.6643 17.6018 16.7893ZM9.7447 0C6.98541 0 4.7447 2.24071 4.7447 5C4.7447 7.75929 6.98541 10 9.7447 10C12.504 10 14.7447 7.75929 14.7447 5C14.7447 2.24071 12.504 0 9.7447 0ZM9.7447 1.42857C11.7161 1.42857 13.3161 3.02857 13.3161 5C13.3161 6.97143 11.7161 8.57143 9.7447 8.57143C7.77327 8.57143 6.17327 6.97143 6.17327 5C6.17327 3.02857 7.77327 1.42857 9.7447 1.42857Z", fill: "#3C4345", "fill-rule" => "evenodd"}
                    .logo-action-text Profile
                - else
                  %svg{fill: "none", height: "20", viewBox: "0 0 20 20", width: "20", xmlns: "http://www.w3.org/2000/svg"}
                    %path{"clip-rule" => "evenodd", d: "M19.0304 16.79C19.0304 16.4129 18.9311 16.0429 18.7426 15.7164C18.7418 15.715 18.7411 15.7136 18.7397 15.7121C14.8047 9.02357 4.68541 9.03072 0.7547 15.715C0.753985 15.7164 0.752555 15.7186 0.752555 15.7186C0.565412 16.0421 0.466127 16.4086 0.464698 16.7814C0.458984 17.0593 0.458984 17.4579 0.458984 17.8571C0.458984 18.4257 0.6847 18.9707 1.08684 19.3721C1.48827 19.7743 2.03327 20 2.60184 20C5.83041 20 13.659 20 16.8876 20C17.4561 20 18.0011 19.7743 18.4026 19.3721C18.8047 18.9707 19.0304 18.4257 19.0304 17.8571V16.79ZM17.6018 16.7893V17.8571C17.6018 18.0464 17.5268 18.2286 17.3926 18.3621C17.259 18.4964 17.0768 18.5714 16.8876 18.5714H2.60184C2.41256 18.5714 2.23041 18.4964 2.09684 18.3621C1.96256 18.2286 1.88756 18.0464 1.88756 17.8571C1.88756 17.4657 1.88756 17.075 1.89327 16.8043C1.89327 16.7993 1.89327 16.795 1.89327 16.79C1.89327 16.6657 1.92541 16.5436 1.98756 16.4364C5.38041 10.6714 14.1097 10.665 17.5068 16.4329C17.569 16.5414 17.6018 16.6643 17.6018 16.7893ZM9.7447 0C6.98541 0 4.7447 2.24071 4.7447 5C4.7447 7.75929 6.98541 10 9.7447 10C12.504 10 14.7447 7.75929 14.7447 5C14.7447 2.24071 12.504 0 9.7447 0ZM9.7447 1.42857C11.7161 1.42857 13.3161 3.02857 13.3161 5C13.3161 6.97143 11.7161 8.57143 9.7447 8.57143C7.77327 8.57143 6.17327 6.97143 6.17327 5C6.17327 3.02857 7.77327 1.42857 9.7447 1.42857Z", fill: "#3C4345", "fill-rule" => "evenodd"}
                  .logo-action-text Profile
            .dropdown-content
              - if account_signed_in?
                = link_to "My Orders", orders_path, class: 'all-menu', data: {turbolinks: 'false'}
                -if !is_domestic?
                  = link_to 'My Measurements', stitching_measurements_path,class: 'all-menu', data: {turbolinks: 'false'}
                = link_to "My Wallet", user_wallet_path, class: 'all-menu', data: {turbolinks: 'false'}
                = link_to "My Wishlist", user_wishlists_path, class: 'all-menu'
                = link_to "My Addresses", user_addresses_path, class: 'all-menu'
                = link_to "Change Password", edit_account_registration_url, class: 'all-menu', data: {turbolinks: 'false'}
                = link_to "Sign Out", destroy_account_session_path, method: :delete, class: 'all-menu', data: {turbolinks: 'false'}
                
          = link_to user_wishlists_path do
            .logo-wrap
              .action-btn.wishlist-action-wrap.m-pointer.logo-icon
                .inactive-icon
                  / prettier-ignore
                  %svg{fill: "none", height: "20", viewbox: "0 0 21 20", width: "21", xmlns: "http://www.w3.org/2000/svg"}
                    %path{d: "M0.0302734 6.83994C0.0302734 11.1269 3.62544 15.3434 9.3052 18.9662C9.51668 19.097 9.81879 19.2379 10.0303 19.2379C10.2418 19.2379 10.5439 19.097 10.7654 18.9662C16.4351 15.3434 20.0303 11.1269 20.0303 6.83994C20.0303 3.27754 17.5831 0.761719 14.3203 0.761719C12.4573 0.761719 10.9467 1.64729 10.0303 3.00583C9.134 1.65735 7.60328 0.761719 5.74024 0.761719C2.4774 0.761719 0.0302734 3.27754 0.0302734 6.83994ZM1.65162 6.83994C1.65162 4.16311 3.38375 2.38191 5.7201 2.38191C7.61336 2.38191 8.70097 3.55931 9.34548 4.56564C9.61738 4.96817 9.78858 5.07887 10.0303 5.07887C10.272 5.07887 10.423 4.95811 10.7151 4.56564C11.4099 3.57944 12.4573 2.38191 14.3404 2.38191C16.6768 2.38191 18.4089 4.16311 18.4089 6.83994C18.4089 10.5835 14.4512 14.6189 10.2418 17.4165C10.141 17.4869 10.0706 17.5372 10.0303 17.5372C9.98999 17.5372 9.9195 17.4869 9.82886 17.4165C5.60933 14.6189 1.65162 10.5835 1.65162 6.83994Z", fill: "#3C4345"}
              .logo-action-text Wishlist
          = link_to cart_path, data: {turbolinks: 'false'} do
            .logo-wrap
              .action-btn.cart-action-wrap.m-pointer.logo-icon
                .inactive-icon
                  %svg{fill: "none", height: "24", viewbox: "0 0 21 24", width: "21", xmlns: "http://www.w3.org/2000/svg"}
                    %path{"clip-rule" => "evenodd", d: "M6.54537 5.92728C6.6835 5.09931 6.87611 4.4136 7.13073 3.86056C7.42761 3.21576 7.79469 2.78395 8.23255 2.50728C8.67133 2.23002 9.24254 2.06982 10.0124 2.07254C10.7811 2.07526 11.353 2.23795 11.793 2.51706C12.2319 2.79546 12.5995 3.22757 12.8967 3.87078C13.1511 4.42129 13.344 5.10341 13.4825 5.92728H6.54537ZM4.86036 7.42728C4.79313 8.3037 4.765 9.25639 4.76501 10.2806C4.76501 10.6948 5.1008 11.0306 5.51501 11.0306C5.92923 11.0306 6.26501 10.6948 6.26501 10.2806C6.265 9.21831 6.29691 8.26919 6.36541 7.42728H13.6639C13.7329 8.26851 13.7651 9.21748 13.7651 10.2805C13.7651 10.6948 14.1009 11.0305 14.5151 11.0305C14.9294 11.0305 15.2651 10.6948 15.2651 10.2805C15.2651 9.25567 15.2367 8.3031 15.1691 7.42728H16.7495C17.38 7.42728 17.9117 7.89671 17.9899 8.52224L19.4899 20.5222C19.5832 21.2683 19.0014 21.9273 18.2495 21.9273H2.78068C2.02881 21.9273 1.44707 21.2683 1.54033 20.5222L3.04033 8.52224C3.11852 7.8967 3.65027 7.42728 4.28068 7.42728H4.86036ZM5.02683 5.92728C5.18435 4.88513 5.42286 3.98334 5.76821 3.23324C6.15783 2.38699 6.6965 1.70351 7.43129 1.23922C8.16514 0.775515 9.03345 0.56907 10.0177 0.572553C10.9989 0.576025 11.8646 0.786182 12.5964 1.25036C13.3294 1.71525 13.8681 2.39686 14.2584 3.2416C14.604 3.98957 14.8432 4.88826 15.0014 5.92728H16.7495C18.1364 5.92728 19.3063 6.96001 19.4783 8.33619L20.9783 20.3362C21.1835 21.9775 19.9037 23.4273 18.2495 23.4273H2.78068C1.12656 23.4273 -0.153254 21.9775 0.0519144 20.3362L1.55191 8.33619C1.72394 6.96001 2.89379 5.92728 4.28068 5.92728H5.02683Z", fill: "#3C4345", "fill-rule" => "evenodd"}
                  %span.secondary.round.tiny.cart_count{data: {turbolinks_permanent: ''}}
                    = "#{session[:cart_count]}"
              .logo-action-text Bag
    .header-mega-menu-wrapper
      %nav.container-layout-max
        %ul.nav-link-wrap
          - cache("static_desktop_menu_#{@country_code}", :expires_in => 24.hours) do
            - if @menu_list.present?
              -@menu_list.each do |menu|
                - menu_columns = menu.menu_columns
                - menu_columns = menu_columns.sort_by(&:position)
                %li.nav-link-list
                  %a.action-link{:href => menu.link}
                    =menu.title
                    - if MENU_SUPER_TAG.keys.include?(menu.title)
                      %sup.menu-tag= MENU_SUPER_TAG[menu.title]
                  .mega-menu-wrapper
                    .main-flex-container.container-layout-max
                      -menu_list = []
                      - menu_columns.each do |menu_column|
                        - if menu_column.menu_items.any?
                          %ul.category-box
                            %li
                              - if menu_column.link.present?
                                = link_to menu_column.title, menu_column.link, class: 'parent-category-name'
                              - else
                                .parent-category-name= menu_column.title
                            - menu_column.menu_items.each do |menu_item|
                              %li
                                %a.child-category-name{:href => menu_item.link}= menu_item.title
    -if return_details_page?
      .tab-bar.row{style:'z-index:-1'}
        = render partial: 'layouts/order_return_tab'
