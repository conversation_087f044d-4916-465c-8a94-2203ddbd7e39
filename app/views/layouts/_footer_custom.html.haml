#mobile_footer
  #share
    / facebook
    %a.facebook{:href => "https://www.facebook.com/MirrawDesigns/", :target => "blank", 'aria-label' => "Follow Mirraw on Facebook"}
      %svg.bi.bi-facebook{fill: "currentColor", height: "16",style: "color: white", viewbox: "0 0 16 16", width: "16", xmlns: "http://www.w3.org/2000/svg"}
        %path{d: "M16 8.049c0-4.446-3.582-8.05-8-8.05C3.58 0-.002 3.603-.002 8.05c0 4.017 2.926 7.347 6.75 7.951v-5.625h-2.03V8.05H6.75V6.275c0-2.017 1.195-3.131 3.022-3.131.876 0 1.791.157 1.791.157v1.98h-1.009c-.993 0-1.303.621-1.303 1.258v1.51h2.218l-.354 2.326H9.25V16c3.824-.604 6.75-3.934 6.75-7.951z", fill: "white"}
    / instagram
    %a.instagram{:href => "https://www.instagram.com/mirraw/?hl=en", :target => "blank", 'aria-label' => "Follow Mirraw on Instagram"}
      %svg.bi.bi-instagram{fill: "currentColor", height: "16", style: "color: white", viewbox: "0 0 16 16", width: "16", xmlns: "http://www.w3.org/2000/svg"}
        %path{d: "M8 0C5.829 0 5.556.01 4.703.048 3.85.088 3.269.222 2.76.42a3.917 3.917 0 0 0-1.417.923A3.927 3.927 0 0 0 .42 2.76C.222 3.268.087 3.85.048 4.7.01 5.555 0 5.827 0 8.001c0 2.172.01 2.444.048 3.297.04.852.174 1.433.372 1.942.205.526.478.972.923 1.417.444.445.89.719 1.416.923.51.198 1.09.333 1.942.372C5.555 15.99 5.827 16 8 16s2.444-.01 3.298-.048c.851-.04 1.434-.174 1.943-.372a3.916 3.916 0 0 0 1.416-.923c.445-.445.718-.891.923-1.417.197-.509.332-1.09.372-1.942C15.99 10.445 16 10.173 16 8s-.01-2.445-.048-3.299c-.04-.851-.175-1.433-.372-1.941a3.926 3.926 0 0 0-.923-1.417A3.911 3.911 0 0 0 13.24.42c-.51-.198-1.092-.333-1.943-.372C10.443.01 10.172 0 7.998 0h.003zm-.717 1.442h.718c2.136 0 2.389.007 3.232.046.78.035 1.204.166 1.486.275.373.145.64.319.92.599.28.28.453.546.598.92.11.281.24.705.275 1.485.039.843.047 1.096.047 3.231s-.008 2.389-.047 3.232c-.035.78-.166 1.203-.275 1.485a2.47 2.47 0 0 1-.599.919c-.28.28-.546.453-.92.598-.28.11-.704.24-1.485.276-.843.038-1.096.047-3.232.047s-2.39-.009-3.233-.047c-.78-.036-1.203-.166-1.485-.276a2.478 2.478 0 0 1-.92-.598 2.48 2.48 0 0 1-.6-.92c-.109-.281-.24-.705-.275-1.485-.038-.843-.046-1.096-.046-3.233 0-2.136.008-2.388.046-3.231.036-.78.166-1.204.276-1.486.145-.373.319-.64.599-.92.28-.28.546-.453.92-.598.282-.11.705-.24 1.485-.276.738-.034 1.024-.044 2.515-.045v.002zm4.988 1.328a.96.96 0 1 0 0 1.92.96.96 0 0 0 0-1.92zm-4.27 1.122a4.109 4.109 0 1 0 0 8.217 4.109 4.109 0 0 0 0-8.217zm0 1.441a2.667 2.667 0 1 1 0 5.334 2.667 2.667 0 0 1 0-5.334z", fill: "white"}
    / youtube
    %a.youtube{:href => "https://www.youtube.com/channel/UCFz6WjSUuICOJqIYl3DWA-g", :target => "blank", 'aria-label' => "Subscribe to Mirraw on YouTube"}
      %svg.bi.bi-youtube{fill: "currentColor", height: "16", style: "color: white", viewbox: "0 0 16 16", width: "16", xmlns: "http://www.w3.org/2000/svg"}
        %path{d: "M8.051 1.999h.089c.822.003 4.987.033 6.11.335a2.01 2.01 0 0 1 1.415 1.42c.101.38.172.883.22 1.402l.01.104.022.26.008.104c.065.914.073 1.77.074 1.957v.075c-.001.194-.01 1.108-.082 2.06l-.008.105-.009.104c-.05.572-.124 1.14-.235 1.558a2.007 2.007 0 0 1-1.415 1.42c-1.16.312-5.569.334-6.18.335h-.142c-.309 0-1.587-.006-2.927-.052l-.17-.006-.087-.004-.171-.007-.171-.007c-1.11-.049-2.167-.128-2.654-.26a2.007 2.007 0 0 1-1.415-1.419c-.111-.417-.185-.986-.235-1.558L.09 9.82l-.008-.104A31.4 31.4 0 0 1 0 7.68v-.123c.002-.215.01-.958.064-1.778l.007-.103.003-.052.008-.104.022-.26.01-.104c.048-.519.119-1.023.22-1.402a2.007 2.007 0 0 1 1.415-1.42c.487-.13 1.544-.21 2.654-.26l.17-.007.172-.006.086-.003.171-.007A99.788 99.788 0 0 1 7.858 2h.193zM6.4 5.209v4.818l4.157-2.408L6.4 5.209z", fill: "white"}

  - if !checkout_flow_page? && !checkout_cart_login? && !login_page?
    .row
      .col-xs-4.sign_in_button{:style => "margin-left: 34%; padding: 3px"}
        - if account_signed_in?
          = link_to "Sign Out", destroy_account_session_path, method: :delete, data: {turbolinks: 'false'}, class: "ga-sign-out-tracking"
        - else
          = link_to "Sign In", new_account_session_url(protocol: Rails.application.config.partial_protocol), data: {turbolinks: 'false'}, class: "ga-sign-in-tracking"
    %br
  .row
    .col-xs-4.nopadding
      %span.f_free
      .clr
      %span Free Shipping Within India
    .col-xs-4.nopadding
      %span.f_world_wide
      .clr
      %span Ships Worldwide
    .col-xs-4.nopadding
      %span.f_money_back
      .clr
      %span 100% Money Back Guarantee
    .clr  
  .row
    %ul#footer_nav
      %li
        %nav
          =link_to "Help Center", pages_faq_path, {:style =>"color: white !important;", data: {turbolinks: 'false'}}
          %span= "|"
          =link_to "Terms", pages_terms_path, {:style =>"color: white !important;", data: {turbolinks: 'false'}}
          %span= "|"
          =link_to "Privacy", pages_privacy_path, {:style =>"color: white !important;", data: {turbolinks: 'false'}}
          %span= "|"
          =link_to "About Us", pages_about_path, {:style =>"color: white !important;", data: {turbolinks: 'false'}}
          %span= "|"
          =link_to "Careers", "http://careers.mirraw.com/about-us", {:style =>"color: white !important;", data: {turbolinks: 'false'}}
      %li
        %b Email :
        <EMAIL>
      %li.phoneNumber
        - if ['inr', 'rs'].include?(@symbol.to_s.downcase)
          %a{href: pages_faq_url, data: {turbolinks: 'false'}} Contact Us
        -elsif @symbol.to_s.downcase == 'cad'
          %b Phone No. :
          %a{href: "tel:+15817054535"}
            +15-817-054-535
          (Mon-Sat)
        -elsif @country_code.to_s.downcase == 'gb'
          %b Phone No. :
          %a{href: "tel:+441214614192"}
            +441-214-614-192
          (Mon-Sat)
        -else
          %b Phone No. :
          %a{href: "tel:+19494645941"}
            ******-464-5941
          (Mon-Sat)
    .clr
  .col-lg-12.copyright
    %p Copyright © #{Time.now.year}, Mirraw Online Services Pvt. Ltd. All Rights Reserved.
  -if !(uc_browser? || opera_mini_browser?) && cookies[:subscribe].nil? && @subscription_banner_hash.present?
    -button_color = @subscription_banner_hash['color'] || '#FF8030'
    -bg_color = @subscription_banner_hash['bg_color'] || '#FFDEC9'
    #mobile-subscribe-window.reveal-modal{data: {reveal:""}, "close_on_background_click"=>"true" , style: 'padding: 0px; height: 100%;'}
      #modal-subscribe-box
        .modal-body
          .newsletter-image{style: 'max-height:100%;max-width:100%;'}
            - if @subscription_banner_hash['image_url'].is_a?(String)
              = image_tag(BASE_64_PLACHOLDER_IMAGE, alt: 'Newsletter Subscription', id:'newsletter-sub-image', class: 'js-lazy', data: {original: @subscription_banner_hash['image_url']})
            - else
              = webp_picture_tag(@subscription_banner_hash['image_url'], p_style: :mobile_main, id:'newsletter-sub-image', alt: 'Newsletter Subscription', lazy: {})
        .modal-footer{style: "background:#{bg_color}"}
          .row
            .columns.small-12.text-center
              -if opera_mini_browser?
                %input#subscribe-input{:type => "text", :placeholder => "Enter Email", :value => "", :style => "width:250px;color:#{button_color}"}
              -else
                %input#subscribe-input{:type => "text", :placeholder => "Enter Email", :value => "", style: "color:#{button_color}"}
          .row
            .columns.small-12.text-center
              %input#email-subscribe-button{:type => "submit", :value => @subscription_banner_hash['text'], style: "background:#{button_color};color:#{bg_color}"}
          .row
            .columns.small-12.text-center
              %label.sub-msg{style: "background:#{bg_color};color:#{button_color}"}
          .row
            .columns.small-12.text-center
              %input#email-cancel-button{:type => "submit", :value => "No Thanks!"}

  - if cookies[:sticky_coupon_banner].nil? && @sticky_coupon_banner.present?
    .sticky-coupon-banner
      .wrapper
        .sticky-coupon-image
          = link_to @sticky_coupon_banner.link do
            = webp_picture_tag(@sticky_coupon_banner.photo, p_style: :main_m, alt: 'sticky coupon banner', lazy: {})
        %a.close-sticky-coupon-banner.right.close-button
          .close-label x
