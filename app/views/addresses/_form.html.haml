- content_for :page_specific_css do
  = stylesheet_link_tag 'addresses'
- content_for :page_specific_js do
  = javascript_include_tag 'addresses'

.address_row
  .billing_address.large-12.medium-8.small-12.columns.small-centered
    = form_for [@user, @address], validate: true do |f|
      %section.address
        .container
          .address-heading
            %h1
              %i.fa-solid.fa-arrow-left
              Secure Checkout
          .custom_address_page    
            .address-details
              .customrow
                .customcol
                  .address-form.address-box
                    .contact-details.address-box-heading
                      %h2 Contact Details
                      .form-wrap
                        #contact-details
                          .row
                            .col-md-12.custom-fields
                              .form-group
                                %label#name-label{for: "name"} Full Name
                                = f.text_field :name, type: 'text', required: true, id: 'name', class: 'form-control billing_name', label: false
                            - if !is_domestic?
                              .col-lg-6.col-md-12.custom-fields
                                .form-group
                                  %label#email-label{for: "email"} Email ID
                                  - cookies[:mobile_exp] = {value: 'f', expires: (Time.current + 2.days)}
                                  = f.text_field :email, value: @address.user.email, type: 'email', required: true, id: 'email', label: false, class: 'form-control billing_email', pattern: '^((?:[-a-zA-Z0-9_\.]+)+[a-zA-Z0-9]*)@((?:[-a-zA-Z0-9]+\.)+[a-zA-Z]{2,})$'
                            - if is_domestic?
                              .col-lg-6.col-md-12.custom-fields
                                .form-group
                                  %label#email-label{for: "email"} Email ID (optional)
                                  - cookies[:mobile_exp] = {value: 't', expires: (Time.current + 2.days)}
                                  = f.text_field :email, value: @address.user.email, type: 'email', id: 'email', class: 'form-control billing_email', label: false, pattern: '^((?:[-a-zA-Z0-9_\.]+)+[a-zA-Z0-9]*)@((?:[-a-zA-Z0-9]+\.)+[a-zA-Z]{2,})$'
                            .col-lg-6.col-md-12.custom-fields
                              .form-group
                                %label#number-label{for: "number"} Phone Number
                                .phone-input
                                  .dial_code_block{style: 'display: none'}
                                  = text_field_tag 'address[dial_code]', '', style: 'text-align: center;', id: 'dial_code_text', autocomplete: 'none', label: 'dial_code', class: 'form-control billing_dial_code'
                                  #address_phone_conatainer
                                    = f.text_field :phone, required: true, type: 'tel', pattern: '^[0-9]{10}$', id: 'address_phone', class: 'form-control billing_phone', label: false, oninvalid: "this.setCustomValidity('Please enter valid mobile number')", onchange: "try{setCustomValidity('')}catch(e){}"
                    = hidden_field_tag 'country_code', @actual_country
                    .billing-details.address-box-heading
                      %h2 Billing Details
                      .form-wrap
                        #contact-details
                          .row
                            .col-lg-6.col-md-12.custom-fields
                              .form-group
                                %label Country / Region
                                = f.select :country, @countries, class: 'form-select form-control billing_country', label: false
                            .col-lg-6.col-md-12.custom-fields
                              .form-group
                                %label#pincode-label{for: "pincode"} Pincode / Zip / Postal Code
                                - if @actual_country == 'India'
                                  = f.text_field :pincode, required: true, type: 'tel', maxlength: '6', pattern: '^[1-9]\d{5}$', class: 'form-control billing_code', label: false, oninvalid: "this.setCustomValidity('Please enter 6 digit pincode')", onchange: "try{setCustomValidity('')}catch(e){}"
                                - else
                                  = f.text_field :pincode, required: true, type: 'text', maxlength: '15', class: 'form-control billing_code', label: false
                                #pincode_format_notice.pincode_format
                            .col-lg-6.col-md-12.custom-fields
                              .form-group
                                %label#city-label{for: "city"} City
                                = f.text_field :city, required: true, minlength: 3, class: 'form-control billing_city', label: false
                            .col-lg-6.col-md-12.custom-fields
                              .form-group
                                %label State / Province
                                = f.text_field :state, required: true, class: 'form-control billing_state', label: false
                            - if is_domestic?
                              .col-md-12.custom-fields
                                .form-group
                                  %label#address1-label{for: "address1"} House No, Colony, Street
                                  = f.text_field :street_address_line_1, required: true, type: 'text', rows: 3, label: 'Address', minlength: 3, class: 'form-control billing_address_1', label: false
                              .col-md-12.custom-fields
                                .form-group
                                  %label#address2-label{for: "address2"} Locality
                                  = f.text_field :street_address_line_2, required: true, type: 'text', rows: 3, label: 'Address', minlength: 3, class: 'form-control billing_address_2', label: false
                              .col-md-12.custom-fields
                                .form-group
                                  %label#address3-label{for: "address3"} Landmark (optional)
                                  = f.text_field :street_address_line_3, type: 'text', rows: 3, label: 'Address', minlength: 3, class: 'form-control billing_address_3', label: false
                            - elsif is_uae?
                              .col-md-12.custom-fields
                                .form-group
                                  %label#address2-label{for: "address1"} Flat No / House No / Floor / Building Name
                                  = f.text_field :street_address_line_1, required: true, type: 'text', rows: 3, label: 'Flat No / House No / Floor / Building Name', minlength: 3, class: 'form-control billing_address_1', label: false
                              .col-md-12.custom-fields
                                .form-group
                                  %label#address3-label{for: "address2"} Colony / Street / Locality / Landmark
                                  = f.text_field :street_address_line_2, required: true, type: 'text', rows: 3, label: 'Colony / Street / Locality / Landmark', minlength: 3, class: 'form-control billing_address_2', label: false
                            - else
                              .col-md-12.custom-fields
                                .form-group
                                  %label#address1-label{for: "address1"} Street Address
                                  = f.text_field :street_address, required: true, type: 'text', rows: 3, label: 'Address', minlength: 3, class: 'form-control billing_address_1', label: false
            
                  .shipping_details_box
                    - if params[:address_type].blank? && @address.new_record? && @user.default_address.blank?
                      .address-details#shipping-address
                        .customrow
                          .customcol
                            .address-form.address-box
                              .contact-details.address-box-heading
                                %h2 Contact Details
                                .form-wrap
                                  #contact-details
                                    .row
                                
                                      .col-md-12.custom-fields
                                        .form-group
                                          %label#name-label{for: "name"} Full Name
                                          = f.fields_for :shipping_address do |shipping_f|
                                            = shipping_f.text_field :name, type: 'text', required: false, id: 'name', class: 'form-control shipping_name', label: false
                                      - if !is_domestic?
                                        .col-lg-6.col-md-12.custom-fields
                                          .form-group
                                            %label#email-label{for: "email"} Email ID
                                            - cookies[:mobile_exp] = {value: 'f', expires: (Time.current + 2.days)}
                                            = f.fields_for :shipping_address do |shipping_f|
                                              = shipping_f.text_field :email, value: @address.user.email, type: 'email', required: false, id: 'email', label: false, class: 'form-control shipping_email', pattern: '^((?:[-a-zA-Z0-9_\.]+)+[a-zA-Z0-9]*)@((?:[-a-zA-Z0-9]+\.)+[a-zA-Z]{2,})$'
                                      - if is_domestic?
                                        .col-lg-6.col-md-12.custom-fields
                                          .form-group
                                            %label#email-label{for: "email"} Email ID (optional)
                                            - cookies[:mobile_exp] = {value: 't', expires: (Time.current + 2.days)}
                                            = f.fields_for :shipping_address do |shipping_f|
                                              = shipping_f.text_field :email, value: @address.user.email, type: 'email', id: 'email', class: 'form-control shipping_email_optional', label: false, pattern: '^((?:[-a-zA-Z0-9_\.]+)+[a-zA-Z0-9]*)@((?:[-a-zA-Z0-9]+\.)+[a-zA-Z]{2,})$'
                                      .col-lg-6.col-md-12.custom-fields
                                        .form-group
                                          %label#number-label{for: "number"} Phone Number
                                          .phone-input
                                            .dial_code_block_shipping{style: 'display: none'}
                                            = text_field_tag 'address[dial_code]', '', style: 'text-align: center;', id: 'dial_code_text_shipping', autocomplete: 'none', label: 'dial_code', class: 'form-control shipping_dial_code'
                                            #address_phone_conatainer
                                              = f.fields_for :shipping_address do |shipping_f|
                                                = shipping_f.text_field :phone, type: 'tel', pattern: '^[0-9]{10}$', required: false, id: 'shipping_address_phone', class: 'form-control shipping_name', label: false, oninvalid: "this.setCustomValidity('Please enter valid mobile number')", onchange: "try{setCustomValidity('')}catch(e){}"
                              = hidden_field_tag 'country_code', @actual_country
                              .billing-details.address-box-heading
                                %h2 Shipping Details
                                .form-wrap
                                  #shipping-contact-details
                                    .row
                                      .col-lg-6.col-md-12.custom-fields
                                        .form-group
                                          %label Country / Region
                                          = f.fields_for :shipping_address do |shipping_f|
                                            = shipping_f.select :country, @countries, class: 'form-select form-control shipping_country', label: false
                                      .col-lg-6.col-md-12.custom-fields
                                        .form-group
                                          %label#pincode-label{for: "pincode"} Pincode / Zip / Postal Code
                                          - if @actual_country == 'India'
                                            = f.fields_for :shipping_address do |shipping_f|
                                              = shipping_f.text_field :pincode, required: false, type: 'tel', maxlength: '6', pattern: '^[1-9]\d{5}$', class: 'form-control shipping_code', label: false, oninvalid: "this.setCustomValidity('Please enter 6 digit pincode')", onchange: "try{setCustomValidity('')}catch(e){}"
                                          - else
                                            = f.fields_for :shipping_address do |shipping_f|
                                              = shipping_f.text_field :pincode, required: false, type: 'text', maxlength: '15', class: 'form-control shipping_code', label: false
                                          #shipping_pincode_format_notice.shipping_pincode_format
                                      .col-lg-6.col-md-12.custom-fields
                                        .form-group
                                          %label#city-label{for: "city"} City
                                          = f.fields_for :shipping_address do |shipping_f|
                                            = shipping_f.text_field :city, required: false, minlength: 3, class: 'form-control shipping_city', label: false
                                      .col-lg-6.col-md-12.custom-fields
                                        .form-group
                                          %label State / Province
                                          = f.fields_for :shipping_address do |shipping_f|
                                            = shipping_f.text_field :state, required: false, class: 'form-control shipping_state', label: false
                                      - if is_domestic?
                                        .col-md-12.custom-fields
                                          .form-group
                                            %label#address1-label{for: "address1"} House No, Colony, Street
                                            = f.fields_for :shipping_address do |shipping_f|
                                              = shipping_f.text_field :street_address_line_1, required: false, type: 'text', rows: 3, label: 'Address', minlength: 3, class: 'form-control shipping_address_1', label: false
                                        .col-md-12.custom-fields
                                          .form-group
                                            %label#address2-label{for: "address2"} Locality
                                            = f.fields_for :shipping_address do |shipping_f|
                                              = shipping_f.text_field :street_address_line_2, required: false, type: 'text', rows: 3, label: 'Address', minlength: 3, class: 'form-control shipping_address_2', label: false
                                        .col-md-12.custom-fields
                                          .form-group
                                            %label#address3-label{for: "address3"} Landmark (optional)
                                            = f.fields_for :shipping_address do |shipping_f|
                                              = shipping_f.text_field :street_address_line_3, type: 'text', rows: 3, label: 'Address', minlength: 3, class: 'form-control shipping_address_3', label: false
                                      - elsif is_uae?
                                        .col-md-12.custom-fields
                                          .form-group
                                            %label#address2-label{for: "address1"} Flat No / House No / Floor / Building Name
                                            = f.fields_for :shipping_address do |shipping_f|
                                              = shipping_f.text_field :street_address_line_1, required: false, type: 'text', rows: 3, label: 'Flat No / House No / Floor / Building Name', minlength: 3, class: 'form-control shipping_address_1', label: false
                                        .col-md-12.custom-fields
                                          .form-group
                                            %label#address3-label{for: "address2"} Colony / Street / Locality / Landmark
                                            = f.fields_for :shipping_address do |shipping_f|
                                              = shipping_f.text_field :street_address_line_2, required: false, type: 'text', rows: 3, label: 'Colony / Street / Locality / Landmark', minlength: 3, class: 'form-control shipping_address_2', label: false
                                      - else
                                        .col-md-12.custom-fields
                                          .form-group
                                            %label#address1-label{for: "address1"} Street Address
                                            = f.fields_for :shipping_address do |shipping_f|
                                              = shipping_f.text_field :street_address, required: false, type: 'text', rows: 3, label: 'Address', minlength: 3, class: 'form-control shipping_address_1', label: false
                      .custom_billing_address                          
                      - if params[:address_type].blank? && @address.new_record? && @user.default_address.blank?
                        .row
                          .columns
                            %label.shipping_confirm_checkbox
                              = check_box_tag :shipping_address, 1, checked: true, id: 'ship_to_same_address_checkbox'
                              = hidden_field_tag :ship_to_same_address, 1
                              %span= 'Ship to same address'
                    = hidden_field_tag :address_id, @address.id
    
                .customcoll
                  .order-summary
                    .order-review.address-box
                      - if @cart.present?
                        = render partial: 'addresses/order_review'
                    .payment-button
                      = f.submit 'SAVE AND CONTINUE', id: :address_collect_submit, class: 'button tiny expand success order_flow_submit_button sticky-button'

