= render partial: 'addresses/form'
-if @cart.present?
  - pro_totals, max_total, shipping_currency = get_cart_total_information(@country_code, @rate)
  - coupon_discount_amount = pro_totals.find { |item| item[:title] == "Coupon Discounts" }&.dig(:amount) || 0
  - tax_amount = pro_totals.find { |item| item[:title] == "Tax" }&.dig(:amount) || 0
  - total_amount = pro_totals.find { |item| item[:title] == "Item Total" }&.dig(:amount) || 0
  - gift_wrap = session[:gift_wrap] && @country_code != 'IN' ? get_price_in_currency(GIFT_WRAP_PRICE.to_f) : 0
  - market_rate = CurrencyConvert.countries_marketrate[@country_code]
  - gift_wrap = (gift_wrap *= (market_rate).round(CurrencyConvert.round_to)).round(CurrencyConvert.round_to)
  - tax_amount = (tax_amount *= (market_rate).round(CurrencyConvert.round_to)).round(CurrencyConvert.round_to)
  - shipping_currency = (shipping_currency *= (market_rate).round(CurrencyConvert.round_to)).round(CurrencyConvert.round_to)
  - coupon_discount_amount = (coupon_discount_amount *= (market_rate).round(CurrencyConvert.round_to)).round(CurrencyConvert.round_to)
  -if coupon_discount_amount != 0
    -discount_percent = (coupon_discount_amount*100.0/@totalvalue)
  -else
    -discount_percent = 0
  
  :javascript
    window.dataLayer = window.dataLayer || [];
    $(document).on('click', '#address_collect_submit', function(e)  {
      if (isFormFilled()) {
        var total =  (#{@totalvalue} + #{gift_wrap}) - #{coupon_discount_amount}
        var ga4_shipping_params = #{@ga_hash_new.to_json};
        var customizationTotal = 0;

        ga4_shipping_params.items.forEach(function (item) {
          customizationTotal += item.item_customization * item.quantity;
        });
        ga4_shipping_params.customization = customizationTotal
        ga4_shipping_params.value = total + customizationTotal
        ga4_shipping_params.gift_wrap = #{gift_wrap}
        ga4_shipping_params.shipping = #{shipping_currency}
        ga4_shipping_params.coupon_discount = #{coupon_discount_amount}
        ga4_shipping_params.tax = #{tax_amount}
        dataLayer.push({ ecommerce: null });
        dataLayer.push({
          event: "ga4_add_shipping_info",
          ecommerce: ga4_shipping_params
        });
      }
    });
    function isFormFilled() {
      var form = $('#new_address');
      var requiredFields = form.find('input[required], textarea[required]');
      for (var i = 0; i < requiredFields.length; i++) {
        if (!requiredFields[i].value.trim()) {
          return false;
        }
      }
      return true;
    }