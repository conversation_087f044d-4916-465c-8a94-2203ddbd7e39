class AppSourceService
    def initialize(headers)
      @headers = headers
    end
  
    def determine_and_store_app_source(session)
      app_source = if @headers['HTTP_CLOUDFRONT_IS_MOBILE_VIEWER'] == 'true' || @headers['HTTP_CLOUDFRONT_IS_TABLET_VIEWER'] == 'true'
                     'Mobile'
                   else
                     'Desktop'
                   end
  
      session[:app_source] = app_source
    end
  end