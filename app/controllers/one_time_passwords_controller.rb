require 'one_time_password_service'

class OneTimePasswordsController < ApplicationController
  # NOTE: Most code within this class does not belong here.
  # This was done because of need of the hour.
  # A separate class should be implemented to switch the
  # strategies being used for finding the phone number to
  # send the OTP to.
  # 
  # For example:
  # 
  #   phone_number = OTPPhoneFinder.find(params)
  # 
  # The +OTPPhoneFinder+ class will implement methods
  # to find the phone number strategy based on the keys
  # present in +params+ and return strategy class which
  # will encapsulate all the functionality for that scenario
  # like the +message+ to send and the +follow_url+ to
  # redirect to.

  def deliver
    OneTimePasswordService.deliver(phone, "Dear #{name},\nYour One Time Password for the cancellation of order #{number} is :OTP:. Please enter the OTP to successfully cancel your order.")

    head :ok
  end

  def verify
    if OneTimePassword.verify(phone, params[:code])
      Order.sidekiq_delay.cancel_order_mobile(order.id)
      order.save_order_cancel_reason(params[:order_cancel_reason], current_account)
      flash[:success] = "Your order #{number}, if not showing as cancelled right now, will be cancelled soon."
      render js: "window.location = '#{order_path(order)}'"
    else
      head :bad_request
    end
  end

  private

  def phone
    order.billing_phone
  end

  def name
    order.billing_name
  end

  def number
    order.number
  end

  def order
    @order ||= Order.find_by(number: params[:order_number])
  end
end