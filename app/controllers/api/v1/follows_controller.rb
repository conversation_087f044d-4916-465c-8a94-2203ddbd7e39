class Api::V1::Follows<PERSON><PERSON>roller < ApiController

  before_action :authenticate_api_account!, :authenticate_user!

  def follow_designer
    designer = Designer.find_by_id(params[:designer_id])
    current_user.follow(designer) if designer.present?
    response_for designer
  end

  def unfollow_designer
    designer = Designer.find_by_id(params[:designer_id])
    if current_user.following?(designer)
      current_user.stop_following(designer)
      response_for designer
    else
      head :no_content
    end
  end

  def follow_user
    user = User.find_by_id(params[:user_id])
    current_user.follow(user) if user.present?
    response_for user
  end

  def unfollow_user
    user = User.find_by_id(params[:user_id])
    if current_user.following?(user)
      current_user.stop_following(user)
      response_for user
    else
      head :no_content
    end
  end

  def auto_follow
    Follow.sidekiq_delay.auto_follow_friends(current_user.id, params[:oauth_ids].split(','))
    response_for current_user
  end

end