class Api::V1::AddressesController < ApiController

  before_action :authenticate_api_account!, :authenticate_user!, except: [:pincode_info]
  skip_before_filter :validate_headers?, :currency, only: [:pincode_info]
  skip_after_action :update_auth_header, only: [:pincode_info]

  # Provides the addresses of given user_id
  #
  # GET /user/addresses
  #
  # == Returns
  # JSON
  #
  def index
    @addresses = current_user.addresses.order('updated_at DESC')
    head :no_content if @addresses.blank? && request.headers['App-Source'] != 'iOS'
  end

  # Provides the addresses of given user_id
  #
  # GET /user/addresses/get_address
  #
  # == Returns
  # JSON
  #

  def get_address
    @addresses = current_user.addresses.recently_updated
    @addresses.each do |address|
      dial_code = Country.find_by_namei(address.country).try(:dial_code)
      phone_num = address.phone.gsub('+', "")
      phone_num.slice!(dial_code) if phone_num.start_with?(dial_code)
      address.phone = phone_num
    end
    head :no_content if @addresses.blank?
  end

  # Creates address for given current_user.
  #
  # POST /user/addresses
  #
  # == Returns
  # Nil
  #
  def create
    if (address = current_user.addresses.new(address_params))
      address.save
      begin
        current_user.account.add_phone_to_guest_account(address_params)
      rescue Exception => e
        Order.sidekiq_delay.notify_exceptions("Guest checkout storing phone number", e.inspect, { params: address_params })
      end
    end
    response_for address
  end

  # Updates the address with given params
  #
  # [PATCH/PUT] /user/addresses/:id
  #
  # == Returns
  # JSON
  #
  def update
    if (address = current_user.addresses.find_by(id: params[:id]))
      address.update_attributes(address_params)
    end
    response_for address
  end

  # Deletes address for user
  #
  # DELETE /user/addresses/:id
  #
  def destroy
    if address = current_user.addresses.find_by_id(params[:id])
      address.destroy
    end
    response_for address
  end

  # Provides City, State and Country for given Pincode
  #
  # == Returns:
  #  JSON

  def pincode_info
    pincode = Pincode.find_by_pin_code(params[:pincode])
    response_for pincode, [:city_name, :district, :state]
  end

  private

  # Permitting only allowed params
  #
  def address_params
    params.require(:address).permit(:name, :street_address, :landmark, :city,
                                    :state, :country, :pincode, :phone, :default,
                                    *Address.street_address_lines)
  end

end
