class Api::V1::VideoListingsController < ApiController
  before_action  :authenticate_api_account!, :authenticate_user!, except: [:videos]

  def videos
    @account = Account.find_by_id params[:account_id]
    if @account
      @videos = @account.video_listings.order(created_at: :desc)
    else
      render json: {errors: "Couldn't find content"}, status: :unprocessable_entity
    end
  end

  def upload_video
    video_listing = VideoListing.upload(listing_params, current_api_account)
    if video_listing.errors.blank?
      head :ok
    else
      render json: video_listing.errors, status: :unprocessable_entity
    end
  end

  def destroy
    @video_listing = VideoListing.find_by_id params[:id].to_i
    if @video_listing and @video_listing.destroy
      head :ok
    else
      render json: {errors: 'Something went wrong'}, status: :unprocessable_entity
    end
  end
  private

  def listing_params
    params.permit(:name, :url, :tag)
  end
end