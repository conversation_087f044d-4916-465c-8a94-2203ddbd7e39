class Api::V1::LineItemController < ApiController

  before_action :authenticate_api_account!, :authenticate_user!, only: [:move_to_wishlist]
  before_action :set_response_header, only: [:create, :destroy, :move_to_wishlist]

  # Creates / Updates LineItem
  #
  def create
    render(json: { errors: { Warning: ['We do have cart limit of 200 items per Order.'] } }, status: 422) and return if current_cart.line_items.count >= 50
    app_version = request.headers['App-Version']
    design_ids = params[:line_items].present? ? params[:line_items].map{|d| d['design_id']} : params[:line_item][:design_id]
    designs = Design.where(id: design_ids).preload(:variants,:categories)
    error_for_line_item, out_of_stocked_design = nil, nil
    if designs.present?
      if params[:line_items]
        designs = designs.group_by(&:id)
        multi_line_item_params['line_items'].each do |item_param|
          design = designs[item_param['design_id'].to_i].try(:first)
          error_for_line_item, out_of_stocked_design = create_the_line_item(design, item_param)
          break if error_for_line_item.any?
        end
      else
        error_for_line_item, out_of_stocked_design = create_the_line_item(designs.first, line_item_params)
      end
    else
      head :no_content and return
    end
    current_cart.cart_coupon_apply(request.headers['Device-ID'], request.headers['App-Version'], @app_source, current_api_account.try(:id), request.headers['Fcm-Token']) if CART_COUPON_CONSTANT['Cart_coupon_Enable'] && @country_code != 'IN'
    if error_for_line_item.any? && error_for_line_item[:quantity].present?
      render json: { errors: { Quantity: ["#{out_of_stocked_design} is not available, Rest are added."] } }, status: 422 and return
    elsif error_for_line_item.any?
      render json: { errors: { Problem: ["for adding the product to the cart, please try again later."] } }, status: 422 and return
    else
      render json: {message: "Successfully added to cart"}, status: 200 and return
    end
  end

  def create_the_line_item(design, item_param)
    line_item = current_cart.line_items.where(item_param.slice(:design_id, :variant_id)).first_or_initialize
    line_item.source = item_param.slice(:source)[:source]
    line_item.clear_add_addons(item_param.slice(:line_item_addons_attributes)) if STITCHING_ENABLE == 'true'
    line_item.add_variant(line_item.variant) if line_item.variant.present?
    line_item.app_source = @app_source
    line_item.buy_get_free = line_item.design.buy_get_free if PromotionPipeLine.bmgnx_hash.present?
    line_item.designable_type = design.designable_type
    line_item.category_id = design.categories.try(:first).try(:id)
    if (var_hash = Promotion.get_free_shipping(nil, true, country_code: @country_code)).last == true && line_item.snapshot_price > var_hash.first
      line_item.item_details['promotion'] = 'Free Shipping'
    end
    line_item.save
    update_previous_line_item(item_param.slice(:design_id, :variant_id))
    line_item.cart.send_notification(request.headers['App-Version'], @app_source, current_api_account.try(:id))
    return line_item.errors, design.title
  end

  # Updates LineItem
  #
  def update
    if line_item = current_cart.line_items.find_by_id(params[:id])
      line_item.update(line_item_update_params)
    end
    response_for line_item
  end

  # Deletes LineItem
  #
  def destroy
    if line_item = current_cart.line_items.find_by_id(params[:id])
      if line_item.designer_order_id.present?
        line_item.update_column(:cart_id,nil)
      else
        line_item.destroy
      end
    end
    response_for line_item
  end

  def move_to_wishlist
    if (line_item = current_cart.line_items.find_by_id(params[:id])).present? && (user = current_user).present?
      @wishlist = Wishlist.where(user_id: user.id, design_id: line_item.design_id, state: 'added').first_or_initialize
      if @wishlist
        @wishlist.update_attributes(conversion_rate: @rate, country_code: @country_code,
          price: line_item.snapshot_price, wish: true, app_source: @app_source)
        # line_item.design.increment_count(:wishlists_count)
        destroy()
      end
    end
    head :no_content if @wishlist.blank?
  end

  def update_through_notification
    if @line_item = current_cart.line_items.find_by_id(params[:id])
      design = @line_item.design
      @line_item.update_column(:snapshot_price, design.effective_price)
      @line_item.update_column(:scaling_factor, design.get_scale)
      @line_item.update_column(:vendor_selling_price, (@line_item.variant || design).get_vendor_selling_amount)
    else
      head :no_content
    end
  end

  def returnable
    render json: LineItem.where(id: params[:ids]).inject({}) { |hash, line_item|
      hash.merge!(line_item.id => false) # TODO : line_item.returnable?)
    }
  end

  private

  # Rails Strong parameters implementation
  # Blocking unwanted values
  #
  def line_item_params
    params.require(:line_item).permit(:design_id, :variant_id, :quantity, :source, line_item_addons_attributes: [:addon_type_value_id, :notes, addon_option_type_id: []])
  end

  def line_item_update_params
    params.require(:line_item).permit(:design_id, :variant_id, :quantity, :source, line_item_addons_attributes: [:addon_type_value_id, :notes, addon_option_type_id: []])
  end

  # Rails Strong parameters implementation
  # Blocking unwanted values
  #
  def line_item_addons_params
    params.require(:line_item).permit(line_item_addons_attributes: [
      :addon_type_value_id, :notes, addon_option_type_id: []])
  end

  def unique_line_item_params
    line_item_params.slice(:design_id, :variant_id)
  end

  def update_previous_line_item(current_line_item= nil)
    li = current_cart.line_items.where(current_line_item || unique_line_item_params).last
    li.update_snapshot_price if li.present?
  end

  def update_line_item_source
    line_item = current_cart.line_items.find_by(unique_line_item_params)
    line_item.update_column(:source, line_item_source) if line_item.present?
  end

  def line_item_source
    line_item_source = line_item_params.slice(:source)[:source]
  end

  def multi_line_item_params
    params.permit(line_items: [:design_id, :variant_id, :source, :quantity, line_item_addons_attributes: [:addon_type_value_id, :notes, addon_option_type_id: []]])
  end
end
