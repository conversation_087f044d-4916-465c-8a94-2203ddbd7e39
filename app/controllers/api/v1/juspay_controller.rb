class Api::V1::JuspayController < ApiController
  include <PERSON><PERSON><PERSON><PERSON>

  before_filter :authenticate_account!, except: [:create_order, :payment_options, :callback]

  def create_order
    return_url = api_v1_orders_juspay_response_url
    order = Order.find_by_number(params[:number])
    response = order.create_juspay_order(return_url)
    if response[:error].nil?
      render json: response
    else
      render json: response, status: response[:error_code]
    end
  end

  def payment_methods
    response = JuspayApi.get_payment_methods
    render json: response
  end

  def payment_options
    cod_enable = true
    if (GOKWIK_CONFIG['enable_gk_on_api'] || web_request?) && GOKWIK_CONFIG['enable_gokwik']
      res = get_response_data_for('gokwiks_predict_rto_risk', params.merge({request_method: "POST", session: {
        utm_medium: session[:utm_medium], utm_source: session[:utm_source], utm_campaign: session[:utm_campaign],
        remote_ip: request.remote_ip, user_agent: request.user_agent
      }}))
      cod_enable = (res["data"].present? ? (GOKWIK_CONFIG["risk_flags"].include?(res["data"]["risk_flag"].downcase) ? false : true) : true) if !GOKWIK_CONFIG['enable_cod']
    end
    @payment_options = PaymentOption.fetch_juspay_options(params[:app_source], cod_enable) 
  end

  def callback
    response = params[:juspay]
    jp_order_status = response[:content][:order]
    order_id = jp_order_status[:order_id]
    
    if (order = Order.find_by_number(order_id)).present?
      order.skip_filter = true
      if (transaction_order = order.transaction_order).present?
        transaction_order.status = jp_order_status[:status]
        transaction_order.save
      end
      case response[:event_name]
      when 'TXN_CREATED'
        if (txn_detail = jp_order_status[:txn_detail]).present?
          order.payment_gateway = txn_detail[:gateway]
          order.save
        end
      when 'ORDER_SUCCEEDED', 'ORDER_FAILED'
        Order.sidekiq_delay(queue: 'critical').process_juspay_order!(jp_order_status.to_hash) if order.juspay_order_status.blank?
        payment_gateway_response = jp_order_status[:payment_gateway_response]
        case order.payment_gateway
        when 'PAYTM'
          order.update_attributes(paytm_txn_id: payment_gateway_response[:epg_txn_id], paytm_txn_status: payment_gateway_response[:resp_code])
        when 'PAYPAL'
          order.update_attributes(paypal_txn_id: payment_gateway_response[:epg_txn_id])
        when 'PAYU'
          order.update_attributes(payu_mihpayid: payment_gateway_response[:epg_txn_id], payu_payment_category_mode: jp_order_status[:payment_method_type], payu_status: payment_gateway_response[:resp_code])
        when 'RAZORPAY'
          order.update_attributes(razorpay_id: payment_gateway_response[:epg_txn_id], razorpay_status: payment_gateway_response[:resp_code])
        end if payment_gateway_response.present?
      end 
    end 
    render nothing: true
  end

end