class Api::V1::DesignersController < ApiController

  before_action :expired_coupons_required?, only: :coupons

  def show
    @curr_user = current_user
    @designer = Designer.find_by_id(params[:id])
    head :no_content if @designer.nil?
  end

  def index
    @curr_user = current_user
    @designers = Designer.get_designers(params[:key], params[:value]).page(params[:page]).per(params[:per] || 20)
    head :no_content if @designers.blank?
  end

  def all_followers
    @curr_user = current_user
    @followers = Designer.find_by_id(params[:id]).followers_by_type('User').page(params[:page]).per(params[:per] || 20)
    head :no_content if @followers.blank?
  end

  def coupons
    coupon_ids = params[:coupon_ids].split(',')
    @coupons = Coupon.where(id: coupon_ids).includes(:designer)
    @coupons = @coupons.page(params[:page]).per(params[:per] || 20)
  end

  def offers
    if Designer.find_by_id(params[:id]).present?
      @coupons = Coupon.includes(:designer).live.advertise.for_designer(params[:id]).order('end_date asc')
      @offers = Designer.active_additional_discount.for_designer(params[:id]).order('additional_discount_percent DESC')
    end
    head :no_content if @coupons.blank? && @offers.blank?
  end

  def banner_and_tags
    if (sub_app = request.headers['Sub-App']).present? && Designer.find_by_id(params[:id]).present?
      @banners = BannerSlider.graded.mirraw.live.with_link_value.where(app_source: sub_app).country(@country_code)
      @tags = TagSlider.graded.with_link_value.where(app_source: sub_app)
    end
    head :no_content if @banners.blank? && @tags.blank?
  end

  private

  def expired_coupons_required?
    @all_coupons = ALLOWED_APP_VERSIONS[20..-1].include?(request.headers['App-Version'])
  end

end