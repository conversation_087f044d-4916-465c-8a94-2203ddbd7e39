class Api::V1::ReturnsController < ApiController

  before_action :authenticate_api_account!, :authenticate_user!
  before_action :set_result, only: [:index, :completed, :pending]

  require 'one_time_password_service'

  ASSOCIATIONS = [order: [designer_orders: [line_items: [:line_item_addons, :return_designer_order]]]]

  # For initiating return
  #
  # POST api/v1/returns
  #
  # Parameters::
  #     return[notes]:
  #     return[reason]:
  #     return[return_designer_orders_attributes][idOrIndex][notes]:
  #     return[return_designer_orders_attributes][idOrIndex][designer_id]:
  #     return[return_designer_orders_attributes][idOrIndex][line_item_ids]][]:
  #
  def create
    rdo_params = return_designer_order_params
    @return, duplicate = Return.duplicate?(rdo_params['rdo_attributes'].map {|rdo| rdo['line_items_attributes'].map {|line_item| line_item['id']}}), true
    unless @return.present?
      @return, duplicate = Return.new, false
      @return.app_source = "#{@app_source}-#{request.headers['App-Version']}"
      @return.build_by(return_params)
      @return.build_by_api(rdo_params)
    end
    if rdo_params['verified_phone_number'].present? && rdo_params['otp'].present?
      if OneTimePassword.verify('91'+rdo_params['verified_phone_number'], rdo_params['otp'])
        @return.refund_phone_number = rdo_params['verified_phone_number']
      else
        @return.errors.add(:refund_phone_number, 'OTP not verified')
      end
    end
    if @return.errors.blank? && (duplicate || @return.save!)
      pickup_service_available = (request.headers['App-Version'] >= REVERSE_PICKUP_MINIMUM_VERSION[request.headers['App-Source']] ) ? @return.pickup_available_on_order? : false
      @success_msg =  @return.return_success_msg(pickup_service_available)
      render json: {id: @return.id, success_msg: @success_msg, pickup_service_available: pickup_service_available}
    elsif @return.errors.messages[:refund_phone_number].present?
      render json: {errors: 'Incorrect OTP'}, status: 422 and return
    else
      render json: {errors: 'Something went wrong'}
    end
    rescue
      render json: {errors: 'Something went wrong, please try again later or click Help to contact Mirraw Support.'}, status: 422 and return
  end

  # For initiating return
  #
  # POST api/v1/returns/id
  #
  # Parameters::
  #     return[notes]:
  #     return[reason]:
  #     return[return_designer_orders_attributes][idOrIndex][notes]:
  #     return[return_designer_orders_attributes][idOrIndex][designer_id]:
  #     return[return_designer_orders_attributes][idOrIndex][line_item_ids]][]:
  #
  def update
    if @return = Return.find_by_id(params[:id])
      @return.update_attributes(return_params)
    end
    @return.update_transition_state(return_params) if params[:return][:return_designer_orders_attributes].present?
    response_for @return
    rescue
      render json: {errors: 'Something went wrong, please try again later or click Help to contact Mirraw Support.'}, status: 422 and return
  end

  # Provides the order list of current_user
  #
  # GET /api/v1/user/returns/completed          For completed returns
  # GET /api/v1/user/returns/pending            For pending returns
  # GET /api/v1/user/returns                    For Returns Listing
  #
  # == Returns
  # JSON
  #
  def index; end

  def completed
    render :index
  end

  def pending
    render :index
  end

  def show
    @rtn = current_user.returns.includes(:order, return_designer_orders: [:designer_order, line_items: [line_item_addons: :addon_type_value, design: [:designer,:master_image]]]).find_by_id(params[:id])
    head :no_content and return if @rtn.blank?
  end

  def generate_otp
    response = OneTimePasswordService.deliver('91' + params[:phone], "[PHONE_NUMBER], OTP:[OTP_NUMBER] is your one time password to proceed on Mirraw. It is valid for 10 minutes. Do not share your OTP with anyone.", 4)
    sms_sent = response.include?('MsgID') ? true : false
    render json: {sms_sent: sms_sent}
  end

  private

  def return_designer_order_params
    params.require(:return_designer_order).permit(
      :return_type,
      :verified_phone_number,
      :otp,
      rdo_attributes:[
        :designer_id,
        line_items_attributes: [
          :id,
          :design_id,
          :return_quantity,
          :return_reason,
          return_image: [
            :filename,
            :content,
            :content_type
          ]
        ]
      ]
    )
  end

  def return_params
    params.require(:return).permit(
      :notes,
      :reason,
      :order_id,
      :account_holder_name,
      :bank_name,
      :branch,
      :ifsc_code,
      :account_number,
      return_designer_orders_attributes: [
        :id,
        :notes,
        :designer_id,
        :tracking_company,
        :tracking_number,
        :return_type,
        line_item_ids: []
      ]
    )
  end

  def set_result
    if @app_source.downcase.include?('android') && ALLOWED_APP_VERSIONS[40..-1].include?(request.headers['App-Version']) || @app_source.downcase.include?('ios') && ALLOWED_IOS_APP_VERSIONS[7..-1].include?(request.headers['App-Version'])
      @returns = load_returns.page(params[:page]).per(20)
      head :no_content if @returns.blank?
    else
      head :no_content
    end
  end

  def load_returns
    if params[:order_number].present? && (returns = current_user.returns.joins(:order).where(order_number: params[:order_number])).includes(ASSOCIATIONS)
      params[:category] == 'Refund' ? returns.where(state: 'payment_complete') : returns
    elsif params[:action] == 'completed'
      current_user.returns.where('returns.state' => 'payment_complete').includes(ASSOCIATIONS).order('created_at desc')
    elsif params[:action] == 'pending'
      current_user.returns.where.not('returns.state' => ['payment_complete','canceled']).includes(ASSOCIATIONS).joins(return_designer_orders: :line_items).where.not('return_designer_orders.state' => ['buyer_canceled', 'goodwill_adjusted']).uniq.order('created_at desc')
    else
      current_user.returns.includes(ASSOCIATIONS)
    end
  end

end
