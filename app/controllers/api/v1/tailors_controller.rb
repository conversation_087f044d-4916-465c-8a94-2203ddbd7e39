class Api::V1::TailorsController < ApiController

  before_action :set_tailor
  before_action :match_tailor_password, only: :login, if: :new_version?
  before_action :check_if_version_serviceable, except: [:login]
  before_action :set_tailoring_batch, only: :update_tailoring_batch
  caches_action :delayed_products, expires_in: API_CACHE_LIFESPAN.minutes

  def login
    @is_master = is_master
    render json: { errors: "#{params[:contact_no]} is not registered with us. Please enter correct contact number and try again." }, status: 422 unless @tailor.present?
  end

  def pending_products
    if @tailor.present?
      query = params[:batch_id].blank? ? :fetch_info : [:batch_info, params[:batch_id]]
      @tailoring_info = @tailor.tailoring_infos.only_line_items.preload(item: [:stitching_measurements,design: :designable]).public_send *query
      close_the_tailoring_batch(params[:batch_id]) if params[:batch_id].present? && @tailoring_info.blank?
      user_ids = []
      @tailoring_info.each{|tailor_info| user_ids << tailor_info.item.stitching_measurements.collect(&:user_id).uniq}
      user_ids.flatten!.try(:compact!)
      @user_photos = User.where('full_size_photo_file_size is not null and id IN (?)', user_ids).collect{|i| [i.id ,i.full_size_photo.url]}.to_h
      @tailor_receiving_lpst_day = OperationProcess.tailor_receiving_lpst_days.days
    else
      head :no_content
    end
  end

  def tailor_reviews
    if @tailor.present?
      select_clause = 'distinct reviews.*, orders.number as order_number'
      @tailor_all_reviews = Review.select(select_clause).joins(survey_answers: :survey_question, order: [line_items: [tailoring_info: :tailor]]).where(survey_questions: {id: [8,9,10]}).where('reviews.design_id = line_items.design_id and tailors.id = ?', @tailor.id).where('reviews.created_at > ?',4.months.ago).order('reviews.created_at') || []
    end
  end

  def create_issue
    if (tailoring_info = TailoringInfo.only_line_items.find(issue_params[:tailoring_info_id])).present? && tailoring_info.tailor_id == @tailor.id && issue_params[:issue_notes].present?
      tailoring_info.tailoring_issues.create(issue: params[:issue_notes])       
      head :ok and return
    else
      head :bad_request and return
    end
  end

  def mark_not_with_me
    if (tailoring_info = TailoringInfo.only_line_items.find(params[:tailoring_info_id])).present?
      tailoring_info.update_attribute(:active, false)
      head :ok and return
    else
      head :bad_request and return
    end
  end

  def tailoring_batch_index
    @tailoring_batches = if params[:summary].present?
      check_worker_no
      @tailor.summary_batches
    else
      @tailor.open_batches
    end.joins(:tailoring_infos).preload(:tailoring_infos).merge(TailoringInfo.only_line_items).distinct
    @tailor_receiving_lpst_day = OperationProcess.tailor_receiving_lpst_days.days
    head :no_content unless @tailoring_batches.present?
  end

  def update_tailoring_batch
    head begin
      action = valid_action[params[:event]]
      @tailoring_batch && action && @tailoring_batch.public_send(action) ? :ok : :bad_request
    end and return
  end

  def tailor_next_visit
    if request.post?
      next_visit = Time.zone.parse(params[:next_visit]) rescue nil
      if @visit = @tailor.tailor_visits.where('next_visit::date = ?', next_visit).first
        @visit.touch
      else
        @visit = @tailor.tailor_visits.build(next_visit: next_visit)
      end
      unless @visit.save
        render json: {errors:  @visit.errors.messages}, status: 422
      else
        head :ok and return
      end
    else
      if visit = @tailor.tailor_visits.order(:updated_at).last
        render json: {visit: visit.next_visit.try(:strftime,'%d-%m-%Y')}
      else
        head :no_content and return
      end
    end
  end

  def delayed_products
    if @tailor.present?
      @tailoring_info = @tailor.tailoring_infos.only_line_items.delayed_products
      @tailor_receiving_lpst_day = OperationProcess.tailor_receiving_lpst_days.days
    else
      head :no_content
    end
  end

  def dashboard
    metric_data = MetricValue.fetch_latest('ProductAlterationRate', 'ReassignToTailorRate', 'TailorInscanDelayRate')
    metric_data.default = 0.0

    statistic_data = @tailor.statistics.fetch_latest('tailor_average_time_taken')
    statistic_data.default = 0.0

    render locals: {
      delay_ratio: metric_data['TailorInscanDelayRate'],
      alteration_ratio: metric_data['ProductAlterationRate'],
      reassign_to_tailor_ratio: metric_data['ReassignToTailorRate'],
      average_time_taken: statistic_data['tailor_average_time_taken']
    }
  end

  def tailoring_bag_scan
    if @tailor.present?
      preload_array = [tailoring_infos: [item: [:stitching_measurements, :design, designer_order: [:rack_list, :order]]]]
      if (@tailor_scanned_bag = TailoringBatch.preload(preload_array).where(batch_code: params[:bag_scan]).first)
        @bag_product_details = @tailor_scanned_bag.get_product_barcodes
      else
        render json: {errors: 'Tailoring Bag Not Found.'}, status: 404
      end
    else
      head :no_content
    end
  end

  def product_scan
    if @tailor.present?
      unless (@scanned_product = get_scanned_product_details).present?
        render json: {errors: 'Scanned Product Not Found.'}, status: 404
      end
      TailoringInfo.set_product_scanned_date(@tailor.id, @scanned_product.id) if is_master
    else
      head :no_content
    end
  end


  def urgent_stitchable_products
    if @tailor.present?
      @urgent_products = TailoringInfo.urgent_products.only_line_items
    else
      head :no_content
    end
  end


  private

  def get_scanned_product_details
    params[:item_id].present? ? get_details_by_item_id(item_id: params[:item_id].first(9)) : get_details_by_barcode
  end

  def get_details_by_item_id(item_id: nil)
    LineItem.preload(get_item_preload_array_for_product_scan).where(id: item_id).last
  end

  def get_item_preload_array_for_product_scan
    [:stitching_measurements, [design: :images], [line_item_addons: :addon_type_value], [order: :stylist]]
  end

  def get_details_by_barcode
    if (scan_input = params[:product_scan]).present?
      splitted_barcode = scan_input.split('-')
      get_item_details_based_on_barcode(scan_barcode_array: splitted_barcode)
    end
  end

  def get_item_details_based_on_barcode(scan_barcode_array: [])
    barcode_data_size = scan_barcode_array.length
    if barcode_data_size == 2
      get_details_by_item_id(item_id: scan_barcode_array[1].first(9))
    elsif (is_inscan_barcode = (barcode_data_size == 4)) || (barcode_data_size == 5 && ['B', 'D', 'F'].include?(scan_barcode_array[4]))
      item_id = (is_inscan_barcode ? scan_barcode_array[3] : scan_barcode_array[2])
      get_details_by_item_id(item_id: item_id.first(9))
    elsif barcode_data_size == 5
      get_item_details_from_rack_barcode(order_number: scan_barcode_array[1], design_id: scan_barcode_array[4].first(9))
    end
  end

  def get_item_details_from_rack_barcode(order_number: nil, design_id: nil)
    LineItem.joins(:order).
    preload(get_item_preload_array_for_product_scan).
    where('orders.number = ? and design_id = ?', order_number, design_id).first
  end

  def issue_params
    params.permit(:tailoring_info_id, :issue_notes, :contact_no)
  end

  def set_tailor
    @tailor = Tailor.where('contact_no = :contact_no or worker_contanct_no = :contact_no',contact_no: params[:contact_no]).first  
    head :bad_request and return if @tailor.nil?
  end

  def set_tailoring_batch
    @tailoring_batch = @tailor.tailoring_batches.find_by_id(params[:batch_id])
  end

  def match_tailor_password
    input_encrypted_password = Digest::SHA1.hexdigest(params[:password].to_s + @tailor.salt)    
    head :bad_request and return unless (@tailor.encrypted_password == input_encrypted_password)
  end

  def check_if_version_serviceable
    version_serviceable = SHOW_TAILOR_PRODUCT_CONTENT['content'] || SHOW_TAILOR_PRODUCT_CONTENT['app_versions'].to_a.include?(params[:app_version])
    head :no_content and return unless version_serviceable
  end

  def valid_action
    {
      'start' => :start_batch,
      'close' => :close_batch
    }
  end

  def close_the_tailoring_batch(batch_id)
    t_batch = TailoringBatch.find_by_id(batch_id)
    t_batch.try(:forced_close_batch!) if t_batch.try(:can_forced_close_batch?)
  end

  def new_version?
    params[:app_version].present?
  end

  def check_worker_no
    head :bad_request and return if is_slave
  end

  def is_master
    !is_slave
  end

  def is_slave
    params[:contact_no] != @tailor.contact_no
  end
end