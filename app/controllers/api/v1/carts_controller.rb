class Api::V1::CartsController < ApiController
  include ApplicationHelper
  before_action :set_min_cart_value, only: [:show, :payment_details]
  before_action :set_city_for_app_request, :probable_shipping_country, only: [:show]
  before_action :set_cart, only: [:show, :quick_cod]

  skip_before_filter  :user_currency, :api_user_details, :set_api_data, :set_paper_trail_whodunnit, only: [:line_item_count]
  # Common associations required for cart
  ASSOCIATIONS = [line_items: [[design: :designer], variant: [
    option_type_values: :option_type], line_item_addons: :addon_type_value]]

  # Provides User Cart
  #
  # == Returns:
  # JSON
  #
  def show
    @cart.remove_existing_referral(current_user)
    if (@wallet = @cart.wallet).present?
      @wallet.referral_amount = @cart.wallet_referral_amount(@country_code)
    end
    @api_account = ApiData.find_by_device_id(request.headers['Device-ID'])
  end

  # Check COD and CBD Availability
  #
  # == Returns:
  # JSON
  #
  def payment_details
    enable_cod = true
    if @country_code == 'IN' && GOKWIK_CONFIG['enable_gk_on_api']
      enable_cod =  call_gokwik_rto_api
    end
    @details = current_cart.payment_details(payment_details_params.merge({enable_cod: enable_cod}), @rate, @min_cart_value, @app_source.downcase, @country_code, request.headers['App-Version'], request.headers['Sub-App'])
    @juspay_enable = @country_code == 'IN' && payment_details_params[:billing][:country] == 'India' && rand(1..ENV['JUSPAY_EXPRESS_CHECKOUT'].to_i) == 1
    @payment_offers = OfferPanel.active_domestic_offers(@app_source.downcase) if @juspay_enable
  end

  def call_gokwik_rto_api
    res = get_response_data_for('gokwiks_predict_rto_risk', params.merge({request_method: "POST",
      cart_id: current_cart.id,
      session: {
      utm_medium: session[:utm_medium], utm_source: session[:utm_source], utm_campaign: session[:utm_campaign],
      remote_ip: request.remote_ip, user_agent: request.user_agent
    }}))
    cod_enable = (res["data"].present? ? (GOKWIK_CONFIG["risk_flags"].include?(res["data"]["risk_flag"].downcase) ? false : true) : true) if !GOKWIK_CONFIG['enable_cod']
  end
  # Assigns coupon to given Cart
  #
  # PUT user/cart/assign_coupon?code="#{coupon_code}"
  #
  # == Returns:
  # JSON
  #
  def assign_coupon
    if params[:code].blank?
      current_cart.errors.add(:coupon, 'not found')
    else
      # Determine coupon type based on country code (similar to web controller)
      coupon_region = determine_coupon_region
      
      # Enhanced coupon lookup with geo filtering
      coupon = find_coupon_with_geo_logic(params[:code], coupon_region)
      
      if coupon.blank?
        current_cart.errors.add(:coupon, 'not found')
      elsif !coupon.applicable_for_country?(@country_code)
        current_cart.errors.add(:coupon, 'not valid for your country')
      elsif current_cart.assign_check_coupon?(coupon)
        current_cart.save
      end
    end

    response_for current_cart
  end

  def remove_coupon
    current_cart.update(coupon_id: nil)
    response_for current_cart
  end

  def line_item_count
    render :json => {count: current_cart.line_items.sum(:quantity)}
  end

  def generate_otp
    otp_sent = false
    if (address_id = params[:cart][:address_id].to_i) > 0
      otp_sent = current_cart.try{|cart| cart.generate_otp(Address.find_by_id(address_id).try(:phone).to_s, params[:cart][:resend])}
    end
    render json: {otp_sent: otp_sent.present?}
  end

  def verify_otp
    if current_cart.try(:otp) == params[:cart][:otp].to_s
      current_cart.update_attribute(:otp, 'verified')
      render json: { verified: true } and return
    else
      head :bad_request
    end
  end

  def quick_cod
    if @cart.line_items.present? && params[:pincode].present?
      @response = @cart.quick_cod_cart(params, @app_source, @rate)
    else
      head :bad_request
    end
  end

  private
  # Rails Strong parameters implementation
  # Blocking unwanted values
  #
  def payment_details_params
    params.require(:location).permit(
      billing: [:pincode, :country, :id], shipping: [:pincode, :country, :id]
    )
  end

  def probable_shipping_country
    @shipping_country = (current_user.present? && (address = current_user.default_address).present?) ? address.country : @actual_country
    @shipping_city = address.present? && address.country.try(:downcase) == 'india' ? (address.city.presence || @delivery_city) : @delivery_city
  end

  def set_min_cart_value
    @min_cart_value = INTERNATIONAL_MIN_CART_VAL.to_i #@app_source.include?('Android') && (min = CurrencyConvert.find_by_country_code(@country_code).try(:min_cart_value).to_i).nonzero? ? min : INTERNATIONAL_MIN_CART_VAL.to_i
  end

  def set_cart
    @cart = current_cart(ASSOCIATIONS)
    @cart.remove_existing_invalid_coupon
    @messages, @imp_msg = @cart.get_messages(@rate, @hex_symbol, @country_code, @actual_country, @app_source.split('-',2)[0], current_user, @min_cart_value, request.headers['App-Version'], @shipping_country)
    set_cart_promotion
  end
  
  def determine_coupon_region
    is_domestic? ? "Domestic" : "International"
  end

  def find_coupon_with_geo_logic(code, coupon_region)
    return nil if code.blank? || coupon_region.blank?
    
    query = Coupon.where("LOWER(code) = ?", code.downcase)
                  .where("geo = ? OR geo = ?", coupon_region, "All")
                  .mirraw_coupons
    
    # Add app source filtering - this handles both source and platform
    if @app_source.present?
      query = query.by_source(@app_source.downcase)
    end
    
    query.first
  end
end
