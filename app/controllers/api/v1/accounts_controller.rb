class Api::V1::AccountsController < ApiController

  skip_before_filter :validate_headers?, only: [:send_otp, :verify_email_before_login, :verify_only_otp]

  require 'one_time_password_service'

  def send_otp
    account_exists = Account.find_by_phone(params[:phone]) ? true : false
    sms_sent = false
    account_status_new = false
    if account_exists
      sms_sent = sent_sms_service(params[:phone])
    elsif ENABLE_AUTO_SIGNUP_WITH_MOBILE
      params[:password] = params[:password_confirmation] = Devise.friendly_token.first(6)
      response = HTTParty.post(api_account_registration_url, body: params)
      if response['status'] == "success"
        account_exists = true
        sms_sent = sent_sms_service(params[:phone])
        account_status_new = true
      end
    end

    render json: render_json_response(account_exists, sms_sent, account_status_new)
  end

  def verify_email_before_login
    account_exists = Account.exists?(email: params[:email])
    render json: render_json_response(account_exists, false, false, login_type: 'Email id')
  end

  def verify_only_otp
    acc = Account.find_by_phone params[:user_phone]
    if acc && acc.verify_otp(params[:one_time_password])
      render json: {error: false, message: 'OTP verified'}, status: 200 and return
    end
    render json: {error: true, message: 'Account not found / Incorrect OTP'}, status: 401
  end

  def update_user_profile
    acc = Account.find_by_id params[:account_id]
    if acc
      user = acc.accountable
      if params[:email]
        acc.email = params[:email]
        user.email = params[:email]
      end
      user.first_name, user.last_name = params[:name].split if params[:name]
      acc.phone = params[:phone] if params[:phone]
      if acc.save
        user.save
        acc.reset_password(params[:password], params[:password]) if params[:password].present?
      else
        render json: {error: true, message: acc.errors.full_messages.join(", ")}, status: 401 and return
      end
      render json: {error: false, message: 'Account Updated'}, status: 200 and return
    end
    render json: {error: true, message: 'Account not found'}, status: 401
  end

  private

  def render_json_response(account_exists, sms_sent, account_status_new, login_type: 'Mobile number')
    response = {
      error:    !account_exists,
      sms_sent: sms_sent,
      message: "",
      account_status_new: account_status_new
    }
    response[:message] = "#{login_type} does not exist. Please sign up" if !account_exists
    response
  end

  def sent_sms_service(phone)
    # response = OneTimePasswordService.deliver('91' + params[:phone], ":OTP: is your one time password to proceed on Mirraw. It is valid for 10 minutes. Do not share your OTP with anyone.", 4)
    response = OneTimePasswordService.deliver('91' + params[:phone], "[PHONE_NUMBER], OTP:[OTP_NUMBER] is your one time password to proceed on Mirraw. It is valid for 10 minutes. Do not share your OTP with anyone.", 4)
    return response.include?('MsgID') ? true : false
  end

end
