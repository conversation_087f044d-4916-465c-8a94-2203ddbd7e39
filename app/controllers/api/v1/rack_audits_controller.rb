class Api::V1::<PERSON><PERSON><PERSON><PERSON>tsController < ApiController
  before_action :is_action_allowed, except: [:login]

  def login
    @allow_weekly_audit = RackAudit.check_if_weekly_audit
    render json: {error: "#{params[:emp_code]} is not registered for rack audit. Please correct Emp code and try again."}, status: 422 unless check_if_valid_login
  end

  def get_audit_rack
    @auditable_rack = RackAudit.get_auditable_rack(@auditor_account, @auditor_admin, params[:audit_type], params[:auditor_level])
    if @auditable_rack
      @valid_product_barcodes = @auditable_rack.try(:get_rack_product_barcodes).to_h if @auditable_rack.try(:audit_level) == 'L2'
      @previous_audit_details = @auditable_rack.try(:get_previous_audit_details).to_h if @auditable_rack.try(:audit_level) == 'L3'
    else
      render json: {error: 'No Rack Found'}, status: 422
    end
  end

  [:submit_product_count, :skip_rack_audit, :mark_resolved_rack_audit, :submit_product_scan, :mark_rack_empty].each do |action_name|
    send :define_method, action_name do
      rack_audit = get_current_rack_audit
      if rack_audit
        rack_audit.public_send(*get_callable_rack_audit_method[action_name])
        head :ok and return 
      else
        head :bad_request and return
      end 
    end
  end

  private

  def get_callable_rack_audit_method
    {
      submit_product_count: [:store_product_count, params[:product_count].to_i],
      skip_rack_audit: [:skip_audit],
      mark_resolved_rack_audit: [:audited_successfully],
      mark_rack_empty: [:mark_rack_audit_complete],
      submit_product_scan: [:store_product_scan_details, params[:product_scan_id], params[:scanned_barcode]]
    }
  end

  def get_current_rack_audit
    RackAudit.find_by_id(params[:rack_audit_id].to_i)
  end

  def check_if_valid_login
    get_auditor_account.present?
  end

  def is_action_allowed
    head :bad_request and return unless check_if_valid_login
  end

  def get_auditor_account
    @auditor_account = get_auditor_admin_record.try(:account)
  end

  def get_auditor_admin_record
    @auditor_admin = Admin.get_rack_auditor_account(params[:emp_code])
  end

end