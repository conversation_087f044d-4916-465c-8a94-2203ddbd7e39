class Api::V1::CountriesController < ApiController

  caches_action :index, expires_in: API_CACHE_LIFESPAN.minutes

  # Provides names and codes for all countries.
  #
  # == Returns:
  # JSON
  #
  def index
    @countries = Country.priority_wise_countries
    head :no_content if @countries.blank?
  end

  # Provides names and codes for the country whose id is passed in params
  #
  # == Returns:
  # JSON
  #
  def states
    @country_id = params[:id].to_i == 0 ? Country.find_by_iso3166_alpha2(params[:country_code]).try(:id) : params[:id].to_i
    @states = State.order(:name).where(country_id: @country_id)
    head :no_content if @states.blank?
  end
end
