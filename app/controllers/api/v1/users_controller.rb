class Api::V1::UsersController < ApiController

  before_action :authenticate_api_account!, :authenticate_user!, only: %i[show update bank_details reviewer update_notification_settings update_privacy_setting notification_settings cancel_cod_order]
  skip_before_filter :set_paper_trail_whodunnit, only: [:notification_settings, :following_designers, :following_users, :wishlist_designs]
  # Provides User Details
  #
  # GET /user
  #
  # == Returns:
  # JSON
  #
  def show
    @user = current_user
    head :no_content if @user.blank?
  end

  def profile
    @curr_user = current_user
    @user = User.find_by_id(params[:user_id])
    head :no_content if @user.blank?
  end

  def following_designers
    @curr_user = current_user
    @user = User.find_by_id(params[:user_id])
    @designers = @user.following_by_type('Designer').page(params[:page]).per(params[:per] || 20) if @user.present?
    head :no_content if @designers.blank?
  end

  def following_users
    @curr_user = current_user
    @user = User.find_by_id(params[:user_id])
    @users = @user.following_by_type('User').page(params[:page]).per(params[:per] || 20) if @user.present?
    head :no_content if @users.blank?
  end

  def friends
    @curr_user = current_user
    @user = User.find_by_id(params[:user_id])
    @friends = @user.followers_by_type('User').page(params[:page]).per(params[:per] || 20) if @user.present?
    head :no_content if @friends.blank?
  end

  def wishlist_designs
    if (user = User.find_by_id(params[:user_id])).present? && (user.account.nil? || (api_data = user.account.api_data).nil? || api_data.wishlist_public || (params[:show] == 'true'))
      @wishlist_designs = user.wishlisted_designs(params[:wishlist_ids])
      .page(params[:page]).per(params[:items_per_page] || 20)
      @user_wishlist_designs = Wishlist.get_ids_if_logged_in(current_user)
      head :no_content if @wishlist_designs.blank?
    else
      render json: { errors: "Wishlist is private" }, status: 422
    end
  end

  # Updates user 'model' attributes
  #
  # PATCH /user
  #
  # == Returns
  # Nil / JSON
  #
  def update
    if (user = current_user)
      user.update_attributes(user_params)
    end
    response_for user
  end

  def cancel_cod_order
    if current_api_account.present? && (current_user = current_api_account.user).present?
      order = Order.where(user_id: current_user.id,number: params[:order_number]).first
      if order.present? && order.cod? && order.check_not_picked_up
        order.order_notification['order_cancelled'] = 1
        order.save
        Order.sidekiq_delay.cancel_order_mobile(order.id)
        order.save_order_cancel_reason(params[:order_cancel_reason], current_api_account)
        render json: { message: "Order Cancelled Successfully! Will take time to Update" }, status: 200 and return
      else
        render json: { errors: "Order cannot be cancelled, <NAME_EMAIL>" }, status: 422 and return
      end
    else
      render json: { errors: "Not authorized" }, status: 401 and return
    end
  end

  # Provides User Bank Details
  #
  # GET api/v1/user/bank_details
  #
  # == Returns:
  # JSON
  #
  def bank_details
    @bank_details = current_user.bank_details.order('updated_at DESC')
    head :no_content  if @bank_details.blank?
  end

  def update_notification_settings
    if current_api_account && (settings = current_api_account.api_data.api_notification_settings).present?
      response_for settings.update_settings(params)
    else
      head :unauthorized
    end
  end

  def update_privacy_setting
    if current_api_account
      response_for current_api_account.api_data.update_wishlist_privacy(params[:wishlist_public])
    else
      head :unauthorized
    end
  end

  def notification_settings
    if (account = current_api_account).present?
      @settings = account.api_notification_settings
      head :no_content unless @settings
    else
      head :unauthorized
    end
  end

  def user_history
    @user_data = {}
    @orders    = {}
    if params[:email].present?
      @user = User.where(email: Base64.urlsafe_decode64(params[:email])).first
      orders = @user.present? ? @user.orders.where(state: ['sane','dispatched','ready_for_dispatch']).where('created_at >= ?', 6.months.ago).order('created_at desc').to_a : []
      @user_data[:total_orders] = orders.count
      @user_data[:total_amount] = orders.sum(&:total)
      orders.first(10).each do |order|
        order_detail ={}
        order_detail[:state] = order.state
        order_detail[:amount] = order.total
        order_detail[:date] = order.confirmed_at.try(:strftime, '%a %d, %B %Y').presence || order.created_at.try(:strftime, '%a %d, %B %Y')
        @orders[order.number] = order_detail
      end
    end
  end

  def reviewer
    current_user.can_review(params[:design_id].to_i) > 0 ? head(:ok) : head(:forbidden)
  end

  private

  # Permitting only allowed params
  #
  def user_params
    params.require(:user).permit(:first_name, :last_name, :image_url, :birthday, :gender)
  end
end
