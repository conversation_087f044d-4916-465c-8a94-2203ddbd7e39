class Api::V1::BankDetailsController < ApiController

  before_action :authenticate_api_account!, :authenticate_user!

  # Provides the bank_details of given user_id
  #
  # GET /user/bank_details
  #
  # == Returns
  # JSON
  #
  # def index
  #   @bank_details = current_user.bank_details.order('id DESC')
  #   head :no_content if @bank_details.blank?
  # end

  # Creates bank_detail for given current_user.
  #
  # POST /user/bank_detail
  #
  # == Returns
  # Nil
  #
  def create
    bank_detail = current_user.create_bank_detail(bank_details_params)
    if (errors = bank_detail.errors).any?
      render_error_msg(errors) and return
    end
    response_for bank_detail
  end

  # Provides the bank_detail for given current_user.
  #
  # GET /user/bank_detail
  #
  # == Returns
  # JSON
  #
  def show
    @bank_detail = current_user.bank_detail
    head :no_content if @bank_detail.blank?
  end

  # Updates the bank_detail with given params
  #
  # [PATCH/PUT] /user/bank_detail
  #
  # == Returns
  # JSON
  #
  def update
    if (bank_detail = current_user.bank_detail)
      bank_detail.update_attributes(bank_details_params)
      if (errors = bank_detail.errors).any?
        render_error_msg(errors) and return
      end
    end
    response_for bank_detail
  end

  # Deletes bank_detail for user
  #
  # DELETE /user/bank_detail
  #
  def destroy
    if bank_detail = current_user.bank_detail
      bank_detail.destroy
    end
    response_for bank_detail
  end


  private
  def bank_details_params
    params.require(:bank_details).permit(
      :account_holder_name,
      :bank_name,
      :branch,
      :ifsc_code,
      :account_number
    )
  end

  def render_error_msg(errors)
    if errors[:ifsc_code].present?
      render json: { errors: 'IFSC code is invalid' }, status: 422
    elsif errors[:account_number].present?
      render json: { errors: errors[:account_number].first }, status: 422
    end
  end

end