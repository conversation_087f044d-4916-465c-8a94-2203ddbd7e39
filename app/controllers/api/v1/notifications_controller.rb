class Api::V1::NotificationsController < ApiController
  before_action :authenticate_api_account!, :authenticate_user!

  def show
    if (notification = current_user.try(:notification)).present?
      active_notifications = notification.body.map do |status_hash|
        if status_hash['active']
          status_hash['active'] = false
          status_hash
        end
      end.compact
      if active_notifications.present?
        notification.save!
        render json: {customer_panel_notification: active_notifications}
      else
        head :no_content
      end
    else
      head :no_content
    end
  end

end