class Api::V1::DeviseOverrides::RegistrationsController < DeviseTokenAuth::RegistrationsController

  include MirrawUtil

  before_filter :user_currency
  # skip_before_filter :verify_authenticity_token

  # Assign the cart to user after after log in
  #
  # REF: https://github.com/lynndylanhurley/devise_token_auth#passing-blocks-to-controllers
  #
  def create
    if !params.has_key?(:phone) || (params.has_key?(:phone) && Account.find_by_phone(params[:phone]).blank?)
      params[:email] = Faker::Name.first_name+Faker::Number.number(10)+"<EMAIL>" if params[:email].blank?
      super do |resource|
        user = User.where(email: @resource.email).first_or_initialize
        app_versions = ALLOWED_APP_VERSIONS[22..-1] | ALLOWED_IOS_APP_VERSIONS[5..-1]
        if (REFERRALS_ACTIVE == 'true') && user.new_record? && (params[:referral] == 'true') && ApiData.find_by_device_id(request.headers['Device-ID']).nil? && (referrer_email = User.find_by_id(params[:referrer]).try(:email)).present?
          user.assign_attributes(referral: true)
          Referrer.new_referral(referrer_email, @resource.email, (REFERRAL_TYPES - ['new_user_wallet']).first)
          @new_referral_account = true
        elsif REFERRAL_TYPES.include?('new_user_wallet') && user.new_record? && WELCOME_OFFER_COUNTRIES.include?(@country_code) && app_versions.include?(request.headers['App-Version']) && ApiData.find_by_device_id(request.headers['Device-ID']).nil?
          user.assign_attributes(referral: true)
          Referrer.new_referral('<EMAIL>', @resource.email, 'new_user_wallet')
        end
        user.save
        @resource.update_attributes(accountable: user, fcm_registration_token: request.headers['Fcm-Token'], phone: params[:phone], dial_code: params[:dial_code])
        # Send registration details to user for guest login
        guest = params[:guest_user] == 'true'
        @resource.guest_account = TRUE if guest
        @resource.send_registration_details(params[:password], edit_password_path(@resource, reset_password_token: @resource.set_reset_password_token)) if guest
        @resource.add_to_subscribers("api/#{guest ? 'guest' : 'email'}/login",
                                     @country_code,
                                     "API/#{request.headers['App-Source']}",
                                     request.headers['True-Client-IP'] || request.ip)

        # Skip confirmation if value is set in system constant
        @resource.skip_confirmation! if SystemConstant.find_by_name('SKIP_ACCOUNT_CONFIRMATION').value == 'true'
      end
    else
      error = {errors: "mobile number already exist."}
      if request.headers['Api-Version'].to_i >= 2
        if (acc = Account.find_by_phone(params[:phone])) && (acc.email != params[:email]) && !(email_exixst = Account.exists?(email: params[:email]))
          email = acc.email.split('_')[-1] == "<EMAIL>" ? nil : acc.email
          msg = email ? "Mobile number is linked with #{email}. Do you want to update the email?" : "Mobile number already present. Do you want to link this email?"
          error = {errors: msg, account_email: email, account_id: acc.id, link_account: true}
        else
          error = {errors: "Mobile and Email already present. PLease Login", account_email: email, account_id: acc.id, link_account: false}
        end
      end
      render json: error, status: 422
    end
  end

  protected

  def sign_up_params
    params.permit devise_parameter_sanitizer.for(:sign_up) do |u|
      u.permit(:email, :password, :password_confirmation, :phone, :dial_code)
    end
  end

  def render_create_success
    user = @resource.user
    render json: {
      status: 'success',
      data: @resource.as_json.merge(
        new_referral_account: !!@new_referral_account,
        name: user.try(:full_name),
        image: user.try(:image_url) || "#{SITE_PROTOCOL}#{ENV['MOBILE_SITE_URL']}/" + ActionController::Base.helpers.image_url("fb_default.jpg"),
        gender: user.try(:gender),
        total_designers: user.try(:designers_count),
        total_users: user.try(:users_count),
        total_followers: user.try(:followers_count),
        referral_login: !!user.try(:referral)
      )
    }
  end
end
