class Api::V1::WishlistsController < ApiController
  before_action :authenticate_api_account!, :authenticate_user!, except: [:notified_wishlists, :create_like]
  before_action :set_response_header, only: [:create]

  def create
    if design = Design.find_by_id(params[:design_id])
      wishlist = Wishlist.where(user_id: current_user.id, design_id: design.id, state: 'added').first_or_initialize
      wishlist.update_attributes(conversion_rate: @rate, price: design.effective_price,
        country_code: @country_code, wish: true, app_source: @app_source)
      # design.increment_count(:wishlists_count)
      response_for wishlist
    else
      head :no_content
    end
  end

  def index
    params[:wishlist_ids] = params[:wishlist_ids].split(',') unless params[:wishlist_ids].blank?
    @current_user = current_user
    @wishlist_designs = @current_user.wishlisted_designs(params[:wishlist_ids])
    .page(params[:page]).per(params[:items_per_page] || 20)
    @user_wishlist_designs = Wishlist.get_ids_if_logged_in(@current_user)
    head :no_content if @wishlist_designs.blank?
  end

  def destroy
    if wishlist = current_user.wishlists.find_by_id(params[:id])
      wishlist.like ? wishlist.toggle!(:wish) : wishlist.destroy
      # wishlist.design.decrement_count(:wishlists_count)
    end
    response_for wishlist
  end

  def notified_wishlists
    wishlist_ids = params[:wishlist_ids].split(',')
    @wishlists = Wishlist.where(id: wishlist_ids).includes(:user, :design).order('created_at DESC').group_by(&:user_id)
    @wishlists = Kaminari.paginate_array(@wishlists.to_a).page(params[:page]).per(params[:per] || 20)
    @user_wishlist_designs = Wishlist.get_ids_if_logged_in(current_user)
    head :no_content if @wishlists.blank?
  end

  def likes
    @liked_designs = current_user.liked_designs.page(params[:page]).per(params[:items_per_page] || 20)
    head :no_content and return if @liked_designs.blank?
    @user_liked_designs = Wishlist.get_ids_if_logged_in(current_user, :like)
  end

  def create_like
    if params[:liked_designs].present? || params[:disliked_designs].present?
      Wishlist.generate_likes(params, current_user, @country_code, @rate)
      head :ok
    else
      head :bad_request
    end
  end

  def destroy_like
    if wishlist = current_user.wishlists.find_by_id(params[:id])
      wishlist.wish ? wishlist.toggle!(:like) : wishlist.destroy
    end
    response_for wishlist
  end
end