class Api::V1::WalletsController < ApiController

  EXPIRY_DATE = Date.parse(REFERRALS_EXPIRY)
  before_action :authenticate_api_account!, :authenticate_user!, except: [:apply_wallet_referral, :remove_wallet_referral, :apply_wallet_refund, :remove_wallet_refund]
  skip_before_filter :validate_headers?, :set_api_data ,only: [:apply_wallet_referral, :remove_wallet_referral, :apply_wallet_refund, :remove_wallet_refund]
  before_action :active_referrals, only: [:wallet]

  def wallet
    @currency = @wallet.currency_convert
  end

  def apply_referral
    wallet = current_user.wallet
    if (wallet.currency_convert.country_code == @country_code) && wallet.referral_amount.nonzero? && current_cart.coupon.nil?
      current_cart.apply_from_wallet(:referral_amount, wallet, @rate, @country_code)
    else
      wallet.errors.add(:error, 'Not Applicable')
    end
    response_for wallet
  end

  def remove_referral
    cart_wallet = current_cart.wallet
    if cart_wallet && cart_wallet.referral_amount.nonzero?
      current_cart.remove_from_wallet(:referral_amount)
      if cart_wallet.return_amount > 0
        current_cart.apply_from_wallet(:return_amount, current_user.wallet, @rate, @country_code)
      end
    end
    response_for current_cart
  end

  def apply_refund
    wallet = current_user.wallet
    @min_cart_value = INTERNATIONAL_MIN_CART_VAL.to_i #@app_source == 'Android' && (min = CurrencyConvert.find_by_country_code(@country_code).try(:min_cart_value).to_i).nonzero? ? min : INTERNATIONAL_MIN_CART_VAL.to_i
    if (wallet.currency_convert.country_code == @country_code) && wallet.return_amount.nonzero?
      current_cart.apply_from_wallet(:return_amount, wallet, @rate, @country_code, params['location']['shipping']['country'])
      request.env["sent_from_web_mobile"] || request.headers["Sent-from-Web-Mobile"] ? (render json: current_cart.payment_details(payment_details_params, @rate, @min_cart_value, @app_source.downcase, @country_code), status: 200) :
                            @details = current_cart.payment_details(payment_details_params, @rate, @min_cart_value, @app_source.downcase, @country_code, request.headers['App-Version'])
    else
      render json: { error: 'Not applicable' }, status: 422
    end
  end

  def remove_refund
    @min_cart_value = INTERNATIONAL_MIN_CART_VAL.to_i #@app_source == 'Android' && (min = CurrencyConvert.find_by_country_code(@country_code).try(:min_cart_value).to_i).nonzero? ? min : INTERNATIONAL_MIN_CART_VAL.to_i
    if current_cart.wallet && current_cart.wallet.return_amount.nonzero?
      current_cart.remove_from_wallet(:return_amount)
    end
    request.env["sent_from_web_mobile"] || request.headers["Sent-from-Web-Mobile"] ? (render json: current_cart.payment_details(payment_details_params, @rate, @min_cart_value, @app_source.downcase, @country_code), status: 200) :
                          @details = current_cart.payment_details(payment_details_params, @rate, @min_cart_value, @app_source.downcase, @country_code, request.headers['App-Version'])
  end

  %w(apply remove).each do |method|
    %w(referral refund).each do |type|
      send :define_method, "#{method}_wallet_#{type}" do
        request.env["sent_from_web_mobile"] = true
        if params[:user_id].present?
          @current_api_account ||= @current_account ||= User.find_by_id(params[:user_id]).try(:account)
        end
        current_user = set_current_api_account.try(:user)
        @cart = Cart.find_by_id(params[:cart_id])
        if current_user && @cart && @cart.belongs_to_user?(current_user)
          @app_source = 'web'
          headers['Access-Control-Allow-Origin'] = '*'
          @rate = params[:rate].to_f if params[:rate].present?
          @country_code = params[:country_code] if params[:country_code].present?
          params.merge!({location: {shipping: {country: params[:customer_location]}}}) if params[:customer_location].present?
          send "#{method}_#{type}"
        else
          render  json: {error: 'cart not found'}, status: 422
        end
      end
    end
  end

  private

  def active_referrals
    @wallet = current_user.wallet
    if REFERRAL_TYPES.include?('new_user_wallet') && @wallet && @wallet.promotional_balance.nonzero? && @wallet.updated_at < WALLET_EXPIRY_HOURS.to_i.hours.ago
      @wallet.update_attribute(:promotional_balance, 0.0)
      current_cart.remove_from_wallet(:return_amount) if current_cart.wallet.present?
    end
    if Time.current > EXPIRY_DATE && @wallet.referral_amount.nonzero?
      #@wallet.update_attribute(:referral_amount, 0.0)
      current_cart.remove_from_wallet(:referral_amount) if current_cart.wallet.present?
      @wallet.errors.add(:description, {valid: false, message: "Referral Money is no longer Applicable."})
    else
      @wallet.errors.add(:description, {valid: true, message: "Applicable upto #{EXPIRY_DATE.to_formatted_s(:long)}"})
    end
  end

  private
  # Rails Strong parameters implementation
  # Blocking unwanted values
  #
  def payment_details_params
    params.require(:location).permit(
      billing: [:pincode, :country, :id], shipping: [:pincode, :country, :id]
    )
  end

end