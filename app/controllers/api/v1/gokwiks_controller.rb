class Api::V1::GokwiksController < ApiController

  def predict_rto_risk
    response = {}
    begin
      if params[:location].present? && params[:location][:shipping][:id].present?
        gokwik_data = GokwikData.find_or_initialize_by({cart_id: params[:cart_id]})
        gd = Gokwik::GokwikData::PredictData.new(params)
        payload = gd.get_predict_api_payload
        gokwik_api = Gokwik::GokwikApis.new
        response = gokwik_api.predict_api_call(payload)
        if response["error"].blank? && (request_id = response["data"]["request_id"]).present?
          gokwik_data.update_attributes({request_id: request_id, risk_factor: response["data"]["risk_flag"]})
        else
          gokwik_data.update_attribute(:error, response["error"].to_s)
        end  
        gokwik_data.save!
      end
    rescue => exception
      handle_exception(exception)
    end
    respond_to do |format|
      format.json { render json: response }
    end
  end

  def create_order
    response = {}
    begin
      @order = Order.find params[:order_id]
      gd = Gokwik::GokwikData::CreateData.new(params)
      gokwik_api = Gokwik::GokwikApis.new
      gokwik_data = @order.gokwik_data
      payload = gd.get_create_order_payload
      response = {}
      if gokwik_data.gokwik_oid.blank? && gokwik_data.request_id.present? 
        response = gokwik_api.create_order_api_call(payload) 
      end
      if response["data"].present? && [200,201,202,203].include?(response["statusCode"])
        @order.other_details["gokwik_data"] = response["data"].to_json
        gokwik_data.update_attribute(:gokwik_oid, response["data"]["gokwik_oid"]) 
        @order.save
      end
    rescue => exception
      handle_exception(exception)
    end
    respond_to do |format|
      format.json { render json: response }
    end
  end

  def update_order
    response = {}
    begin
      gd = Gokwik::GokwikData::UpdateOrder.new(params)
      gokwik_api = Gokwik::UpdateOrder.new
      payload = gd.get_update_order_payload
      response = gokwik_api.get_update_order_payload(payload)
    rescue => exception
      handle_exception(exception)
    end
    respond_to do |format|
      format.json { render json: response }
    end
  end

  def split_order
    response = {}
    begin
      gd = Gokwik::GokwikData::SplitOrder.new(params)
      gokwik_api = Gokwik::GokwikApis.new
      payload = gd.get_split_order_payload
      response = gokwik_api.split_order_api_call(payload)
    rescue => exception
      handle_exception(exception)
    end
    respond_to do |format|
      format.json { render json: response }
    end
  end

  def handle_exception(exception)
    Order.sidekiq_delay.notify_exceptions("Gokwik Exception Mobile-API", exception.to_s, {})
  end

end