class Api::V1::TabsController < ApiController
  skip_before_filter :authenticate_api_account!, :set_paper_trail_whodunnit, :set_api_data, :validate_headers?, only: [:tab_boards, :tab_menus]
  skip_after_action :update_auth_header

  def tab_boards
    items_per_page = @app_source == 'mobile' ? 10 : 15
    @boards = Board.live.graded.country(@country_code).app_source(@app_source.split('-')[1] || @app_source).preload(:frontpages, :banner_sliders, :tag_sliders, :designs).joins(:tabs).where(:tabs => {id: params[:tab_id]})
              .where('board_type <> ?', "story_collection").page(params[:page]).per(items_per_page)
  end

  def tab_menus
    @app_source = @app_source.split('-')[0]
    country = "%#{@country}%"
    app_source = "%#{@app_source}%"
    @menus = Menu.preload(:menu_columns => :menu_items).joins(:tabs).where(:tabs => {id: params[:tab_id]})
  end
end