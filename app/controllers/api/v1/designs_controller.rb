class Api::V1::DesignsController < ApiController
  API_DEVICE_ID = WEB_DEVICE_ID
  include Api::V1::StoreHelper

  before_action :authenticate_api_account!, :authenticate_user!, only: [:add_remove_wishlist, :wishlist]
  before_action :set_response_header, :set_city_for_app_request, only: [:show]
  caches_action :similar_designs_unbxd, cache_path: Proc.new{ |p| "#{p.request.path}/#{@country_code}" }, expires_in: API_CACHE_LIFESPAN.minutes
  caches_action :preview, cache_path: Proc.new{ |c| "api/v1/designs/preview/#{c.params['ids']}/#{@country_code}"}, expires_in: API_CACHE_LIFESPAN.minutes
  skip_before_filter :set_menu, only: [:preview]
  # Provides Design for given id
  #
  # == Returns:
  # JSON
  #
  def show
    @design = current_design
    head :no_content and return if @design.blank?
    @current_user = current_user
    @review = @current_user.review_for(@design.id) if @current_user.present?
    @cart = current_cart
    @addon_products = get_designs_addon_products(@design)
    @wishlist_count = (count = @design.wishlists_count.to_i) < 10 ? count + WISHLIST_COUNT_OFFSET.to_i : count
    update_recently_viewed()
    estimate_days
    rescue Timeout::Error
      head :no_content
  end

  # Provides Design for given design id or title and designer_id or designer_name
  #
  # == Returns:
  # JSON
  #
  def slug
    if (@designer = Designer.find_by_cached_slug(params[:designer_id])).present? && (@design = @designer.designs.find_by_cached_slug(params[:id])).present?
      @addon_products = get_designs_addon_products(@design)
      #update_recently_viewed()
      estimate_days
      render 'show'
    else
      head :no_content
    end
  end

  def get_designs_addon_products(design)
    design.available? ? design.in_stock_addon_products.includes(:designer,:images,:master_image,categories: :default_size_chart,variants: [option_type_values: :option_type]).limit(3) : []
  end


  def preview
    if (ids = params[:ids].try(:split,',')).present?
      in_catalog_one_value = Design.country_code == 'IN' ? 1 : 2
      preload_arr = [:designer, :master_image, :categories, :variants]
      preload_arr.push(:dynamic_price_for_current_country) if DYNAMIC_PRICE_ENABLED && Design.country_code != 'IN'
      winners = DesignCluster.where(design_id: ids).uniq.pluck(:winner_design_id,:design_id)
      ids = (ids.map(&:to_i)  - winners.collect(&:last) + winners.collect(&:first)).uniq
      @designs = Design.where(id: ids, state: 'in_stock').where('in_catalog_one is null or in_catalog_one = ?', in_catalog_one_value).preload(preload_arr)
    end
    head :no_content if @designs.blank?
  end

  # Check Cod Availability
  #
  # == Returns:
  # JSON
  #
  def cod
    if current_design.present? && (pincode = params[:pincode]).present?
      pincode_to_int = pincode.to_i
      @result = Pincode.exists?(pin_code: pincode) && current_design.designer.can_cod?(pincode) && !LOCKDOWN_COD_DISABLE_PINCODES.include?(pincode_to_int) 
      if COD_REQUEST_ENABLE == 'true'
        app_source = request.headers['App-Source'] || 'Android'
        CodRequest.where(ip: request.remote_ip, app_source: app_source,
          pincode: params[:pincode], design_id: current_design.id, is_cod_available: @result).first_or_create
      end
    else
      head :no_content
    end
  end

  def pdd_design
    response = {error: true, error_text: 'Unable to calculate, Please try another pincode', eta: 0}
    if params[:id].present? && params[:pincode].present? && (design = Design.find params[:id]).present?
      pickup = design.sor_available? ? 'bhiwandi' : design.designer.pickup_location.try(:downcase)
      drop_city = Pincode.where(pin_code: params[:pincode]).pluck(:city).first
      city_pdd = Design.get_city_based_pdd(drop_city, [pickup]) if drop_city.present? && pickup.present?
      if city_pdd.to_i > 0
        city_pdd += design.eta unless design.sor_available?
        city_pdd += (DESIGNER_LSR.hours.to_f/1.days).round.to_i unless design.sor_available?
        city_pdd += 1 if Time.current.advance(days: city_pdd).sunday?
        response = {error: false, error_text: '', eta: (Time.now + city_pdd.days).strftime('%d %b, %Y')}
      end
    end
    render json: response
  end 

  def set_unbxd_flag
    design = Design.find_by_id params['id']
    success = design.update_column(:unbxd_status, 'indexed')
    render plain: design.errors.full_messages.join(', '), status: success ? 200 : 500
  end

  def unbxd_xml
    if (item = UnbxdItem.find_by_unique_id(params['id'])).present?
      design = Design.includes(:categories,:designer).find_by_id item.unique_id
      if (design.state == 'sold_out' || design.state == 'in_stock') &&
        ( design.designer.state_machine == 'approved' || design.designer.state_machine == 'vacation' || design.designer.state_machine == 'review')
        category = design.categories.try(:first)
        text = item.create_xml(category)
        render :text => text, :content_type => 'text'
      else
        render text: 'Design does not meet criteria', status: 200
      end
    else
      render nothing: true, status: 404
    end
  end

  def similar_designs
    @similar_designs = Design.similar_designs(current_design)
    head :no_content if @similar_designs.blank?
  end

  def similar_designs_unbxd
    @similar_designs_unbxd = Design.similar_designs_unbxd(current_design, @user_id = current_account ? current_account.unbxd_user_id : nil, @country_code)
    head :no_content if @similar_designs_unbxd.blank?
  end

  def recommended_designs
    @recommended_designs_unbxd = Design.get_recommended_designs_unbxd(@user_id = current_account ? current_account.unbxd_user_id : nil, @country_code) if UnbxdWidget.unbxd_container_active?('recommended_for_you')
    head :no_content if @recommended_designs_unbxd.blank?
  end

  def recent_designs
    recent_items =
      if (request.headers['Device-ID'] == API_DEVICE_ID)
        RecentItem.where(session_id: session.id).order("updated_at desc").limit(20).pluck(:design_id)
      else
        RecentItem.where(device_id: request.headers['Device-ID']).limit(20).pluck(:design_id)
      end
    @history = Design.for_ids_with_order(recent_items).includes(:designer, :master_image).page(params[:page] || 1).per(20)
    #@wishlist_designs = Wishlist.get_ids_if_logged_in(current_user)
    head :no_content if @history.blank?
  end

  def wishlist
    if current_user
      @wishlist = current_design.wishlist_for_user(current_user.id).present?
    else
      head :no_content
    end
  end

  def remove_wishlist
    if current_user
      if (wishlist = current_design.wishlist_for_user(current_user.id)).present?
        wishlist.like ? wishlist.toggle!(:wish) : wishlist.destroy
        # current_design.decrement_count(:wishlists_count)
      end
      # Used status 201 instead of 200
      render json: wishlist, status: 201
    else
      head :unauthorized
    end
  end

  def autosuggests
    if unbxd_activated?
      url = unbxd_autosuggest_url
      @response = HTTParty.get(url)
      @response = JSON.parse(@response.body)['response']
      if @response['products'].blank?
        head :no_content
      else
        render :json => {
          source: 'unbxd',
          data: process_unbxd_autosuggest_response(@response)
        }
      end
    else
      @results  = solr_results(params.merge(autosuggest_term: params[:q]))
      if @results.raw_results.blank?
        head :no_content
      else
        render 'api/v1/designs/solr_autosuggestion'
      end
    end
  end

  def weekly_hot_selling
    design_ids = []
    @count = []
    params[:designs][1..-2].split('],[').each { |d| d = d.split(','); design_ids << d.first; @count << (d.last.to_i+86).to_s }
    @designs = Design.for_ids_with_order(design_ids).includes(:designer, :master_image)
    @designs = @designs.page(params[:page]).per(params[:per] || 20)
    @count = Kaminari.paginate_array(@count).page(params[:page]).per(params[:per] || 20)
    head :no_content if @designs.blank?
  end

  def reviews
    @curr_user = current_user
    @designs = Review.where('designer_id = ? AND review <> ?', params[:id].to_i, '')
                     .where.not(user_id: nil).approved.includes(:user, :design)
                     .order('updated_at DESC').group_by(&:design_id)
    @designs = Kaminari.paginate_array(@designs.to_a).page(params[:page]).per(params[:per] || 20)
    head :no_content if @designs.blank?
  end

  private

  def process_unbxd_autosuggest_response(response)
    @rate
    response['hex_symbol'] = @hex_symbol
    response['symbol'] = @symbol

    return response if response['products'].blank?

    response['products'].each do |product|
      if product['doctype'] == 'POPULAR_PRODUCTS'
        product['productUrl'] = "assets#{rand(6)}.mirraw.com" + product['productUrl']
        product['imageUrl'] = product['imageUrl'].collect do |imgUrl|
          "assets#{rand(6)}.mirraw.com" + imgUrl
        end
        product['price'] = (product['price'].to_i/@rate).round(2)
        product['discount_price'] = (product['discount_price'].to_i/@rate).round(2)
      end
    end
    response
  end

  def current_design(associations = nil)
    @design ||= Design.includes(associations).find_by_id(params[:id]) || Design.includes(associations).find_by_cached_slug(params[:id])
  end

  def update_recently_viewed
    return if params[:track_recent].nil? || params[:track_recent] == 'false'
    if (request.headers['Device-ID'] == API_DEVICE_ID)
      # in case of mirraw mobile
      attrs = {session_id: session.id}
    else
      # in case of api request
      attrs = {device_id: request.headers['Device-ID']}
    end

    if current_design.present?
      attrs.merge!(design_id: current_design.id)
      RecentItem.where(attrs).first_or_create
    end
    # if current_design.present? && (recent_item = current_design.recent_items.where(attrs).first).present?
    #   if current_user.present? && recent_item.user_id != current_user.id
    #     recent_item.update_attributes(user_id: current_user.id)
    #   else
    #     recent_item.touch
    #   end
    # else
    #   attrs.merge!(design_id: current_design.id) unless current_design.blank?
    #   RecentItem.create(attrs.merge({user_id: current_user.try(:id)}))
    # end
  end

  def unbxd_autosuggest_url
    params[:inFields] ||= SystemConstant.get('UNBXD_AUTOSUGGEST_INFIELDS_COUNT').to_i
    params[:popularProducts] ||= SystemConstant.get('UNBXD_AUTOSUGGEST_POPULAR_PRODUCTS_COUNT').to_i
    params[:keywordSuggestions] ||= SystemConstant.get('UNBXD_AUTOSUGGEST_KEYWORD_SUGGESTION_COUNT').to_i
    params[:topQueries] ||= SystemConstant.get('UNBXD_AUTOSUGGEST_TOP_QUERIES_COUNT').to_i

    [
      # UNBXD's autosuggest url with API key and Site key
      "http://search.unbxd.io/#{UnbxdItem::UNBXD_API_KEY}/#{UnbxdItem::UNBXD_SITE_KEY}/autosuggest?",
      # Term to be searched in auto-suggest
      "q=#{params[:q]}&",
      # Infields suggestions Count required in response
      "inFields.count=#{params[:inFields]}&",
      # popularProducts suggestions Count required in response
      "popularProducts.count=#{params[:popularProducts]}&",
      # keywordSuggestions suggestions Count required in response
      "keywordSuggestions.count=#{params[:keywordSuggestions]}&",
      # topQueries suggestions Count required in response
      "topQueries.count=#{params[:topQueries]}"
    ].join()
  end

  def estimate_days
    mirraw_ship_time = Country.country_wise_delivery_time(@actual_country.try(:downcase)).to_f.nonzero? || (@country_code == 'IN' ? DOMESTIC_SHIPPING_TIME : INTERNATIONAL_SHIPPING_TIME)
    @number_of_days = if ESSENTIAL_DESIGNERS['designer_ids'].include?(@design.try(:designer_id).to_s) && @country_code == 'IN'
      ESSENTIAL_DESIGNERS['max_eta']
    elsif @country_code != 'IN' && (@design.ready_to_ship? || @design.sor_available?)
      @addon_ship_time = (mirraw_ship_time + @design.designer_shipping_time(@actual_country.try(:downcase))).round
      mirraw_ship_time.round
    elsif @country_code == 'IN' && @delivery_city.present? && (pickup_city = @design.designer.pickup_location).present? && @design.designer.designer_type == 'Tier 1 Designer'
      @design.warehouse_shipping_time(@actual_country.try(:downcase), @delivery_city)
    elsif @country_code == 'IN' && @delivery_city.present? && (pickup_city = @design.designer.pickup_location).present?
      @design.designer_shipping_time(@actual_country.try(:downcase), @delivery_city)
    elsif @country_code == 'IN' && @actual_country == 'India' && @delivery_city.blank?
      CITY_BASED_SHIP_TIME[:non_metro].to_i.round
    else
      (mirraw_ship_time + @design.designer_shipping_time(@actual_country.try(:downcase))).round
    end
  end

end
