class SurveysController < ApplicationController
  def create
    survey_rating = false
    if params[:sample_form] == 'true' || ((order_number = params[:survey][:order_number]).present? && (email = params[:survey][:email]).present? && (token = params[:survey][:token]).present?) && (Survey.generate_token(email, order_number) == token)
      begin
        if params[:sample_form] == 'true'
          survey_rating = params[:star_value]
        else
          order = Order.where(number: order_number).first
          survey = Survey.find_or_initialize_by(email: email, order_id: order.id)
          if survey.persisted?
            notice = "Your feedback has already been saved."
          else
            survey.rating = params[:star_value]
            order.update_column(:feedback_flag, true)
            survey.app_source = APP_SOURCE[0].downcase
            survey.save
            survey_rating = survey.rating
            notice = "Thankyou for your feedback"
          end
        end
      rescue ActiveRecord::RecordNotFound
        notice = "Order does not exist"
      end
    else
      notice = "Couldn't validate credentials"
    end
    render json: {notice: notice, star_value: survey_rating}
  end

  def new
    if params[:order].present?
      @data = !params[:sample_form] ? JSON.parse(Base64.urlsafe_decode64(params[:order])).symbolize_keys! || {} : {}
      order = Order.find_by_number(@data[:order_number])
      if !params[:sample_form] && Survey.where(order_id: order.try(:id)).present?
        Order.sidekiq_delay.notify_exceptions("Multiple Survey Request", "Survey Already Exist for #{order.try(:number)}", { params: @data }) if @data[:email].present?
        redirect_to :root, notice: "Your feedback has already been saved."
      else
        @survey = Survey.new
      end
    else
      redirect_to :root, notice: "Order does not exist"
    end
  end

  def update_notes
    order_id = Order.where(number: params[:survey][:order_number]).pluck(:id).first
    survey = Survey.where(order_id: order_id).first if order_id.present?
    if survey.present?
      notes = params[:survey][:notes]
      survey.update_column(:notes, notes)
      notice = "Thank You for your feedback"
    else
      notice = "Survey does not exist."
    end
    render json: {notice: notice}
  end

   def get_line_item_image
    begin
      @image_array = []
      @design_ids = []
      @line_item_type = []
      @line_item_ids = {}
      if params[:sample_form] == 'true'
        FEEDBACK_SAMPLE_FORM_LINE_ITEMS['line_items'].each do |line_item|
          @image_array << line_item['master_image']
          @design_ids << line_item['design_id']
          @line_item_type << line_item['type']
        end
      else
        order_number = params[:order_number]
        order = Order.where(number: order_number).first
        order.line_items.sane_line_items.each do |line_item|
          master_image = line_item.design.master_image.photo('long')
          @image_array << master_image
          @design_ids << line_item.design_id
          @line_item_ids[line_item.design_id] = line_item.id
        end
      end
      @order_id = order.id
    rescue ActiveRecord::RecordNotFound
      notice = "Survey does not exist"
    end
    render file: '/surveys/nps_design_ratings', content_type: 'text/javascript'
  end

  def new_review
    begin
      begin
        Survey.new_review(params[:order_id], params[:star_value_design], params[:design_id], 'product')
      rescue ActiveRecord::RecordNotUnique
        render json: {notice: 'Review already exists!', status: 500}
      end
      notice = "Thank you for your feedback"
    rescue ActiveRecord::RecordNotFound
      notice = "Survey does not exist"
    end
    render json: {notice: notice}
  end

  def save_review_text
    if params[:sample_form] == "false"
      Review.sidekiq_delay.new_review(params[:product_id], review_text: params[:comment])
    end
    render json: {notice: "Thank you for your feedback."}
  end

  def survey_answers
    if params[:sample_form] == 'true'
      render json: {status: 200}
    elsif params[:answers].present?
      SurveyAnswer.fill_answers(params[:answers], params[:type], params[:association_id], params[:issue_type])
      render json: {status: 200}
    else
      render json: {status: 422}
    end
  end

  def get_audience_specific_questions
    if params[:sample_form] == 'true' || (@order_id = Order.find_by_number(params[:order_number]).try(:id)).present?
      @survey_questions = SurveyQuestion.get_audience_questions(params[:audience])
      render file: '/surveys/survey_questions.js', content_type: 'text/javascript'
    else
      redirect_to :root, notice: 'Order does not exist'
    end
  end
end
