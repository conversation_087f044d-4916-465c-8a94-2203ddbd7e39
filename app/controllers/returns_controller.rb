class ReturnsController < ApplicationController
  before_action :authenticate_account!

  require 'one_time_password_service'

  def create
    @user = current_user
    @order = Order.where(id: params[:return][:order_id]).first
    @return = Return.duplicate?(params[:return_items])
    if @return.present?
      respond_to do |format|
        url , notice = @return.service_type == "SELF RETURN" ? [user_order_return_items_path(order_number: @order.number,return_id: @return.id, id: @user.id)  , 'Pickup service is available for this order.' ] : [ return_orders_path(@user) , 'Return successfully created. Please check email for further information.']
        format.html { redirect_to url, notice: notice }
      end
    elsif @user.present?
      @return = Return.new
      @return.build_by(return_params)
      @return.app_source = @app_source
      @return.user_id  = @user.try(:id)
      if params[:verified_phone_number].present? && params[:otp].present?
        if OneTimePassword.verify('91'+params[:verified_phone_number], params[:otp])
          @return.refund_phone_number = params[:verified_phone_number]
        else
          @return.errors.add(:refund_phone_number, 'OTP not verified')
        end
      end

      product_ids,designer_names = '',''

      return_items = params[:return_items]
      return_item_detail = Hash[params[:return_items].map{ |item| [item.to_i,{return_quantity: params["quantity_#{item}"].to_i, return_reason: "#{params["reason_#{item}"]}#{params["reason_details_#{item}"].present? ? "-#{params["reason_details_#{item}"]}"  : ""}", return_image: params["uploadimage_#{item}_1"], return_image2: params["uploadimage_#{item}_2"]}]}]    
      line_items_designer = LineItem.includes(:designer_order).where(:id => params[:return_items]).group_by {|item| item.designer_order.designer_id}
      @return.reason = return_item_detail.values[0][:return_reason]
      designers = Hash[Designer.select('id,name').where(id: line_items_designer.keys).map { |d| [d.id, d.name] }]
      line_items_designer.each_with_index do |(designer_id, items), index|
        if index !=0
          designer_names << '/'
          product_ids << '/'
        end
        items.each_with_index do |item, index|
          item.skip_quantity_validation = true
          product_ids << '/' if index !=0
          product_ids << item.design.id.to_s
          item.update_attributes(return_item_detail[item.id])
        end
        designer_names << designers[designer_id]
        @return.return_designer_orders.build(:designer_id => designer_id, line_items: items,designer_order_id: items.first.designer_order_id)
      end
      @return.product_id = product_ids
      @return.designer_name = designer_names

      respond_to do |format|
        if @return.errors.blank? && @return.save!
          pickup_service_available = Return::RETURN_REASON_ARRAY.exclude?(@return.reason)
          if pickup_service_available
            @return.pickup_available_on_order?
            notice = 'Pickup service is available for this order.'
            redirect = user_order_return_items_path(order_number: @return.order.number,return_id: @return.id, id: @user.id)
          else
            notice = 'Return successfully created. Please check email for further information.'
            redirect = return_orders_path(@user)
          end
          format.html { redirect_to redirect, notice: notice }
          format.json { render json: @user, status: :created, location: @user}
        else
          notice = 'Something went wrong'
          notice = 'Incorrect OTP' if @return.errors.messages[:refund_phone_number].present?
          format.html {
            flash[:error] = notice
            redirect_to :back
          }
          format.json { head :no_content }
        end
      end
    else
      redirect_to new_account_session_path, notice: "You need to be logged in."
    end
  end

  def update
    @user = current_user
    @return = Return.find_by_id(params[:id])
    rtn_params = return_params
    if @return.present? && !@return.payment_complete? && @user.present?
      rtn_params.merge!(pay_type: 'PayPal') if rtn_params[:user_paypal_email].present?
      @return.update_attributes(rtn_params)
      if @return.reverse_pickup? && @return.refund_details_pending? && @return.type_of_refund == 'Refund' && [Order::BANK_DEPOSIT, Order::COD].include?(@return.pay_type) && @return.account_holder_name.present? && @return.account_number.present? && @return.ifsc_code.present?
        Return.sidekiq_delay.mobile_all_items_received(@return.id)
      end
      respond_to do |format|
        format.html { redirect_to url_for(controller: :users, action: :return_items, id: @user.id, order_number: @return.order_number, return_id: @return.id), notice: 'Your account details are successfully captured.' }
        format.json { head :no_content }
      end
    else
      respond_to do |format|
        format.html { redirect_to :back, notice: 'Return cannot be updated anymore.' }
        format.json { head :no_content }
      end
    end
  end

  def get_account_no_length
    render json: {length: Bank.find_by_bank_name(params[:bank_name]).account_no_length}
  end

  def generate_otp
    response = OneTimePasswordService.deliver('91' + params[:phone], ":OTP: is your one time password[OTP] to get your refund for order: #{params[:order_number]}. It is valid for 10 minutes. Do not share your OTP with anyone.")
    sms_sent = response.include?('MsgID') ? true : false
    render json: {sms_sent: sms_sent}
  end

  def check_tracking_number
    rdo = ReturnDesignerOrder.where(id: params[:rdo_id]).first
    if rdo.present? && (rdo_dos = rdo.designer_order).present?
      if rdo_dos.order.try(:number) == params[:tracking_number] || rdo_dos.tracking_num.to_s == params[:tracking_number] || rdo_dos.tracking_num.to_s.first(4) == (params[:tracking_number].first(4)) || rdo_dos.tracking_num.to_s.last(4) == (params[:tracking_number].last(4))
        response_json = {new_track: 'incorrect'}
      else
        response_json = {new_track: 'correct'}
      end
    else
      response_json = {error: 'return designer order not found'}
    end
    render :json => response_json
  end

  def update_tracking_info
    rdo = ReturnDesignerOrder.find_by_id(params[:return_designer_order_id])
    @user = current_user
    notice = "Tracking Details Updated Successfully. Your return request need to be approved by our support team."

    if params[:tracking_company].present? && params[:tracking_number].present?
      if rdo.buyer_dispatched?
        ReturnDesignerOrder.where(id: rdo.id).update_all(tracking_company: params[:tracking_company],tracking_number: params[:tracking_number],updated_at:Time.zone.now)
        return1 = rdo.return
        return1.update_column(:state, 'refund_details_pending')
        return1.update_return_tracking_info() if return1.check_if_all_rdo_have_tracking
      else
        rdo.tracking_company = params[:tracking_company]
        rdo.tracking_number  = params[:tracking_number]
        rdo.return_type      = params[:return_type]
        rdo.product_sent!
      end
    else
      notice = 'Please enter tracking details'
    end

    if @user.present?
      redirect_to :back, notice: notice
    else
      redirect_to :back, :notice => 'Please Log in first'
    end
  end

  private

  def return_params
    params.require(:return).permit(:account_holder_name, :coupon_id, :account_number, :courier_name, :customer_phone_number, :adjustment, :bank_name, :order_number, :order_id, :branch, :ifsc_code, :notes, :pay_type, :status, :total, :state, :coupon_code, :reason, :tracking_id, :origin_city, :designer_name, :product_id, :discount, :completed_on, :type_of_refund, :product_charge, :stitching_charge, :instruction_to_accounts, :coupon_gen_date, :shipping, :agent_info, :user_paypal_email)
  end
end