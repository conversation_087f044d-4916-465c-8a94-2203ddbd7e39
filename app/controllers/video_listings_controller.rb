class VideoListingsController < ApplicationController
  require 'uri'
  require 'net/http'
  
  def index
    headers = {
      "X-Api-Key" => VIDEO_LISTING_DATA['X-Api-Key'],
      "content-type" => 'application/json'
    }
    youtube_regex = /(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/
    @response = HTTParty.get(VIDEO_LISTING_DATA['video_homepage_url'],headers: headers)
    @response.each do |res|
      if res['url'].to_s.length > 0
        res['video_id'] = youtube_regex.match(res['url'])[1]
        res['thumbnail'] = "https://img.youtube.com/vi/#{res['video_id']}/sddefault.jpg"
      end
    end
  end

  def show
    video_id = VideoListing.find(params['id']).url
    youtube_regex = /(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/
    @video_id = youtube_regex.match(video_id)[1]
    headers = {
      "X-Api-Key" => VIDEO_LISTING_DATA['X-Api-Key'],
      "content-type" => 'application/json'
    }
    body = {
      "video_id": params['id']
    }.to_json
    response = HTTParty.get("https://www.googleapis.com/youtube/v3/videos?id=#{@video_id}&key=#{VIDEO_LISTING_DATA['youtube_key']}&part=snippet")
    @video_hash = {'title': response['items'][0]['snippet']['title'], 'description': response['items'][0]['snippet']['description']}
    @products_hash = HTTParty.post(VIDEO_LISTING_DATA['video_homepage_url'] + "render_video/", headers: headers, body: body)
  end
end