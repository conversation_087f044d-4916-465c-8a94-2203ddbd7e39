class SitemapsController < ApplicationController
  def index
    @sitemap_headers = SitemapItem.roots.except(:order).order('position')
    @breadcrumb = generate_breadcrumbs
    @seo = SeoList.find_by_label 'sitemap_home'
  end

  def show_category
    @sitemap_category = SitemapItem.friendly.find(params[:title])
    @breadcrumb = generate_breadcrumbs(@sitemap_category)
    @seo = SeoList.find_by_label "sitemap_#{@sitemap_category.friendly_id}"
  end

  private
  def generate_breadcrumbs(category = nil)
    breadcrumbs = [{title: 'Home', url: root_url}]
    if category.present?
      if (ances = category.ancestors.reject(&:deleted_at)).empty?
        breadcrumbs << {title: 'Sitemap', url: sitemaps_index_path}
      else
        breadcrumbs << {title: ances.shift.title, url: sitemaps_index_path}
        ances.each { |anc| breadcrumbs << {title: anc.title, url: anc.friendly_id} }
        breadcrumbs << {title: category.title, url: nil}
      end
    else
      breadcrumbs << {title: 'Sitemap', url: sitemaps_index_path}
    end
  end

  rescue_from ActiveRecord::RecordNotFound do
    redirect_to '/404'
  end
end
