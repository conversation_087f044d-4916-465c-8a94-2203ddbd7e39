class GiftCardOrdersController < ApplicationController
  before_action :redirect_under_certain_conditions

  def new
    @gift_cards = GiftCard.visible.all

    @gift_card_order = GiftCardOrder.new({
      gift_card: @gift_cards.first
    })
  end

  def create
    @gift_card_order = GiftCardOrder.new(gift_card_order_params)
    @gift_card_order.pay_type = 'paypal'
    @gift_card_order.currency_code = @symbol
    @gift_card_order.currency_rate = @gift_card_order.paid_currency_rate = CurrencyConvert.currency_convert_cache_by_country_code(Design.country_code).market_rate.round(2)

    @gift_card_order.sender_user = User.find_by(email: @gift_card_order.email)
    @gift_card_order.recipient_user = User.find_by(email: @gift_card_order.recipient_email)
    
    if @gift_card_order.save
      redirect_to paypal_url
    else
      redirect_to new_gift_card_order_path
    end
  end

  def show
    @gift_card_order = GiftCardOrder.find_by_number(params[:number])

    unless @gift_card_order
      flash[:alert] = 'No such gift card ordered.'
      redirect_to root_url
    end
  end

  def paypal_response
    @gift_card_order = GiftCardOrder.find_by_number(params[:invoice])

    @gift_card_order.transaction_id = params[:txn_id]
    @gift_card_order.other_details['payment_details'] = params

    case params['payment_status']
    when 'Completed'
      @gift_card_order.pay!
    when 'Pending'
      @gift_card_order.process!
    when 'Failed'
      @gift_card_order.cancel!
    end

    render nothing: true
  end

  private

  def paypal_url
    if Rails.env.development? || Rails.env.staging?
      url = 'https://www.sandbox.paypal.com/cgi-bin/webscr'
      email = "<EMAIL>"
    elsif Rails.env.production?
      url = 'https://www.paypal.com/cgi-bin/webscr'
      email = "<EMAIL>"
    end

    paid_amount = if paypal_accepts_currency = Order::PAYPAL_ALLOWED_CURRENCIES.include?(@gift_card_order.currency_code)
      @gift_card_order.paid_amount
    else
      @gift_card_order.paid_currency_rate =  CurrencyConvert.currency_convert_memcached.find{|cc| cc.symbol == 'USD'}.market_rate.round(2)
      @gift_card_order.save
      (@gift_card_order.paid_amount * @gift_card_order.currency_rate) / @gift_card_order.paid_currency_rate
    end

    values = {
      business: email,
      cmd: '_xclick',
      upload: 1,
      return: gift_card_order_url(@gift_card_order),
      invoice: @gift_card_order.number,
      notify_url: gift_card_orders_paypal_response_url,
      rm: 1,
      amount: paid_amount,
      currency_code: paypal_accepts_currency ? @gift_card_order.currency_code : 'USD',
      item_name: 'Gift Card',
      item_number: @gift_card_order.id,
      quantity: 1
    }

    "#{url}?#{values.to_query}"
  end

  def redirect_under_certain_conditions
    return unless ['india', 'n/a'].include?(@actual_country.try(:downcase)) || ['inr', 'rs'].include?(@symbol.downcase)
    redirect_to :root
  end

  def gift_card_order_params
    params.require(:gift_card_order).permit(
      :name,
      :email,
      :recipient_name, :recipient_email, :recipient_email_confirmation,
      :paid_amount,
      :message,
      :gift_card_id
    )
  end
end
