class KycDocumentsController < ApplicationController
  before_action :validate_order_exists
  def new
    @kyc_document = KycDocument.new
    @document_type = 'National ID'
  end

  def create
    @kyc_document = @user.kyc_documents.build(kyc_document_params)
    if @kyc_document.save
      flash[:notice] = "#{@kyc_document.name} uploaded successfully"
      Order.send_document_to_shipper(@order.id,params[:kyc_document][:name])
      redirect_url = params[:redirect_back_url].present? ? "#{params[:redirect_back_url]}?notice=#{CGI.escape(flash[:notice])}" : order_url(@order)
      redirect_to (redirect_url)
    else
      flash[:error] = @kyc_document.errors.full_messages.first
      redirect_to new_kyc_document_path(order_number: params[:order_number])
    end
  end

  private

  def kyc_document_params
    params.require(:kyc_document).permit(:name, :document)
  end

  def decrypt_data(encrypted_data)
    raw = Base64.urlsafe_decode64(encrypted_data)
    iv = raw[0, 16]
    encrypted = raw[16..-1]
    cipher = OpenSSL::Cipher::AES.new(128, :CBC)
    cipher.decrypt
    cipher.key = Rails.application.config.url_secret_key
    cipher.iv = iv
    decrypted_data = cipher.update(encrypted) + cipher.final
    decrypted_data.encode('UTF-8', invalid: :replace, undef: :replace, replace: '')
  end

  def validate_order_exists
    begin
      order_number = decrypt_data(params[:order_number])
      order_number = order_number.encode('UTF-8', invalid: :replace, undef: :replace, replace: '')
      @order = Order.find_by(number: order_number)
      if @order.present?
        @user = @order.user
        return if @user.present?
      end
      return redirect_to (request.referer.presence || '/404')
    rescue
      return redirect_to (request.referer.presence || '/404')
    end
  end   
end
