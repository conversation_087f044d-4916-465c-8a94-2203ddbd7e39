class HoroscopesController < ApplicationController
  def index
    @horoscopes = Horoscope.order(:start_date)
    @horoscopes_grade = Array.new
    horoscopes_temp = Array.new
    @horoscopes.each do |horoscope|
      if( horoscope.start_date.month >= 3)
        @horoscopes_grade << horoscope
      else
        horoscopes_temp << horoscope
      end
    end
    @horoscopes_grade.concat(horoscopes_temp)
    @seo = SeoList.where(label: 'horoscope_page').first
  end

  def show
   @horoscope = Horoscope.find(params[:id])
   if @horoscope.present?
      @seo = SeoList.where(label: @horoscope.name).first
    else
      redirect_to horoscopes_path, notice: 'Zodiac sign not found'
    end
  end
end

 
