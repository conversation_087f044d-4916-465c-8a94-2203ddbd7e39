class PushEngageSubscribersController < ApplicationController
  require "pushengage_helper.rb"

  def create
    uniq_hash = params[:uniq_hash]
    if PushengageHelper.is_subscribed?(uniq_hash)
      pe_subsriber = PushEngageSubscriber.where(uniq_hash: uniq_hash).first_or_initialize
      current_cart = current_web_cart_without_create
      pe_subsriber.cart = current_cart if current_cart.present?
      pe_subsriber.account = current_account if account_signed_in?
      self.current_pe_subscriber = pe_subsriber if pe_subsriber.save
    end
    send_data Base64.decode64("R0lGODlhAQABAPAAAAAAAAAAACH5BAEAAAAALAAAAAABAAEAAAICRAEAOw=="), type: "image/gif", disposition: "inline"
  end

  private

  def push_enage_subscriber_params
    params.require(:push_engage_subscriber).permit(:uniq_hash)
  end

end
