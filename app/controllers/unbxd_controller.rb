class UnbxdController < ApplicationController

  def unbxd_recommendations
    unbxd_service_obj = Unbxd::RecommendService.new(params)
    unbxd_custom_recs = unbxd_service_obj.call()
    if unbxd_custom_recs.present? && unbxd_custom_recs['data'].present?
      discounts = {}
      design_ids = unbxd_custom_recs['data'].collect{|design| design['id']}
      designs = Design.where(id: design_ids).each{|design| 
                                                  discounts[design.id] = {
                                                    "discount_price": design.effective_price_currency(@rate),
                                                    "price": design.price_currency(@rate),
                                                    "discount_percent": design.effective_discount(PromotionPipeLine.active_promotions)
                                                  }
                                                }
      
      unbxd_custom_recs['data'].each do |design|
        design['discount_price'] = discounts[design['id'].to_i][:discount_price]
        design['price'] = discounts[design['id'].to_i][:price]
        design['discount_percent'] = discounts[design['id'].to_i][:discount_percent]
        design['symbol'] = @symbol
        design['hex_symbol'] = @hex_symbol
      end
      
      
      partial_string = 'unbxd_recommendations/newly_added_products'
      
      
      if params['modal'].present?
        partial_string = 'unbxd_recommendations/recommendations_modal'
      end
      render json: { html: render_to_string(partial: partial_string, locals: { unbxd_custom_recs: unbxd_custom_recs }) }
    else 
      render json: { html: '' }
    end
  end

  # def unbxd_search
  #   # Shift UnbxdSearch module over here 
  # end

  # def unbxd_recommendations

  # end


  # def get_search_result
  #   # Shift UnbxdSearch module over here
  # end


end

