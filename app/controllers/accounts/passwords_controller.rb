class Accounts::PasswordsController < Devise::PasswordsController

  # PUT /resource/password
  def update
    self.resource = resource_class.reset_password_by_token(resource_params)
    yield resource if block_given?

    if resource.errors.empty?
      resource.unlock_access! if unlockable?(resource)
      if Devise.sign_in_after_reset_password
        flash_message = resource.active_for_authentication? ? :updated : :updated_not_active
        set_flash_message(:notice, flash_message) if is_flashing_format?

        # Not allowing user to sign_in.
        # sign_in(resource_name, resource)
      else
        set_flash_message(:notice, :updated_not_active) if is_flashing_format?
      end
      respond_with resource, location: after_resetting_password_path_for(resource)
    else
      flash.now[:alert] = resource.errors.full_messages.join(', ')
      respond_with resource
    end
  end

  def create
    if check_string(resource_params[:login]) and @account = resource_class.find_by_phone(resource_params[:login]) and  !@account.blank? and @account.dial_code == "+91"
      @account.send_pwd_reset_link_via_sms
      redirect_to new_account_session_path
    else
      resource_params_custom = resource_params[:login]
      resource_params = {}
      resource_params[:email] = resource_params_custom
      self.resource = resource_class.send_reset_password_instructions(resource_params)
      yield resource if block_given?

      if successfully_sent?(resource)
        respond_with({}, location: after_sending_reset_password_instructions_path_for(resource_name))
      else
        respond_with(resource)
      end
    end
  end


  private

  def check_string string
    string.scan(/\D/).empty?
  end
end