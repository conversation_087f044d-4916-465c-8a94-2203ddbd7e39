class Accounts::OmniauthCallbacksController < Devise::OmniauthCallbacksController

  include MirrawUtil

  before_filter :user_currency

  %w(facebook google_oauth2).each do |provider|
    self.class_eval %Q{
      def #{provider}
        # You need to implement the method below in your model (e.g. app/models/account.rb)
        @account = Account.from_omniauth(request.env["omniauth.auth"])

        if @account.persisted?
          if @account.user?
            sign_in_and_redirect @account, event: :authentication
            # Keeping track of omniauth authenticated user for blocking changes password functionality
            session['devise.omnitauth_user'] = true
            flash[:ga_event] = { method: "#{provider}", action: 'login', user_id: @account.id.to_s}
            reset_current_web_cart(@account)
            @account.add_to_subscribers("mobile/#{provider}/login", @country_code, 'mobile', request.headers['True-Client-IP'] || request.ip)
            set_flash_message(:notice, :success, kind: "#{provider.split('_').first.humanize}") if is_navigational_format?
          else
            redirect_to new_account_registration_url, alert: 'Email does not belong to a user'
          end
        else
          session["devise.#{provider}_data"] = request.env["omniauth.auth"]
          redirect_to request.env['omniauth.origin'] || new_account_registration_url
        end
      end
    }
  end

end