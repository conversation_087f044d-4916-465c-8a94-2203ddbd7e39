class Accounts::ConfirmationsController < Devise::ConfirmationsController
  private
  def after_confirmation_path_for(resource_name, resource)
    if signed_in?(resource_name)
      signed_in_root_path(resource)
    elsif resource.guest_account
      sign_in(resource)
      flash[:notice] = "You Need to Set a new password"
      edit_account_registration_url
    else
      new_session_path(resource_name)
    end
  end
end