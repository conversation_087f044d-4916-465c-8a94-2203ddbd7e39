class UsersController < ApplicationController
  before_filter :authenticate_account!, only: [:cancel_cod_order,:return_items, :return_orders, :user_wallet]

  def show
    begin  
      @user = User.friendly.find (params[:name])
    rescue ActiveRecord::RecordNotFound
      redirect_to root_url, :notice => 'No such user exists.'
      return
    end
    @designs = Design.joins(:follows).includes(:categories, :master_image, :designer).where('follows.follower_id = ?', @user.id).page params[:page]
  end

  def return_items
    @return = Return.new
    @banks = Bank.pluck(:bank_name)
    if current_user.present?
      if params[:return_id].present?
        @return = Return.where(id: params[:return_id]).preload(return_designer_orders: [:reverse_shipment, :designer,line_items: [:designer_order,:discount_line_items,design: :master_image]]).first
        @order  = @return.order if @return.present?
      else
        @order = Order.where(number: params[:order_number], user_id: current_user.id).preload(line_items:[:designer_order,design: [:master_image,:designer]]).first
      end
      if @order.blank? || @order.user_id != current_account.user.id
        redirect_to return_orders_path, notice: "Your order number #{params[:order_number]} was not found."
      end
    else
      redirect_to new_account_session_path, notice: 'You need to be logged in first.'
    end
  end

  def cancel_cod_order
    message = 'Sorry Order Cannot be cancelled. <NAME_EMAIL>'
    if current_account.present? && (current_user = current_account.user).present?
      order = Order.where(user_id: current_user.id,number: params[:order_number]).first
      if order.present? && order.cod? && order.check_not_picked_up
        order.order_notification['order_cancelled'] = 1
        order.save
        Order.sidekiq_delay.cancel_order_mobile(order.id)
        message = 'Order Cancelled Successfully!'
      end
    end
    redirect_to :back,notice: message
  end

  def return_orders
    if current_user.present?
      order_ids = Order.unscoped.
                  where(state: ['sane','dispatched'], user_id: current_user.id).
                  uniq.pluck(:id)
      @active_returns    = Return.where(order_id: order_ids).where('created_at >= ?',(params[:old] ? 1.year.ago.beginning_of_day : 3.months.ago.beginning_of_day)).preload(:order,line_items: [return_designer_order: :designer,design: :master_image]).order('created_at desc').page(params[:page]).per(10)
    else
      redirect_to new_account_session_path, notice: 'You need to be logged in first.'
    end
  end

  def upload_full_size_photo
    response = 'Could Not Upload Photo.'
    if request.post? && params[:photo].present?
      extension = params[:photo].original_filename.split('.').last
      if (valid_ext = ['jpg','jpeg','png'].include?(extension)) && params[:photo].size <= 4000000 && (user = current_account.try(:user)).present?
        user.update_attributes(full_size_photo:params[:photo])
        response = 'Photo Added Successfully.'
      else
        response = valid_ext ? ( user.blank? ? 'You are not Signed In.' : 'Please Upload Image less than 4MB.') : 'Please Upload .jpg, .jpeg, .png Format Only.'
      end
    end
    redirect_to :back, notice: response
  end

  def cancel_order
    if (order = Order.where(user_id: current_user.id,number: params[:order_number]).first).present?
      Order.sidekiq_delay.cancel_order_mobile(order.id)
      order.save_order_cancel_reason(params[:order_cancel_reason], current_account)
      flash[:success] = "Your order #{params[:order_number]}, if not showing as cancelled right now, will be cancelled soon."
      render js: "window.location = '#{order_path(order)}'"
    end
  end

  def user_wallet
    @wallet = current_user.wallet.presence || current_user.assign_wallet(@country_code)
  end
end