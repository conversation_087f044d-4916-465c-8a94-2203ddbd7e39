class StoreController < ApplicationController
  # respond_to :html, :json, :js
  newrelic_ignore :only => [:catalog_page_amp] if Rails.env.production? || Rails.env.staging?
  # get the params and send it to as optional params
  #
  # == Parameters:
  # opt::params[:category_parent_id], params[:items_per_page]
  #
  #
  # == Returns:
  # JSON
  #
  SORT = {
        'top_rated' => '1',
        'l2h' => '2',
        'h2l' => '3',
        'new' => '4',
        'discount' => '5',
        'popularity' => '6',
        'bstslr' => '6',
        'default' => '7',
        'trending' => '8',
        'trending-designs'=> '9',
        'popular' => '10', #for experiment
        'recommended' => '11', #for experiment
        'recent-30' =>  '14'
      }.freeze
  before_filter :set_cache_buster
  before_action :validate_search_query, only: [:catalog_page]

  def catalog_page
    get_catalog_data(false)
  end
  
  def validate_search_query
    # Pattern to check for Log4j-style injections
    log4j_pattern = /\$\{.*?\}/
    # Pattern to check for any invalid characters (anything other than alphanumeric, hyphen, or space)
    
    if params[:q].present? && (params[:q] =~ log4j_pattern)
      Order.sidekiq_delay.notify_exceptions("Suspicious search query detected Mobile/API!", "Some One is Trying To Inject Malicious Query", params[:q])
      render 'pages/error_404', :status => :not_found
    end
  end

  def catalog_page_amp
    get_catalog_data(true)
    return if performed?
    if params[:dynamic_amp_data]
      render :json => @store_page.present? ? @store_page['designs'] : []
    else
      render 'catalog_page_amp', layout: 'application_amp'
    end
    # respond_to do |format|
    #   format.json {render :json => @store_page['designs']}
    #   format.any {render 'catalog_page_amp', layout: 'application_amp'}
    # end
  end

  def landing_page
    @landing = Landing.mirraw.where(label: params[:landing],category_landing: false).first
    @boards = nil
    if @landing.present?
      if @landing.boards.present?
        @boards = @landing.boards.graded.country(@country_code).app_source(@app_source.downcase)
      end
      @blocks = @landing.blocks.mirraw.graded.country(@country_code).app_source(@app_source.downcase)
    else
      redirect_to root_url
    end
  end

  def dynamic_landing_page
    @dynamic_landing_page = DynamicLandingPage.country(Design.country_code).app_source(@app_source.downcase).find(params[:id])
    @designs = Design.includes(:designer,:images,:dynamic_price_for_current_country).in_stock.where(id: @dynamic_landing_page.design_ids.split(',')) if (@dynamic_landing_page.try(:design_ids).present?)
    @seo = SeoList.where(label: @dynamic_landing_page.seo_list_label).first if @dynamic_landing_page.try(:seo_list_label).present?
    gtm_data_layer.push({pageType: 'landing', categoryName: @dynamic_landing_page.name})
    @ga_list = "landing / #{@dynamic_landing_page.name}"
    rescue
      redirect_to root_path, notice: 'We couldn\'t find the page you were looking for.'
  end

  def flash_deals
    @ga_list = "FlashDealsPage"
    @flash_page = get_response_data_for('flash_deals', params)['flash_deals']
  end

  private

  def sorting(sort, sort_via = nil)
    if sort_via == 'unbxd_sort'
      ApplicationHelper::UNBXD_SORT_TYPE[params[:sort]]
    else
      params[:sort] = SORT[sort.try(:downcase)]
    end
  end

  def get_catalog_data(amp_page=false)
    designer = nil
    if params[:kind].present?
      if params[:kind] == 'b1g1'
        params[:buy_get_free] = 1
      elsif params[:kind] == 'direct_dollar'
        params[:buy_get_free] = 4
      else
        params[:kind] = "kids-#{params[:gender]}" if params[:gender].present?
        @category = get_kind_category_id(params[:kind])
        @popular_links = @category.popular_links if @category.present?
        params[:category_parent_id] = @category.try(:id)
      end
      if CATEGORY_MOBILE_GRADE.include? params[:kind]
        params[:category_grade] = params[:kind]
      end
      # @url_path = "#{params[:kind]}?"
      @ga_list = "category / #{params[:kind]}"
      gtm_data_layer.push({pageType: 'category', categoryName: params[:kind].downcase})
    elsif params[:q].present? && params[:utf8].blank?
      if ['jhumkas','toe rings','skirts','hijab'].include?(params[:q].downcase)
        redirect_kind = params[:q].downcase.gsub(' ','-')
      elsif ['crop top and lehenga'].include?(params[:q].downcase)
        redirect_kind = 'crop-top-lehengas'
      elsif ['ethnic wear'].include?(params[:q].downcase)
        redirect_kind = 'women-ethnic-wear'
      elsif ['bridal', 'temple', 'statement', 'thewa', 'handmade', 'filigree', 'pearl', 'high end', 'ethnic', 'kundan', 'indian traditional', 'antique', 'american diamond', 'maharashtrian traditional'].map { |s| "#{s} jewellery" }.include?(params[:q].downcase)
        redirect_kind = params[:q].parameterize
      end
      if redirect_kind.present?
        redirect_to (amp_page ? store_amp_search_path(kind: redirect_kind) : store_search_path(kind: redirect_kind)) and return
      end
      params[:tag] = params[:q]
      # @url_path = "tags?q=#{params[:tag]}"
      @seo = SeoList.where(:label => params[:tag]).first
      gtm_data_layer.push({pageType: 'search', searchTerm: params[:q].downcase})
      @ga_list = "tags / #{params[:q]}"
    elsif params[:q].present?
      params[:term] = params[:q]
      @search = params[:q]
      gtm_data_layer.push({pageType: 'search', searchTerm: params[:q].downcase})
      # @url_path = "search?q=#{params[:term]}"
      @ga_list = "searchresults"
    elsif params[:collection]
      @collection = params[:collection]
      # @url_path = "collections/#{params[:collection]}"
      @seo = SeoList.where(:label => "#{params[:collection]}_collection").first
      @collection_heading = @seo&.heading.presence || params[:collection]
      gtm_data_layer.push({pageType: 'collection', collectionName: params[:collection].downcase})
      @ga_list = "collection / #{params[:collection]}"
    elsif params[:catalogue]
      @catalogue = params[:catalogue]
      @seo = SeoList.where(:label => "#{params[:catalogue]}_catalogue").first
      @catalogue_heading = @seo&.heading.presence || params[:catalogue]
      gtm_data_layer.push({pageType: 'catalogue', catalogueName: params[:catalogue].downcase})
      @ga_list = "catalogue / #{params[:catalogue]}"
    elsif params[:id]
      if designer = Designer.find_by_cached_slug(params[:id])
        @design_categories = designer.limited_design_categories.collect(&:name).join(', ')
        @canonical_path = designers_path(designer.cached_slug.presence || designer.id)
        @ga_designer_id = params[:designer_id] = designer.id
        gtm_data_layer.push({pageType: 'designer', designerId: designer.cached_slug})
        @ga_list = "designer / #{designer.cached_slug}"
      else
        render 'pages/error_404', :status => :not_found
      end
      # @url_path = "designers/#{params[:id]}"
      @seo = SeoList.where(:label => params[:id]).first
      @designer_search = true
    end
    params[:category_child_ids] = params.delete(:category_ids) if params[:category_ids].present?
    show_details = FACETED_URL_KINDS[params[:kind]].present?
    imp_pvids = PropertyValue.get_property_values_for_facets(params[:kind], params[:facets], details: show_details)

    @faqs =
      if params[:facets].present?
        FAQ.joins(:property_values).where({
          'property_values.id': imp_pvids
        })
      else
        FAQ.joins(:categories).where({
          'categories.id': params[:category_parent_id]
        })
      end.order(updated_at: :desc).limit(5).map do |faq|
        faq.attributes.slice('id', 'question', 'answer')
      end

    if params[:preference].present?
      params[:preference] = SolrPreferenceQuery.new(params[:preference], kind: params[:kind].try(:downcase))
    end
    raise ActionController::RoutingError.new('Catalog Page Not Found') if imp_pvids.empty? && params[:facets].present?
    if show_details
      @facet_properties = imp_pvids
      imp_pvids = imp_pvids.collect { |prop| prop[:id] }
    end
    pv_id = imp_pvids.empty? ? '' : imp_pvids.join(',')
    params[:colour_property_value_ids] = pv_id if params[:facets] && params[:facets].index('colour-')
    if pv_id.present?
      params[:property_value_ids] = [params[:property_value_ids], pv_id].compact.join(',')
    end
    if params[:sort].present? and @country_code == 'IN' and params.has_key?(:q)
      # @url_path =
      #   if params[:kind].present? || params[:collection].present?
      #     "#{@url_path}?sort=#{params[:sort]}"
      #   else
      #     "#{@url_path}&sort=#{params[:sort]}"
      #   end
      params[:sort_values] = sorting(params[:sort], "unbxd_sort")
    elsif params[:sort].present?
      params[:sort] = sorting(params[:sort])
    elsif params[:kind].present?
      prepare_users_for_test(params[:kind].downcase)
      if ALLOW_AB_TEST_GRADING && session[:exp_sorted].present? && (new_sort_by = sort_by_ab_test(params[:kind])).present?
        params[:sort] = sorting(new_sort_by)
      end
    end
    redirect_to api_v1_search_path(params) and return if params['api']
    if amp_page
      # Show same number of designs as desktop if page requested is AMP
      params[:items_per_page] = 48
      @amp_url = true
      container_title = 'AMP Catalog Page > Bottom Horizontal Mobile > '
      container_title += @designer_search ? 'Recommended' : 'Category Top Seller'
      unbxd_container = UnbxdContainer.preload(:unbxd_widget).find_by_title container_title
      if unbxd_container && unbxd_container.active?
        unbxd_widget = unbxd_container.unbxd_widget
        widget_hash = {
          uid: cookies['unbxd.userId'],
          product_count: unbxd_container.product_count
        }
        widget_hash[:category] = @category.name if @category.present?
        response = unbxd_widget.get_response_for_widget(widget_hash)
        if response == nil
          head :no_content
        else
          @unbxd_amp_widget = response['status'] == 200 ? response : nil
        end
      end
      # Limit kept on featured products for AMP pages is 16 (Same as desktop)
      featured_products(16)
    end
    if params[:more_designs].present? && request.xhr?
      begin
        params[:facet] = JSON.parse(params[:facet]) if params[:facet].present?
      rescue JSON::ParserError => e
        params[:facet] = {}
      end
      @store_page = if @country_code == "IN" && params.has_key?(:q)
        request_type = request.xhr? ? "filters" : "search"
        search_results = params[:category_parent_id] == 0 ? {} : search_results(params, request_type)
        search_results.is_a?(Hash) ? search_results.with_indifferent_access['search'] : {}
      else
        get_response_data_for('search', params)['search']
      end
      respond_to do |format|
        format.js { render 'catalog_page' }
      end
    else
      session.delete(:unbxdparam_requestId) if session.has_key?(:unbxdparam_requestId)
      @category_parent_id = params[:category_parent_id]
      # @store_page = params[:category_parent_id] == 0 ? {} : (request.xhr? ? get_response_data_for('filters', params) : get_response_data_for('search', params)['search'])
      begin
        params[:facet] = JSON.parse(params[:facet]) if params[:facet].present?
      rescue JSON::ParserError => e
        params[:facet] = {}
      end
      request_type = request.xhr? ? "filters" : "search"
      @store_page = params.key?('category_parent_id') && [0,nil].include?(params[:category_parent_id]) ? {} : search_results(params, request_type)
      if @country_code == "IN" && params.has_key?(:q)
        session[:unbxdparam_requestId] = @store_page['unbxdparam_request_id']
        @store_page = @store_page.is_a?(Hash) ? @store_page.with_indifferent_access['search'] : {}
      end
      if params[:kind].present?
        @category_links = CategoryNewArrival.get_links(@category_parent_id, @country_code)
        @category_menu_links = CategoryNewArrival.get_links_for_nav_bar(@category_parent_id, @country_code)
      end
      @banner_category = CategoryBanner.category_banner_for(@country_code, @category) if @category.present?
      @breadcrumbs = generate_breadcrumb(category: @category, facet_name: params[:facets], designer: designer)
      if @store_page && @store_page['designs'].present?
        design_ids = @store_page['designs'].collect{|design| design['id']}
        gtm_data_layer.push(productIds: design_ids)
        store_ga_hash_v2(@category.id , @category.title, @store_page['designs'], designer=nil) if @category.present?
        store_ga_hash_v2(@store_page['designs'], designer) if designer.present?
      end
      if !amp_page && request.xhr?
        render :file => '/store/facet_sort.js', :content_type => 'text/javascript'
      end
    end
  end

  
  def search_results(params, type)
    if @country_code == "IN" && params.has_key?(:q)
      params['uid'] = cookies['unbxd.userId']
      params['country_code'] = @country_code
      unbxd_service_obj = Unbxd::SearchService.new(params)
      results = unbxd_service_obj.call()
    else
      results = get_response_data_for(type, params)
      results = results['search'] if type.eql?('search')
    end
    return results
  end

  def store_ga_hash_v2(item_list_id=nil, item_list_name=nil, designs, designer)
    @ga_hash_new = {
      "event": "ga4_view_item_list",
      "ecommerce": {
        "item_list_id": item_list_id || "", 
        "item_list_name": item_list_name || "",
        "country_code": @country_code,
        "items": [],
        "item_ids": []
      }
    }
    
    designs.each_with_index do |design, index|
      market_rate = CurrencyConvert.countries_marketrate[@country_code] || 1
      inr_discount_price = (design['discount_price'] * market_rate).round(CurrencyConvert.round_to)
      main_price = (design['price'] * market_rate).round(CurrencyConvert.round_to)
      discount = (main_price - inr_discount_price).round(CurrencyConvert.round_to)
      crumbs_category = []
      if @category.present?
        hash = @category.breadcrumb_path 
        crumbs_category = hash.keys
        array_index = crumbs_category.length > 2 ? 2 : 1
      end
      if designer.present?
        crumbs_category[1] = "Designer"
        array_index = 1
      end
      title = crumbs_category[array_index]
      item_listing_name, item_listing_id = [title, Category.find_by("LOWER(title) = '#{title.try(:downcase)}'").try(:id)] if title.present? && @category.present?
      item = {
        "item_id": design['id'].to_s,
        "item_name": design['title'],
        "discount": discount || 0,
        "item_brand": design['brand'],
        "item_variant": design['color'] || "",
        "price": inr_discount_price,
        "item_category": crumbs_category[1] || "",
        "item_category2": crumbs_category[2] || "",
        "item_category3": crumbs_category[3] || "",
        "item_category4": crumbs_category[4] || "",
        "item_category5": crumbs_category[5] || "",
        "quantity": 1,
        "index": index,
        "item_list_name": item_listing_name || "",
        "item_list_id": item_listing_id.to_s,
        "content_category" => item_listing_name || "",
        'image_url' => "https://#{design['image_url']}",
        'image_thumbnail_url' => "https://#{design['image_thumb_url']}",
        'item_created' => design['created_at'].to_s,
        'item_url' => "https://www.mirraw.com#{design['design_path']}" 
      }
      content_item = {
        "id": "#{design['id']}",
        "google_business_vertical": "retail"
      }
      @ga_hash_new[:ecommerce][:items] << item
      @ga_hash_new[:ecommerce][:item_ids] << content_item
    end

    @googe_add_hash_new = {
      "item_ids":[]
    }
    designs.each do|design,index|
      content_item = {
        "id": "#{design['id']}",
        "google_business_vertical": "retail"
      }
      @googe_add_hash_new[:item_ids] << content_item
    end
  end
  

  def generate_breadcrumb(category: nil, facet_name: nil, designer: nil)
    if category.present?
      current_category_url = nil
      breadcrumbs = category.breadcrumb_path.collect do |title, url|
        current_category_url = url
        {title: title.to_s, url: url}
      end
      if facet_name.present?
        facet_url = "#{current_category_url}/#{facet_name}"
        breadcrumbs << {title: facet_name.titleize, url: facet_url}
      end
      breadcrumbs
    elsif designer.present?
      breadcrumbs = [{title: 'Home', url: root_path}]
      breadcrumbs << {title: designer.try(:name).to_s, url: designers_path(designer)}
      breadcrumbs
    end.to_a
  end

  # Find Category parent ID based on kind, set item per page value
  #
  # == Parameters:
  # opt:: params[:kind] Data Type: String
  #
  #
  # == Returns:
  # String
  #
  def get_kind_category_id(kind)
    kind = kind.downcase
    if params[:facets].present?
      if params[:facets].try(:index, 'colour-') == 0
        facets = params[:facets].partition('colour-').last.split('--')
        facets = [facets.join('-')]
        seo_labels = facets.map { |color| kind + '-' + color }
        seo = SeoList.where(:label => seo_labels).to_a
        @seo = seo.first
        @seo ||= SeoList.where(:label => kind).includes(:category).first
      else
        seo_labels = params[:facets].gsub('_', '-')
        seo = SeoList.where(label: seo_labels).to_a
        @seo = seo.first
      end
      seo.to_a.each do |s|
        @seo.keyword += ', ' + s.keyword.to_s if @seo.keyword != s.keyword
      end
    else
      @seo = SeoList.where(:label => kind).includes(:category).first
    end
    if @seo && @seo.category.present?
      category = @seo.category
      @category_title = @seo.category.title
    elsif (category = Category.find_by_namei(kind)).present?
      @category_title = category.title
      @seo ||= SeoList.find_by(category_id: category.id)
    end
    category
  end


  def set_cache_buster
    response.headers["Cache-Control"] = "no-cache, no-store, max-age=0, must-revalidate"
    response.headers["Pragma"] = "no-cache"
    response.headers["Expires"] = "Fri, 01 Jan 1990 00:00:00 GMT"
  end

  def prepare_users_for_test(category)
    if ALLOW_AB_TEST_GRADING
      get_user_experiment_id
      unless experiment_running?(category)
        GRADING_EXPERIMENTS.each do |exp_id, exp_values|
          if AbTesting.is_valid_for?(@country_code, exp_values[:country]) && (category.include?(exp_values[:category]) || exp_values[:category] == 'all')  && AbTesting.allow_user?(session[:user_exp_id], exp_id, category)
            session[:exp_sorted][exp_id] = {date: exp_values[:date], category: exp_values[:category], sort: exp_values[:sort]}
            session[:exp_category][exp_values[:category]] = exp_id
            break
          end
        end
      end
    end
  end

  def sort_by_ab_test(kind)
    session[:exp_sorted].deep_symbolize_keys!.each do |id, values|
      if kind.include?(values[:category]) || values[:category] == 'all'
        return values[:sort]
      end
    end
    false
  end

  def get_user_experiment_id
    return session[:user_exp_id] if session[:user_exp_id].present?
    # ui_user_id = Rails.cache.read(:ui_user_id)
    # user_exp_id = (ui_user_id.present? && ui_user_id < 10) ? (ui_user_id.to_i + 1) : 1
    # Rails.cache.write(:ui_user_id,user_exp_id)
    session[:user_exp_id] = rand(1..100)
  end

  def experiment_running?(category)
    session[:exp_sorted] = {} unless session[:exp_sorted].is_a?(Hash)
    session[:exp_category] = {} unless session[:exp_category].is_a?(Hash)
    session[:exp_category].any?{|key, value| category.include?(key) || category == 'all'}
  end
end