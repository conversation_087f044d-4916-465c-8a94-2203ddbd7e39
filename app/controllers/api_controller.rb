class ApiController < ActionController::Base
  include DeviseTokenAuth::Concerns::SetUserByToken
  include ApiHelper
  include MirrawUtil

  before_filter :validate_headers?, :user_currency, :api_user_details, :set_api_data
  before_filter :set_review_exp, :set_cart, if: :web_request?
  # Setting the current_account and current_api_account for all
  # web users before calling current_account or current_api_account
  prepend_before_action :set_current_api_account, if: :web_request?

  # Avoding devise token auth functionality for web requests
  skip_before_action :set_request_start, if: :web_request?
  skip_after_action :update_auth_header, if: :web_request?

  # Block request if token is not valid
  #
  def validate_headers?
    head :unauthorized unless request.headers['Device-ID'].present? &&
                              request.headers['Token'].present? &&
                              ApiUser.token?(request.headers['Token'], params[:action])
  end

  def set_api_data
    if (api_account = current_api_account).present? && (user = current_user).present? && request.headers['Device-ID'].present? && !request.env["sent_from_web_mobile"] && !request.headers["Sent-from-Web-Mobile"]
      country = CurrencyConvert.currency_convert_cache_by_country_code(@country_code)
      api_account.set_api_data_values(request.headers['Device-ID'],
                                              @app_source,
                                              request.headers['App-Version'],
                                              country)
      user.assign_wallet if user.wallet.nil?
    end
  end

  def set_response_header
    response.headers['Customize-M-Headers'] = {'Iso-Code': @iso_code}.to_json
  end

  private

  # TODO Optimize this method so that only one before_action will be called
  # Common method for authenticating account and checking if account belongs to a user
  #
  def authenticate_user!
    unless current_api_account.user?
      sign_out_api_account
      render(json: {error: "Login allowed for user role only"}, status: 401)
    end
  end


  # Common method for responding to update, create, destroy actions
  #
  # == Parameters:
  #   object::
  #     ActiveRecord Model Object
  #
  def response_for(object, methods = [:id])
    if object.nil?
      head :no_content
    elsif object.errors.any?
      render json: { errors: object.errors.messages }, status: 422
    else
      render json: build_hash_for(object, methods), status: 200
    end
  end


  # Signs Out the logged in api account
  # REF: https://github.com/lynndylanhurley/devise_token_auth/blob/master/app/controllers/devise_token_auth/sessions_controller.rb
  # Duplicated the method except response
  #
  def sign_out_api_account
    if api_account_signed_in?
      # Deleting / Invalidating token
      api_account = remove_instance_variable(:@resource) if @resource
      client_id = remove_instance_variable(:@client_id) if @client_id
      remove_instance_variable(:@token) if @token

      if api_account and client_id and api_account.tokens[client_id]
        api_account.tokens.delete(client_id)
        api_account.save
      end
    end
  end

  # User associated with current account
  #
  # == Return:
  # User / Nil object
  #
  def current_user
    current_api_account.present? ? current_api_account.user : nil
  end

  # Find or creates cart for current user
  #
  # == Parameters:
  #   associations::
  #     Array
  #
  # == Return:
  # Cart object
  #
  def current_cart(associations = [])
    if request.headers['Device-ID'] == API_DEVICE_ID
      if !session[:cart_id].in?([nil, 0])
        conditions = {used: false, id: session[:cart_id]}
      else
        conditions = {used: false}
      end
    else
      conditions = {device_id: request.headers['Device-ID'], used: false}
    end
    @cart ||= Cart.includes(associations).order('id DESC').
      find_or_create_by(conditions)
    if current_api_account.present? && current_api_account.api_data.present? && !@cart.user_id.eql?(current_api_account.accountable_id)
      @cart.update_column(:user_id, current_api_account.accountable_id)
    end
    refresh_cart_ordered_items unless @current_cart_already_refreshed
    session[:cart_id] = @cart.id
    session[:cart_count] = get_cart_count
    return @cart
  end

  def get_cart_count
    @cart.line_items.sum(:quantity)
  end

  # Setting current_api_account and current_account for all web requests.
  # To avoid call to set_user_by_token method which fires query to update
  # current_accounts tokens field(REF: https://github.com/lynndylanhurley/devise_token_auth/blob/a561a9b74bd8f6eb957bc54a5ee3aa3714bcfc8e/lib/devise_token_auth/controllers/helpers.rb#L115)
  #
  # == Return:
  #  Account / Nil
  #
  def set_current_api_account
    @current_account ||= @current_api_account ||= warden.user(:account)
  end

  def set_cart
    session[:cart_id] = 0 unless session[:cart_id].present?
    session[:cart_count] = 0 unless session[:cart_count].present?
  end

  def refresh_cart_ordered_items
    unless @current_cart_already_refreshed
      @cart.refresh_ordered_items!
      @current_cart_already_refreshed = true
    end
  end
end
