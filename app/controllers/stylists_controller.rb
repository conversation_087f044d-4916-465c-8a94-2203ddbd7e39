class StylistsController < ApplicationController
  before_filter :authenticate_account!

  def approve_measurement
    stitch_mes = StitchingMeasurement.find_by_id(params[:measurement_id])
    suggested_ids = stitch_mes.suggested_measurements['suggested_ids']
    if params[:phone] == 'true'
      Stylist.sidekiq_delay.update_measurements(suggested_ids,current_user.id,stitch_mes.id,true)
      flash[:notice] = 'Phone Call Arrangement is scheduled'
      redirect_to root_url
    else
      attribute_hash = {}
      stitch_mes.suggested_measurements.each do |key,value|
        next if ['suggested_ids','user_note'].include?(key)
        attribute_hash[key.to_sym] = value
      end
      Stylist.sidekiq_delay.update_measurements(suggested_ids,current_user.id,stitch_mes.id,false,attribute_hash)
      flash[:notice] = 'Measurement Updated Successfully'
      redirect_to root_url
    end
  end

end