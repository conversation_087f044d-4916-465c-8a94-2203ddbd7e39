module MirrawUtil
  include <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
  extend ActiveSupport::Concern
  # Setting user currency and country_code according to params country_code
  #

  def append_info_to_payload(payload)
    super
    payload[:host] = request.host
    payload[:remote_ip] = request.remote_ip
    payload[:ip] = request.ip
    payload[:x_forwarded_for] = request.env['HTTP_X_FORWARDED_FOR']
    payload[:true_client_ip] = request.headers['True-Client-IP']
    payload[:remote_addr] = request.remote_addr
    payload[:http_true_client_ip] = request.env["HTTP_TRUE_CLIENT_IP"]
  end


  def user_currency
    country_code = if MOBILE_SOURCE
      (session.try{|s| s[:country].try{|c| c.symbolize_keys[:country_code]}} || params[:country_code])
    end
    country_code ||= (country_code_request = params[:country_code] || request.headers['HTTP_CLOUDFRONT_VIEWER_COUNTRY'] || request.headers['COUNTRY'] || request.headers['country-code'] || 'US')
    country = CurrencyConvert.currency_convert_cache_by_country_code(country_code)
    country ||= CurrencyConvert.currency_convert_cache_by_country_code('US')
    @rate = country.rate
    # $round_to object is used in module Priceable for price rounding purpose
    CurrencyConvert.round_to = country.round_to
    @country_code = country.country_code
    gon.country_code = @country_code
    Design.country_code = country_code_request || @country_code
    @actual_country = Country.country_name_by_code[Design.country_code || "US"]
    gon.country_name =  @actual_country
    gon.symbol = @symbol = country.symbol
    gon.hex_symbol = @hex_symbol = get_symbol_from(country.hex_symbol)
    @hex_symbol = country.hex_symbol
    @iso_code = country.iso_code
    @currency_symbol = country.currency_symbol
    @offer_message = ['desktop','mobile','android','ios'].include?(current_app_source.downcase) ? promise{ Promotion.offer_message(@country_code) } : promise{ Promotion.app_source_offer_message(current_app_source, @country_code)}
    @timer_promotion = promise{ PromotionPipeLine.get_active_promotion(@country_code) }
    client_ip = request.headers['HTTP_X_FORWARDED_FOR']
    if client_ip.present?
      first_ip = client_ip.split(',').first.to_s.strip
      ip_lookup = MAXMIND_IP.lookup(first_ip)
    else
      ip_lookup = MAXMIND_IP.lookup(request.ip)
    end
    if MOBILE_SOURCE && session[:country].blank? && session.try{|s| s[:country].try{|c| c.symbolize_keys[:country_code]}}.blank?
      countries_currencies = CurrencyConvert.currency_convert_memcached
      country_details      = countries_currencies.find{|c| c.country_code == @country_code} if @country_code != '--'
      country_details      = countries_currencies.find{|c| c.country_code == 'US'} if country_details.blank?
      session[:country]    = {name: country_details.country,symbol: country_details.symbol,rate: country_details.rate,country_code: country_details.country_code,hex_symbol: country_details.hex_symbol, city: (ip_lookup.city.name.presence || '')}
    end
    @delivery_city      = session[:country].try(:[],:city).presence || ip_lookup.city.name.presence || '' if MOBILE_SOURCE
  end

  def api_user_details
    @app_source = current_app_source
    User.app_source = @app_source.downcase == 'desktop' ? 'mobile' : @app_source
  end


  def set_city_for_app_request
    client_ip = request.headers['HTTP_X_FORWARDED_FOR']
    if client_ip.present?
      first_ip = client_ip.split(',').first.to_s.strip
      ip_lookup = MAXMIND_IP.lookup(first_ip)
    else
      ip_lookup = MAXMIND_IP.lookup(request.ip)
    end
    @delivery_city       = request.headers['HTTP_CLOUDFRONT_VIEWER_CITY'] || ip_lookup.city.name.presence || ''
  end

  def current_app_source
    app_source = if request.headers['App-Source'].present?
                   request.headers['App-Source'] || 'Android'
                 elsif request.headers['HTTP_CLOUDFRONT_IS_DESKTOP_VIEWER'] == 'true'
                   'desktop'
                 else
                   'mobile'
                 end
  
    if (category = request.headers['Sub-App']).present?
      app_source += "-#{category}"
    end
  
    app_source
  end

  # Get API Response for a perticular action
  #
  # == Parameters:
  # action_name::
  #   STRING
  #
  # == Returns:
  # JSON
  #
  def get_response_data_for(action_name, request_params = {})
    if request_params[:request_url_provided]
      url = self.send(action_name << '_path')
      request_params.delete(:request_url_provided)
    else
      url = api_request_url(action_name)
    end
    old_request_format = get_old_request_type
    controller_name, action_name = api_controller_and_action(url, request_params[:request_method])
    response_body = render_api_action_response(controller_name, action_name, request_params)
    request.format = old_request_format
    response_body
  end

  # Get Web Request Type
  #
  # == Returns:
  # STRING
  #
  def get_old_request_type
    req_format = request.format.to_s
    if req_format.match('html')
      return 'html'
    elsif req_format.match('json')
      return 'json'
    elsif req_format.match('xml')
      return 'xml'
    else
      return 'js'
    end
  end

  # Get Request API Controller and action name
  #
  # == Parameters:
  # url::
  #   STRING
  #
  # == Returns:
  # ARRAY
  #
  def api_controller_and_action(url, request_method)
    request_method = request_method || "GET"
    request_action = Rails.application.routes.recognize_path(url, method: request_method)
    c_name = request_action[:controller].classify
    c_name = c_name.pluralize if !request_action[:controller].match('store') && !request_action[:controller].match('juspay')

    controller_name = "#{c_name}Controller"
    action_name = request_action[:action]
    [controller_name, action_name]
  end

  # Creates url for api request
  #
  # == Parameters:
  # action_name::
  #   STRING
  #
  # == Returns:
  # STRING
  #
  def api_request_url(action_name)
    api_path = "#{API_VERSION}_#{action_name}_path"
    self.send(api_path)
  end

  # Call Api Methods and get response body
  #
  # == Parameters:
  # controller::
  #   STRING
  # action::
  #   STRING
  # params::
  #   HASH
  #
  # == Returns:
  # JSON
  #
  def render_api_action_response(controller, action, request_params={})
    c = controller.constantize.new
    c.params = request_params
    api_request = request
    api_request.headers['Token'] = API_TOKEN
    api_request.headers['Device-ID'] = API_DEVICE_ID
    api_request.format = 'json'
    api_request.env["sent_from_web_mobile"] = true
    c.dispatch(action, api_request)
    c.response.body.present? ? JSON.parse(c.response.body) : {}
  end

  def set_review_exp
    @is_review_exp = cookies[:review_exp].to_i == 1
  end

  def self.platform
    %w[mobile desktop].include?(User.app_source) ? User.app_source : 'app'
  end

  def set_cart_promotion
    @prepaid_payment_promotion = PrepaidPaymentPromotion.new(@cart, @country_code) if PrepaidPaymentPromotion.active?(@country_code)
  end
end
