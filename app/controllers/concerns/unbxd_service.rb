# require 'httparty'
class UnbxdService
    include HTTParty

    base_uri MirrawMobile::Application.config.unbxd[:UNBXD_SERVICE_URL]
    
    class << self

        def get_recommendation_result params
            post("/mobile/recommend", body: params.to_json)
        end

        # TODO: SEARCH SHOULD ALSO BE CALLED FROM THIS SERVICE
        # def get_search_result params
        
        # end
    end
    
end