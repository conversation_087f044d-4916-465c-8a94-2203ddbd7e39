class UnbxdRecommendationsController < ApplicationController

  before_filter :set_unbxd_widget
  skip_before_filter :set_menu, :set_cart, only: [:more_like_these]

  %w(recommended_for_you recently_viewed more_like_these also_viewed also_bought cart_recommend top_sellers category_top_sellers brand_top_sellers pdp_top_sellers).each do |method_name|
    define_method(method_name.to_sym) do
      if params[:url_params].present?
        response = @unbxd_widget.get_response_for_widget(params[:url_params])
        if response == nil
          head :no_content
        else
          @recd_response = update_prices(response) if response['status'] == 200
        end
      end
      @recd_response ||= 'error'
      render :file => '/unbxd_recommendations/unbxd_recommendations.js', :content_type => 'text/javascript'
    end
  end

  private

  def set_unbxd_widget
    widget_title = params[:action].upcase
    @unbxd_widget = RequestStore.cache_fetch("unbxd_widget_mobile_#{widget_title}",expires_in: 24.hours) do
      UnbxdWidget.find_by_title(widget_title)
    end
  end

  def update_prices(response)
    if response['Recommendations'].blank?
      response
    else
      Design.update_effective_prices(instance_variable_get(:@symbol), response, instance_variable_get(:@country_code))
    end
  end
end
