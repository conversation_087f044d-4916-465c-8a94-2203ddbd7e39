require 'size_chart_mapping'

class StitchingMeasurementsController < ApplicationController
  before_filter :authenticate_account! ,only: :index

  include PagesHelper
  before_action :get_current_user, only: [:stitching_form], unless: :is_web_request?

  MAX_MIN_W_H = MeasurementInfo.select('MAX(weight) as max_weight,MIN(weight) as min_weight, MAX(height) as max_height,MIN(height) as min_height').max
  MEASUREMENT_EXPERIENCE_MAPPING = ['noobie', 'experienced', 'professional']

  def index
    if current_user.present?
      @stitching_measurements= current_user.stitching_measurements.where('measurement_name is not null').page(params[:page]).per(10)
    end
  end

  def create
    mapped_stitching_measurements = SizeChartMapping.new(params[:type_of_suit], stitching_measurement_params[:chest_size]).()
    if params[:type_of_suit] == 'Lehenga'
      saree_measurements = SizeChartMapping.new('Saree', stitching_measurement_params[:chest_size]).()

      mapped_stitching_measurements = mapped_stitching_measurements.slice(:chest_size, :waist_size, :hip_size, :code)
      saree_measurements = saree_measurements.slice(:length, :under_bust, :size_around_arm_hole, :shoulder_size)

      mapped_stitching_measurements = mapped_stitching_measurements.merge(saree_measurements)
    end

    stitching_measurement_attributes = stitching_measurement_params.merge(mapped_stitching_measurements) { |key, old_value, new_value| old_value.presence || new_value }
    stitching_measurement_attributes["style_no"] = "As per image" if stitching_measurement_attributes["style_no"] == "0"
    stitching_measurement_attributes["back_style_no"] = "As per image/Recommend by stylist" if stitching_measurement_attributes["back_style_no"] == "0"
    
    stitching_measurement = StitchingMeasurement.new(stitching_measurement_attributes)
    stitching_measurement.measurement_group = 'user'
    stitching_measurement.user_review['user_measurement_experience'] = MEASUREMENT_EXPERIENCE_MAPPING[params[:measurement_experience].to_i - 1]
    order = Order.where(id: stitching_measurement.order_id).first
    if order.present?
      line_item = order.line_items.where(design_id: stitching_measurement.design_id, stitching_required: 'Y').first
      if line_item.present?
        stitching_measurement_count = StitchingMeasurement.where(line_item_id: line_item.id).count
        stitching_measurement.line_item_id = line_item.id
        if stitching_measurement_count < line_item.quantity
          stitching_measurement.measurement_info_done_by = 'User'
          stitching_measurement.user_id = current_account.try(:user).try(:id) if current_account.try(:user).try(:id) == order.user_id
          stitching_measurement.user_review['used_measurement_tag'] = params[:stitching_measurement][:selected_mes_tag] if params[:stitching_measurement][:selected_mes_tag].present?
          stitching_measurement.user_review['used_review_id'] = params[:stitching_measurement][:selected_review_id] if params[:stitching_measurement][:selected_review_id].present?          
          notes = line_item.line_item_addons.pluck(&:notes).compact.join(',').downcase
          stitching_measurement.padded = notes.include?('padded') ? 'Yes' : 'No'
          stitching_measurement.cancan = notes.include?('cancan') ? 'Yes' : 'No'
          stitching_measurement.save
          if !order.stylist_id? && order.state == 'sane'
            Stylist.sidekiq_delay(queue: 'high')
                   .assign_to_stylist_mobile(
                     order.id,
                     stitching_measurement.line_item_id
                   )
          elsif order.state == 'sane'
            stitching_measurement.update_column(:stylist_id,order.stylist_id)
          end
          status_order = true
          stitching_measurement.update_order_state(params[:measurement_repeat].try(:[],:quantity_repeat),params[:similar_products].split(','))
          Order.sidekiq_delay.send_stitching_info_mail_to_user_from_mobile(order.id)
          if !stitching_measurement.new_record?
            stitching_measurement.create_stylist_measurement
            StitchingMeasurement.sidekiq_delay(queue: 'high').generate_pdf(stitching_measurement.line_item_id, 'stylist')
          end
        end
      end
    end
    render json: {head: :ok}
  end

  def stitching_form
    if params[:order_number].present? && params[:design_id].present? && params[:order_number].length == 10 && params[:design_id].length < 40 && (order = Order.preload(:line_items).find_by_number(params[:order_number])).present? && (@design = order.designs.where(id: params[:design_id]).first).present? && (line_item = order.line_items.select{|li| li.design_id == @design.id}.first).present? && line_item.stitching_required == 'Y'
      if @curr_account.present? && (current_user.blank? || (current_user.try(:email) != request.headers['Uid']))
        sign_in(:account, @curr_account)
      end
      if current_user.present? && order.user_id != current_user.id
        redirect_to root_url,notice: 'You are not authorized to access this page'
        return
      end
      @order_id = order.id
      @order_number = order.number
      @user_id = order.user_id
      @line_item_id = line_item.id
      @plus_size_product = line_item.is_product_plus_size? if line_item.present? && @design.present? && @design.designable_type.to_s.downcase == 'saree'
      @quantity = line_item.quantity
      @stitching_measurements = StitchingMeasurement.where(line_item_id: line_item.id)
      @user_measurements = StitchingMeasurement.unscoped.where(user_id: @user_id, measurement_group: 'user').where('measurement_name is not null AND height is not null AND height <> ?','0').where(product_designable_type: get_designable_from_params).order('created_at desc').limit(20).collect{|sm| [sm.measurement_name,sm.id]}
      @user_measurements.unshift(['Create New','Create New'])
    else
      redirect_to root_url,notice: 'Page Not Found'
    end
  end
  
  def measurement_data
    if params[:height].present?
      @selected_stitching_measurement = StitchingMeasurement.unscoped.where(user_id: current_user.id,id: params[:measurement_select]).first if current_user.present?
      height = @selected_stitching_measurement.try(:height) ? ((mes_height = @selected_stitching_measurement.try(:height)).length == 1 ? (mes_height.to_i * 10) : mes_height.sub('.','').to_i)  : params[:height].to_s.sub('.','').to_i
      weight = @selected_stitching_measurement.try(:weight) || params[:weight].to_i
      params[:weight] = weight.to_i
      params[:height] = mes_height.length == 1 ? mes_height.to_f : mes_height if mes_height.present?
      weight = [MAX_MIN_W_H.max_weight.to_i, MAX_MIN_W_H.min_weight.to_i, weight].sort[1]
      height = [MAX_MIN_W_H.max_height.to_i, MAX_MIN_W_H.min_height.to_i, height].sort[1]
      design_type = (type = params[:product_designable_type].to_s.strip) == 'kurti' ? 'anarkali' : type
      @plus_size_design = params[:plus_size_addon] == 'true'
      @measurement_info = MeasurementInfo.where(weight: weight).where(height: height,product_designable_type: design_type).first.try(:attributes).try(:symbolize_keys!)
      @partial_name = design_type == 'lehenga_choli' ? 'lehenga_form' : 'salwarkameez_form'
      @quantity = params[:quantity].to_i
      @stitching_measurement_count =  StitchingMeasurement.where(order_id: params[:order_id], design_id: params[:design_id]).count
      @design = Design.find_by_id(params[:design_id])
      order = Order.find_by_id(params[:order_id])
      if params[:product_designable_type] == 'lehenga_choli'
        all_styles_mapping = MeasurementInfo.select('front_styles, back_styles, hook, sleeves, design_id').where('product_designable_type IS NULL AND (design_id IS NULL or design_id = ?)', @design.id).map{|i| i.design_id.present? ? [i.design_id, [i.front_styles, i.back_styles, i.hook, i.sleeves]] : [i.front_styles.to_s, [i.back_styles.to_s, i.hook.to_s, i.sleeves.to_s]]}.to_h
        if all_styles_mapping.key?(@design.id)
          design_specific_mapping = all_styles_mapping.delete(@design.id).to_a.map{|i| i.to_s.split(',')}
          all_styles_mapping.slice!(*design_specific_mapping[0])
          all_styles_mapping.keys.each do |front_style|
            (0..2).to_a.each do |index|
              all_styles_mapping[front_style][index] = (all_styles_mapping[front_style][index].split(',') & design_specific_mapping[index+1]).join(',')
            end
          end
        end
        @styles_mapping = all_styles_mapping
      end
      @product_mapping = MeasurementInfo.where(design_id: @design.id, product_designable_type: design_type).map{|info| [(info.product_designable_type == 'lehenga_choli' ? info.design_id.to_s + '-' + info.chest_size.to_s : info.design_id.to_s), [info.chest_size.to_s.split('-').map(&:to_i), info.length.to_s.split('-').map(&:to_i), info.sleeves.to_s.split('-').map(&:to_i), info.bottom_length.to_s.split('-').map(&:to_i)]]}.to_h
      @bust_size_mapping = false
      @bust_values = if @product_mapping.present? && !@product_mapping.key?(@design.id.to_s)
        @bust_size_mapping = true
        @product_mapping.map{|k,v| v[0].first}
      else
        SIZE_CHART_TABLE["#{@design.designable_type}-Size"]["Sizes"].map do |size|
          size["size"]
        end
      end
      @bust_values.to_a.select!{|val| @design.check_if_plus_size_serviceable(val) }
      @similar_bought_products = order.line_items.preload(design: :designer).joins(:design,line_item_addons: :addon_type_value).joins('left outer join stitching_measurements on stitching_measurements.line_item_id = line_items.id').where('lower(stitching_required) = ? AND lower(designs.designable_type) = ? AND line_items.design_id <> ?','y',@design.designable_type.try(:downcase) ,params[:design_id]).where('lower(addon_type_values.name) IN (?)',['custom stitching', 'custom blouse stitching']).where('stitching_measurements.id is null') if order.present?
      if @measurement_info.present?
        render file: '/stitching_measurements/measurement_form_update.js', content_type: 'text/javascript'
      else
        redirect_to root_path, notice:'Page Not Found.'  
      end
    else
      redirect_to root_path, notice:'Page Not Found.'
    end
  end

  def get_back_styles
    render file: '/stitching_measurements/show_back_styles.js', content_type: 'text/javascript' if (@front_style_no = params[:front_style_no]).present? && (@back_styles = params[:back_styles]).present?
  end

  private

  def get_designable_from_params
    return params[:product_designable_type] == 'anarkali' ? %w(anarkali anarkali_pant kameez_pant kameez_salwar kameez_chudidar anarkali_with_bottom_as_per_image kameez_with_bottom_as_per_image) : (@design.designable_type == 'Lehenga' ? 'lehenga_choli' : (['Kurti','Islamic'].include?(@design.designable_type) ? @design.designable_type.try(:downcase) : 'blouse'))
  end

  def stitching_measurement_params
    params.require(:stitching_measurement).permit(:measurement_name,:design_id, :line_item_id, :order_id, :size_around_ankle, :shoulder_size, :product_designable_type,
                  :length, :embroidary, :chest_size, :sleeves_length, :sleeves_around, :front_neck, :back_neck,
                  :size_around_thigh, :bottom_length, :size_around_arm_hole, :waist_size, :hip_size, :size_around_knee,
                  :measurement_locked, :padded, :style_no, :hook, :weight, :height, :under_bust, :stitching_label_url, :measurement_info_done_by,
                  :stitching_notes,:shoulder_to_apex,:measurement_type, :age, :denim_waist_size, :accuracy_info, :source_info, :back_style_no)
  end

end


