class HealthController < ApplicationController

    def healthcheck
        
        http_envs = {}.tap do |envs|
            request.headers.each do |key, value|
              envs[key] = value if key.downcase.starts_with?('http') or key.downcase.starts_with?('x') 
            end
        end

        
        # binding.pry
        
        
        render json: {headers: http_envs, 
            remote_ip: request.remote_ip, 
            remote_addr: request.remote_addr,
            true_client_ip: request.headers['True-Client-IP']
        }.to_json
    end

end
  