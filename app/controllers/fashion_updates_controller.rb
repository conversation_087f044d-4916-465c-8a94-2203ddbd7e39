class FashionUpdatesController < ApplicationController
  caches_action :index, cache_path: Proc.new { |c| c.params }, expires_in: API_CACHE_LIFESPAN.minutes
  caches_action :category_page, cache_path: Proc.new { |c| c.params }, expires_in: API_CACHE_LIFESPAN.minutes

  def index
    @fashion_updates = FashionUpdate.page params[:page]
  end

  def category_page
    @fashion_updates = FashionUpdate.joins(:fashion_update_categories).where('fashion_update_categories.name = ?', params[:category]).page params[:page]
    render action: 'index'
  end

  def show
    @fashion_update = FashionUpdate.friendly.find(params[:id])
    @breadcrumbs = generate_breadcrumb(category: @fashion_update)
    @next_post = @fashion_update.next
    @previous_post = @fashion_update.previous
    @recent_post = Rails.cache.fetch(:fashion_updates_recent_post, expires_in: 6.hours) do
      FashionUpdate.limit(4).all || []
    end
    rescue
      redirect_to fashion_updates_path, notice: 'We couldn\'t find the post you were looking for.'
  end

  def generate_breadcrumb(category: nil)
    breadcrumbs = [{title: 'Home', url: fashion_updates_path}]
    if (categories = category.fashion_update_categories).present?
      all_categories = categories.first.category_names
      all_categories.each do |cat|
        breadcrumbs << {title: cat.blog_category_title, url: blog_category_path(cat.name)}
      end
      breadcrumbs << {title: category.blog_title}
    end
    breadcrumbs
  end

end
