class AccountsController < ApplicationController

  require 'one_time_password_service'
  
  def update
    @account = Account.find_by_id(params[:id])
    if @account && @account.update_attributes(update_account_params)
      @account.send_pwd_reset_link_via_sms if !!@account.guest_account
    end
    respond_to do |format|
      format.js
    end
  end

  def send_otp
    account_exists = Account.find_by_phone(params[:phone]).present? ? true : false
    sms_sent = false
    if account_exists
      sms_sent = sent_sms_service(params[:phone])
    elsif ENABLE_AUTO_SIGNUP_WITH_MOBILE
      hash = {}
      hash[:password] = hash[:password_confirmation] = Devise.friendly_token.first(6)
      hash[:email] = Faker::Name.first_name+Faker::Number.number(10)+"<EMAIL>" if hash[:email].blank?
      hash[:phone] = params[:phone]
      hash[:dial_code] = "+91"
      acc = Account.auto_create_account_user(hash)
      if acc
        account_exists = true
        sms_sent = sent_sms_service(params[:phone])
      end
    end
    render json: {error: !account_exists, sms_sent: sms_sent}
  end

  private
  def update_account_params
    params.require(:account).permit(:phone, :dial_code)
  end

  def sent_sms_service(phone)
    response = OneTimePasswordService.deliver('91' + params[:phone], "[PHONE_NUMBER], OTP:[OTP_NUMBER] is your one time password to proceed on Mirraw. It is valid for 10 minutes. Do not share your OTP with anyone.", 4)
    return response.include?('MsgID') ? true : false
  end
end