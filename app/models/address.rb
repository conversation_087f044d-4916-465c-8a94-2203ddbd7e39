# == Schema Information
#
# Table name: addresses
#
#  id             :integer          not null, primary key
#  name           :string(255)
#  street_address :string(255)
#  landmark       :string(255)
#  city           :string(255)
#  state          :string(255)
#  country        :string(255)
#  pincode        :string(255)
#  phone          :string(255)
#  user_id        :integer
#  created_at     :datetime         not null
#  updated_at     :datetime         not null
#  default        :integer          default(0)
#

class Address < ActiveRecord::Base

  include StripAttribute
  strip_attributes :name, :street_address, :city, :state, :phone, :country, :landmark, :pincode
  scope :default_address, -> { where(default: 1) }

  include RequestAccessor
  rattr_accessor :shipping_country

  # (1..2) signifies total number of address lines
  def self.street_address_lines
    @street_address_address_lines ||= (1..3).collect{|index| "street_address_line_#{index}"}
  end

  attr_accessor :email
  belongs_to :user

  before_save :update_default_address

  before_destroy :check_if_default_address

  validates :phone, :pincode, presence: true

  validates :name, presence: { message: 'Please specify a Name' },
                   length: { maximum: 255, message: 'is too long.' }

  validates :city, presence: {message: "Please specify a City"}
  validates :state, presence: {message: "Please specify a State"}
  validates :country, presence: {message: "Please specify a Country"}

  # Formats to be allowed are +(123) 123 123 12, +12 123 123123, 12331213213

  # Formats to be allowed are 442402, 44240-5555, G3H 6A3
  validates :pincode, format: { with: /\A[^\s-][A-z\d\-\s]+\z/ }, if: :valid_pincode?

  validates :street_address, presence: { message: "Please specify a Street" },
                             length: { maximum: 255 }
  validate :validate_phone_number

  scope :recently_updated, -> { order(updated_at: :desc) }
  # Temporary remove validation 24th Aug 2019
  #validate :check_address_validation, if: :new_record?

  # Updates the previously default address if this address is made default
  #
  # == Returns
  # Boolean
  #
  def update_default_address
    if self.default == 1 && user.default_address && user.default_address != self
      user.default_address.update_column(:default, 0)
      self.default = 1
    elsif !user.default_address
      self.default = 1
    end
  end

  # Check whether address is international
  #
  # == Returns:
  # Boolean
  #
  def international?
    self.country.downcase != 'india'
  end


  def check_if_default_address
    if self.default > 0
      self.errors.add(:base, 'Default address can not be deleted!')
      return false
    end
  end

  def formatted_phone_number
    if phone =~ /^(\d{3})(\d{3})(\d{1,})$/
      "#{$1}-#{$2}-#{$3}"
    else
      phone
    end
  end

  def check_address_validation
    if (self.new_record? || self.pincode_changed?) && ['United States'].include?(country) && PINCODE_FORMATS[country].length != pincode.length
      errors.add(:pincode, 'enter valid 5 character Postal code')
    end
    if self.new_record? && ['United States', 'Australia'].include?(country) && street_address.match(/P[\.\s]*O[\.\s]+Box/)
      errors.add(:street_address, "Please enter a valid street address, we do not accept P.O. Box addresses")
    end
  end

  def validate_phone_number
    if !phone.blank? && !country.blank?
      if country.downcase == "india" && (phone_without_dail = phone.gsub(/^\+91|^0/, '')) && phone_without_dail.match(/^[6-9]\d{9}$/).blank?
        errors.add(:phone, "Please specify a valid 10 digit Phone Number")
      elsif country.downcase != "india" && phone.length < 8
        errors.add(:phone, "Please specify a Correct Phone Number(too short)")
      elsif country.downcase != "india" && phone.length > 20
        errors.add(:phone, "Please specify a Correct Phone Number(too long)")
      end
    end
  end

  def valid_pincode?
    if self.country.downcase == 'india' 
      self.pincode.match(/^[1-9][0-9]{5}$/).nil? ? errors.add(:pincode,'Enter 6 digit pincode') : true
    else
      true
    end
  end

  include StreetAddressLineable
  include Fullnameable.fullnameize(:name) if @country_code != 'IN'
end
