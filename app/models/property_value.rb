# == Schema Information
#
# Table name: property_values
#
#  id          :integer          not null, primary key
#  name        :string(255)
#  position    :integer
#  property_id :integer
#  created_at  :datetime         not null
#  updated_at  :datetime         not null
#  p_name      :string(255)
#

class PropertyValue < ActiveRecord::Base
  belongs_to :property
  has_and_belongs_to_many :faqs
  delegate :p_name, to: :property, prefix: true
  delegate :fixed, to: :property

  class << self
    def get_color_pv_id_list
      hash = Hash.new
      PropertyValue.select('lower(property_values.name) as name,array_agg(property_values.id) as pv_id').where(property_id: Property.where("name in ('color','saree_color','kameez_color','lehenga_color')").pluck(:id)).group('lower(property_values.name)').map{|pv| hash[pv.name.to_sym] = pv.pv_id.is_a?(String) ? (eval pv.pv_id.tr('{','[').tr('}',']')) : pv.pv_id}
      hash
    end

    # Returns the property value information of properties which have facet priority > 0
    #
    # kind    ->  Name of Category
    # facets  ->  Names of property values
    # details ->  Incase of category where URL's are generated from facets:
    #             details = true   --> Return detailed hash of property values
    #             details = false  --> Return ID's of property values
    def get_property_values_for_facets(kind, facets, details: false)
      property_value_names = get_property_value_names_for_facets(kind, facets, details)
      return [] if property_value_names.empty?
      if details
        imp_property_values = Rails.cache.fetch("facets-#{kind}-#{property_value_names.sort.join('-')}", expires_in: 24.hours) do
          # Search within PropertyValue table for property values having 'name'
          # separated by either '-' AND '_'
          names_for_search = property_value_names.inject([]) do |values, value_name|
            values.push(value_name.gsub('-','_')) if value_name.index('-')
            values.push(value_name)
          end
          # Find property_value_ids of all of the required facets belonging to properties
          # having facet priority not equal to zero
          required_property_values = PropertyValue.includes(:property).where('properties.facet_priority != 0').
            where(name: names_for_search).order('properties.facet_priority desc').to_a.uniq(&:name)
          property_values = required_property_values.collect do |prop_value|
            { name: prop_value.name.gsub('_','-'),
            priority: prop_value.property.facet_priority,
            type: prop_value.property.name.match(/color/i) ? 'checkbox' : 'radio',
            property: prop_value.property.name,
            id: prop_value.id }
          end
          property_values = [] unless property_values.count >= property_value_names.count
          property_values
        end
      else
        imp_property_values = property_value_names.inject([]) do |values, color|
          colour_values = COLOR_VALUE_ID_LIST[color.downcase.to_sym]
          colour_values.present? ? values.push(*colour_values) : values.clear
          values.empty? ? break : values
        end.to_a
        imp_property_values.uniq!
      end
      imp_property_values
    end

    private
      # Returns an array of different property value names based on the string passed
      # to this method.
      #
      # kind    ->  Name of Category
      # facets  ->  String of names of property values
      def get_property_value_names_for_facets(kind, facets, create_facets_for_kind)
        facet_names = []
        if facets.present?
          if create_facets_for_kind
            facets = facets.rpartition("-#{FACETED_URL_KINDS[kind]['name']}")
            if facets[1].present? && facets[0].present? && facets[2].empty?
              facet_names = facets[0].split('_')
            end
          else
            facets = facets.partition('colour-')
            if facets[1].present? && facets[2].present? && facets[0].empty?
              facet_names = facets.last.split('--')
            end
          end
        end
        facet_names
      end
  end

end
