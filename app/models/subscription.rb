class Subscription < ActiveRecord::Base
  validates_presence_of :country, :email, :ip_address, :source_url
  validates_uniqueness_of :email

  class << self
    def add(params,country_code=nil, hex_symbol=nil)
      @coupon = Coupon.signup_coupon_for(country_code) if country_code.present?
      @response = { error: false, error_message: '', data: nil, already_subscribed: '' }
      if (@subscription = Subscription.where(email: params[:email]).first_or_initialize).new_record?
        @subscription.update_attributes(params) ? successful(country_code, hex_symbol, params[:appsource]) : errored
      else
        repeated
      end
      [@response, @subscription]
    rescue ActiveRecord::RecordNotUnique
      repeated
      [@response, Subscription.find_by_email(params[:email])]
    end

    def push_to_mailup(_email, country_code, app_source=nil); end

    def send_subsciption_welcome_mail(_subscriber_email, _data, _country_code); end

    private

    def successful(country_code=nil, hex_symbol=nil, app_source=nil)
      if country_code.present? && @coupon.present?
        coupon_msg = ApplicationController.helpers.coupon_text(@coupon, hex_symbol, true)
        @response[:data] = {message: coupon_msg, coupon_code: @coupon.code}
        coupon_data = {message: "To Get #{coupon_msg}, Use Coupon Code:", coupon_code: @coupon.code}
        Subscription.sidekiq_delay.send_subsciption_welcome_mail(@subscription.email, coupon_data, country_code)
      else
        @response[:data] = 'Thank you for Subscribing to our Newsletter.'
      end
      Subscription.sidekiq_delay.push_to_mailup(@subscription.email,@subscription.country,app_source)
    end

    def errored
      @response[:error] = true
      @response[:error_message] = "Email Id : #{@subscription.errors.messages.values.join(', ')}"
    end

    def repeated
      @response[:data] = 'Aha!!, We noticed you are already subscribed.'
      @response[:already_subscribed] = '1'
    end
  end
end
