# == Schema Information
#
# Table name: menus
#
#  id         :integer          not null, primary key
#  title      :string(255)
#  position   :integer
#  link       :text
#  created_at :datetime         not null
#  updated_at :datetime         not null
#  hide       :boolean
#

class Menu < ActiveRecord::Base
  scope :mirraw, -> { where(app_name: [nil,'']) }
  scope :luxe, -> { where(app_name: 'luxe') }

  has_many :menu_columns
  has_many :menu_tabs
  has_many :tabs, through: :menu_tabs
  def self.menu_by_hide_appsource_country(country, app_source, sub_app)
    menus = Rails.cache.fetch("menu_list_#{country}_#{app_source}_#{sub_app}",expires_in: 24.hour) do
      country = "%#{country}%"
      app_source = "%#{app_source}%"
      ## For Apps , we use Modest menu and not Islamic_Clothing menu 
      sub_app = ( sub_app == 'Islamic_Clothing') ? 'Modest' : sub_app
      sub_app.present? ? sub_app_clause = "title = '#{sub_app}'" : sub_app_clause = ""
      where_clause = "(country ILIKE ? OR country IS NULL OR country='') AND (app_source IS NULL OR app_source ILIKE ? OR app_source='') AND (hide = ?)"
      menus = Menu.mirraw.where(where_clause, country, app_source,false).where(sub_app_clause).order(:position).to_a
      ActiveRecord::Associations::Preloader.new.preload(menus, :menu_columns, MenuColumn.where(where_clause, country, app_source, false).order(:position))
      ActiveRecord::Associations::Preloader.new.preload(menus, {menu_columns: :menu_items}, MenuItem.where(where_clause, country, app_source, false).where(mobile_menu: true).order(:position))
      menus
    end
    menus.keep_if{|menu| menu.menu_columns.to_a.any?{|mc| mc.menu_items.present?}}
  end

  def self.menu_for_tags_country(country, app_source)
    Menu.mirraw.where(hide: false)
        .where("(menus.country ILIKE :country OR menus.country IS NULL OR menus.country='')
                AND(menus.app_source IS NULL OR menus.app_source ILIKE :app_source OR menus.app_source='')",
                country: country,
                app_source: app_source) 
        .order('menus.position')
  end
end
