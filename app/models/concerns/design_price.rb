module DesignPrice
  extend ActiveSupport::Concern

  def self.included(base)
    base.class_eval do
      include DesignPromotions
    end
  end

  def designer_quantity
    (quantity.to_i - in_stock_warehouse.to_i)
  end

  def discount_percent
    return design.discount_percent if is_a?(Variant)
    seller_campaign = self.designer.seller_campaigns.select { |campaign| campaign.active? }
    campaign_discount = seller_campaign.present? ? self.design_campaign_discounts.find_by(designer_id: self.designer_id, seller_campaign_id: seller_campaign.first.id).try(:discount).to_f : 0
    (100 - ((100 - self[:discount_percent].to_f) * (100 - self.get_vendor_additional_discount.to_f) / 100.0 * (100 - campaign_discount ) / 100.0))
  end

  def discount_price
    return design.discount_price if self[:price].to_i == 0 && is_a?(Variant)
    (self[:price].to_f * (100 - discount_percent) / 100.0).to_i
  end

  def get_vendor_additional_discount
    des = is_a?(Variant) ? self.design.designer : self.designer
    self.sor_available? ? 0 : des.try(:vendor_additional_discount_percent).to_i
  end

  def get_vendor_selling_amount
    transfer_model = (is_a?(Variant) ? design.designer : designer).is_transfer_model?
    transfer_model ? self.transfer_price : self.discount_price
  end

  def cod_available_for_design(country, actual_country)
    cod = false
    if ENABLE_COD_COUNTRIES.include?(actual_country.try(:downcase))
      if !(country=='IN')
        cod = true if (self.sor_available? || self.is_classiques? || ENABLE_COD_FOR_DESIGNERS_FOR_UAE.include?(self.designer_id))
      else
        cod = true if (self.sor_available? && ALLOWED_DOMESTIC_SOR) 
        cod = true if (self.designer.cod? || self.is_classiques?)   
      end
    end
    cod
  end

  def sor_available?
    SOR_READY_TO_SHIP && self.in_stock_warehouse.to_i > 0 && self.quantity.to_i > 0
  end

  def is_classiques?
    self.designer_id == CLASSIQUES_DESIGNER.id
  end

  def get_scale
    if (bmgnx_hash = PromotionPipeLine.bmgnx_hash).present? && bmgnx_hash[:scale].to_i >= 1 && discount_price < bmgnx_hash[:filter].to_i
      return bmgnx_hash[:scale].to_f
    elsif (qpm_scale = QuantityDiscountPromotion.scale.to_f) > 0
      return qpm_scale
    end
    return design.get_scale if is_a?(Variant)
    if DYNAMIC_PRICE_ENABLED && Design.country_code != 'IN' && self.dynamic_pricing?
      @dynamic_price ||= if dynamic_prices.loaded?
        dynamic_prices.find{|dp| dp.country_code == Design.country_code}
      else
        dynamic_price_for_current_country.first
      end
      return @dynamic_price.scale.to_f if @dynamic_price.present?
    elsif DYNAMIC_PRICE_ENABLED && Design.country_code == 'IN' && self.dynamic_pricing?
      @dynamic_price ||= dynamic_price_for_domestic.first
      return @dynamic_price.scale.to_f if @dynamic_price.present?
    end
    1.0
  end

  def flash_deals_available?
    country_code = Design.country_code
    designid = is_a?(Variant) ? design_id : id
    Promotion.flash_deals_promotion?(country_code, designid)
  end


  def upcoming_flash_deals_available?
    country_code = Design.country_code
    designid = is_a?(Variant) ? design_id : id
    Promotion.flash_deals_upcoming_promotion?(country_code, designid)
  end
end