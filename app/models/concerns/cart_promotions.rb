module CartPromotions
  extend ActiveSupport::Concern

  #FREE_SHIPPING_AT, FREE_SHIPPING = Promotion.get_free_shipping

  # Shipping Offer - free_shipping_at available on which amount
  # if promotions is available
  #
  # == Returns:
  # Hash
  #
  def shipping_offers(rate, country=nil)
    offers = {}
    return offers if Design.country_code == 'IN'
    if Promotion.get_free_shipping(country).last
      if ((value = free_shipping_at_currency(rate, country) )> 0)
        offers[:free_shipping_at] = value
        offers[:available] = free_shipping_available?(rate, country)
        offers[:on_category] = Promotion.shipping_offer_on_category
      end
    end
    offers
  end

  def free_shipping_available?(rate, country)
    country_code = Country.get_country_code(country)
    item_total = self.items_total_without_addons(rate) - self.total_discounts_currency(rate)
    item_total += self.wallet_details(country_code)[:return_amount]
    item_total_exclude_shipping = self.exclude_shipping_item_total_without_addons(rate)
    item_total_exclude_shipping += self.wallet_details(country_code)[:return_amount]
    country_name = country || Address.shipping_country
    amount, flag = Promotion.get_free_shipping(country)
    available = false
    return available if Design.country_code == 'IN'
    if flag
      if (category_name = Promotion.shipping_offer_on_category).present?
        cat_line_items = []
        category_wise_total = 0
        self.line_items.each do |line_item|
          if (Category.getids(category_name) & line_item.design.categories.pluck(:id)).present?
            cat_line_items << line_item
          end
        end
        cat_line_items.each do |line_item|
          category_wise_total += line_item.total_currency_without_addons(rate)
        end
        if category_wise_total.present?
          available = true if category_wise_total >= free_shipping_at_currency(rate, country)
        end
      elsif shipping_categories_available? || cart_has_bmgn_products?
        return (item_total_exclude_shipping.round(0) >= free_shipping_at_currency(rate, country_name, self.line_items.count == 1 && self.line_items.first.quantity == 1)) && Promotion.free_shipping_on_country?(country_name)
      else
        return (item_total.round(0) >= free_shipping_at_currency(rate, country_name, self.line_items.count == 1 && self.line_items.first.quantity == 1)) && Promotion.free_shipping_on_country?(country_name)
      end
    end
    available
  end

  def mastercard_discount(country_code, rate)
    if country_code.to_s == 'IN'
      discount_percent = Promotion.mastercard_discount_percent.to_i
      if discount_percent > 0
        subtotal = self.item_total_with_discount(1, country_code, false, false).to_i
        discount = ((subtotal * discount_percent) / 100)
      end
    end
  end

  def free_stitching_on_country?(country_code = nil)
    country_code ||= Design.country_code
    if (country_code_list = Promotion.free_stitching_on_country(country_code)).present?
      country_code_list.split(',').include?(country_code)
    else
      false
    end
  end

  def free_stitching_eligible?(country_code = nil)
    country_code ||= Design.country_code
    items_total_without_addons(1) - self.bmgnx_discounts >= Promotion.free_stitching_at(country_code)
  end

  # call with recalculate = true if content in cart has changed due to modifications
  def free_stitching?(country_code = nil, recalculate: false)
    country_code ||= Design.country_code
    @free_stitching ||= {}
    @free_stitching[country_code] = if (!recalculate && @free_stitching.has_key?(country_code))
      @free_stitching[country_code]
    else
      free_stiching_coupon? || (free_stitching_on_country?(country_code) && free_stitching_eligible?(country_code))
    end
  end

  def free_stitching_text(country_code, conversion_rate, symbol=nil)
    if free_stitching_on_country?(country_code)
      if free_stitching_eligible?(country_code)
        return "You are eligible for free stitching"
      else
        free_stitching_rate = (Promotion.free_stitching_at(country_code).to_f/conversion_rate).round(2)
        return "Free Stitching on Item Total above #{symbol} #{free_stitching_rate} (*exclude Stitching Charges)"
      end
    else
      return false
    end
  end

  def qpm_disc_percent
    qpm = QuantityDiscountPromotion.current_active_promotions.select{|qpm| self.total_items >= qpm.required_quantity}.last
    qpm.present? ? qpm.discount_percent : 0
  end

  def next_qpm_msg
    qpm = QuantityDiscountPromotion.current_active_promotions.find{|qpm| qpm.required_quantity > self.total_items}
    qpm.present? ? qpm.cart_message(self.total_items) : nil
  end

  private

  def free_shipping_at_currency(rate, country, design = false)
    free_shipping_rate = Promotion.get_free_shipping(country, design).first
    (free_shipping_rate / rate).round(CurrencyConvert.round_to(default: 2))
  end
end
