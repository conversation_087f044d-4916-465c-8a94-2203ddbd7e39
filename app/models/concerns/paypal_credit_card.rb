module PaypalCreditCard
  extend ActiveSupport::Concern
  require 'paypal/express'

  paypal_data = YAML.load_file("#{Rails.root}/config/paypal.yml")

  Paypal.sandbox! unless Rails.env.production?

  PAYPAL_CONFIG =
    if Rails.env.production?
      {
        username:  paypal_data['production']['username'],
        password:  paypal_data['production']['password'],
        signature: paypal_data['production']['signature'],
        sandbox:  ENV.fetch('PAYPAL_PAYMENT_MODE')
      }
    else
      {
        username:  paypal_data['development']['username'],
        password:  paypal_data['development']['password'],
        signature: paypal_data['development']['signature'],
        sandbox:  ENV.fetch('PAYPAL_PAYMENT_MODE')
      }
    end

  def initialize_variables(rate, symbol, shipping_address)
    @order_currency_code = paypal_cc_currency_allowed? ? symbol : 'USD'
    currency =  CurrencyConvert.currency_convert_cache_by_country_code(self.country_code || 'US')
    CurrencyConvert.round_to = currency.round_to
    @currency_rate = @order_currency_code != symbol ? currency.rate : rate
    @shipping_country = shipping_address.country
  end

  def paypal_cc_currency_allowed?
    Order::PAYPAL_ALLOWED_CURRENCIES.include?(self.currency_code)
  end

  def setup!(return_url, cancel_url, rate, symbol, shipping_address)

    initialize_variables(rate, symbol, shipping_address)
    code = (country = Country.where(name: self.billing_country).first).try(:iso3166_alpha2)
    country_code = 'en_US' #code #'US' #['FR','DE'].include?(code) ? "US" : code
    response =
      if self.pay_type == Order::PAYMENT_GATEWAY
        state_code = case code
        when 'BR','CA','IT','MX', 'US'
          country.states.find_by_name(shipping_address.state).try(:iso3166_alpha2)
        when 'AR'
          shipping_address.state.to_s.mb_chars.upcase.to_s
        end || shipping_address.state
        client.setup(
          payment_request,
          return_url,
          cancel_url,
          locale: country_code,
          pay_on_paypal: true,
          solution_type: "sole",
          landing_page: "billing",
          email: self.billing_email,
          name: shipping_address.name,
          street: shipping_address.street_address,
          city: shipping_address.city,
          state: state_code,
          zip: shipping_address.pincode,
          country: code, #shipping_address.country,
          invoice_number: "#{self.paypal_retry_count}#{self.number}",
          phone: shipping_address.formatted_phone_number
        )
      elsif self.pay_type == Order::PAYPAL || self.pay_type == Order::PAYPAL_CREDIT
        client.setup(
          payment_request,
          return_url,
          cancel_url,
          paypal_credit: self.pay_type == Order::PAYPAL_CREDIT ? true : false,
          landing_page: "Login",
          pay_on_paypal: true,
          email: self.billing_email,
          invoice_number: "#{self.paypal_retry_count}#{self.number}",
          locale: country_code
        )
      end
    self.token = response.token
    self.save!
    @redirect_uri = response.redirect_uri
    @popup_uri = response.popup_uri
    response
  end

  def complete!(payer_id = nil)
    currency =  CurrencyConvert.currency_convert_memcached.find{|cc| cc.country_code == (paypal_cc_currency_allowed? ? self.country_code : 'US')}
    CurrencyConvert.round_to = currency.round_to
    return unless cart.present?
    begin
      response = client.checkout!(self.token, payer_id, payment_request)
      self.paypal_payer_id = payer_id
      self.paypal_txn_id = response.payment_info.first.transaction_id
      self.state ||= :new
      self.payment_state ||= :pending_payment
      self.save!
      response
    rescue Paypal::Exception::APIError => e
      e.response
    end
  end

  def paypal_mobile_cc_create_params
    return_url = "https://#{ENV['MIRRAW_DOMAIN']}/api/v1/user/orders/#{self.id}/paypal_success.html"
    cancel_url = "https://#{ENV['MIRRAW_DOMAIN']}/api/v1/user/orders/#{self.id}/paypal_cancel.html"
    country_code = (country = Country.where(name: self.billing_country).first).try(:iso3166_alpha2)
    state_code = case country_code
        when 'BR','CA','IT','MX', 'US'
          country.states.find_by_name(self.buyer_state).try(:iso3166_alpha2)
        when 'AR'
          self.buyer_state.to_s.mb_chars.upcase.to_s
        else
          self.buyer_state
        end

    set_currency_rounding

    response = client.setup(
          payment_request,
          return_url,
          cancel_url,
          pay_on_paypal: true,
          solution_type: "sole",
          landing_page: "billing",
          email: self.billing_email,
          locale: 'en_US',
          name: self.name,
          street: self.street,
          city: self.city,
          state: state_code,
          zip: self.pincode,
          country: country_code,
          invoice_number: "#{self.paypal_retry_count}#{self.number}",
          phone: self.billing_phone
        )
    self.paid_currency_code = self.paypal_currency_allowed? ? self.currency_code : 'USD'
    self.paid_currency_rate = get_paypal_rate
    self.token = response.token
    self.attempted_payment_gateway = 'paypal'
    self.save!
    response
  end


  private

  def client
    Paypal::Express::Request.new PAYPAL_CONFIG
  end

  def set_paypal_related_instances
    @currency_rate ||= self.currency_rate
    @shipping_country ||= self.country
    @order_currency_code ||= paypal_cc_currency_allowed? ? self.currency_code : 'USD'
  end

  def get_payment_attributes
    set_paypal_related_instances
    line_items_list = []
    line_items.reload
    ActiveRecord::Associations::Preloader.new.preload(line_items, [:line_item_addons, design: :categories])
  
    line_items.each do |line_item|
      line_items_list << item_hash(line_item)
    end
  
    line_items_list << subscription_hash if subscription_hash.present?
    line_items_list << platform_fee_hash if platform_fee_hash.present?
    line_items_list << order_addon_hash if order_addon_hash.present?
    line_items_list << discount_hash if discount_hash.present?
  
    shipping = self.shipping_currency(get_paypal_rate)
    shipping += express_delivery_currency(get_paypal_rate) if express_delivery?
    total = self.total_currency(get_paypal_rate)
    tax_amount = self.total_tax_currency(get_paypal_rate)
  
    line_items_list << handling_fee_hash(tax_amount) unless ['AU', 'SG'].include?(self.country_code)
  
    request_attributes = {
      amount: '%.2f' % total,
      description: 'Discounts',
      shipping_amount: '%.2f' % shipping,
      currency_code: @order_currency_code,
      items: line_items_list
    }
  
    request_attributes[:tax_amount] = '%.2f' % tax_amount if ['AU', 'SG'].include?(self.country_code)
  
    request_attributes
  end
  

  def payment_request
    Paypal::Payment::Request.new get_payment_attributes
  end


  def items
    item_list = {}
    line_items_list = []
    cart.line_items.each do|line_item|
      line_items_list << item_hash(line_item)
    end
    item_list[:items] = line_items_list
    line_items_list
    item_list
  end

  def item_hash(line_item)
    price = line_item.snapshot_price_currency(get_paypal_rate)
    line_item.line_item_addons.each do |li_addon|
      price += li_addon.snapshot_price_currency(get_paypal_rate)
    end
    {
      name: line_item.design.title,
      description: line_item.design.design_code,
      amount: '%.2f'%price,
      currency_code: @order_currency_code,
      quantity: line_item.quantity
    }
  end

  def discount_hash
    # During sending data to payment gateway use order values do not use cart values. This is for discount do the same changes for all fields
    if (discount = total_discount_currency(get_paypal_rate)) > 0
      { name: 'Discount',
        amount: sprintf("%.2f", -discount),
        description: 'discount',
        quantity: 1 }
    end
  end

  def subscription_hash
    if self.subscription_fee > 0
      subscription_plan = self.get_subscription_plan
      { name: "#{subscription_plan[:sub_title]}",
        currency_code: @order_currency_code,
        amount: (self.subscription_fee_currency(get_paypal_rate)),
        description: subscription_plan[:membership_description],
        quantity: 1 }
    end
  end

  def platform_fee_hash
    if self.platform_fee > 0
      { name: 'Platform Fee',
        amount: (self.platform_fee_currency(get_paypal_rate)),
        currency_code: @order_currency_code,
        description: 'platform_fee',
        quantity: 1 }
    end
  end

  def handling_fee_hash(tax_amount)
    if (tax_amount = self.total_tax_currency(get_paypal_rate)) > 0
      { name: 'Handling Charges',
        amount: tax_amount,
        currency_code: @order_currency_code,
        description: 'handling_fee',
        quantity: 1 }
    end
  end

  def order_addon_hash
    if self.order_addon.present?
      { name: 'Gift Wrap Charges',
        amount: (self.order_addon.gift_wrap_price_currency(get_paypal_rate)),
        currency_code: @order_currency_code,
        quantity: 1 }
    end
  end

  def totals
    self.total_currency(get_paypal_rate)
    # total = cart.total_currency(get_paypal_rate, self.actual_country_code, @shipping_country, !paypal_cc_currency_allowed?)
    # --------------------adding shipping cost in total already-------------------------
    # shipping_charges = cart.shipping_cost_currency(@shipping_country, get_paypal_rate)
    # if self.order_addon.present?
    #   total += (self.order_addon.gift_wrap_price_currency(get_paypal_rate))
    # end
    # total += express_delivery_currency(get_paypal_rate) if express_delivery?
    # grandtotal =  total# + shipping_charges
    # grandtotal
  end

  #This method provide exact rate for coversion using rate and paypal rate
  def get_paypal_rate
    @currency_record ||=  CurrencyConvert.currency_convert_cache_by_country_code(self.country_code)
    rate = @currency_record.rate
    paypal_rate = @currency_record.try(:paypal_rate)
    paypal_rate.nil? ? @currency_rate : (rate / paypal_rate)
  end
end
