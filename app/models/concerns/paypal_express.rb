module PaypalExpress
  extend ActiveSupport::Concern
  require 'paypal-sdk-rest'

  def paypal_setup
    PayPal::SDK::REST.set_config(
      mode: ENV.fetch('PAYPAL_PAYMENT_MODE'), # "sandbox" or "live"
      client_id: ENV.fetch('PAYPAL_CLIENT_ID'),
      client_secret: ENV.fetch('PAYPAL_CLIENT_SECRET'))
  end

  def initialize_variables(shipping_address, rate, symbol, country_code)
    @order_currency_code = paypal_currency_allowed?(symbol) ? symbol : 'USD'
    currency =  CurrencyConvert.currency_convert_cache_by_country_code('US')
    CurrencyConvert.round_to = currency.round_to
    @country_code = country_code
    @currency_rate = @order_currency_code != symbol ? currency.rate : rate
    @shipping_country_code = Country.get_country_code(shipping_address.country)
  end

  # Check whether order currency is allowed in paypal
  #
  # == Returns:
  # Boolean
  #
  def paypal_currency_allowed?(symbol)
    Order::PAYPAL_ALLOWED_CURRENCIES.include?(symbol)
  end

  def create_paypal_payment(cart, shipping_address, rate, symbol, country_code, return_url, cancel_url)
    paypal_setup
    initialize_variables(shipping_address, rate, symbol, country_code)
    @shipping_address = shipping_address
    @cart = cart

    shipping_address = {
      line1: @shipping_address.street_address,
      line2: @shipping_address.landmark,
      city: @shipping_address.city,
      state: @shipping_address.state,
      postal_code: @shipping_address.pincode,
      country_code: @shipping_country_code,
      phone: @shipping_address.phone,
      recipient_name: @shipping_address.name
    }

    payment = PayPal::SDK::REST::Payment.new({
      intent:  "sale",
      payer: {
        payment_method: "paypal"
      },
      redirect_urls: {
        return_url: return_url,
        cancel_url: cancel_url
      },
      transactions: line_item_details_hash
    })
    if payment.create
      # Redirect the user to given approval url
      redirect_url = payment.links.find{|v| v.method == "REDIRECT" }.href
      logger.info "Payment[#{payment.id}]"
      logger.info "Redirect: #{redirect_url}"
      patch = [{
        op: "add",
        path: "/transactions/0/item_list/shipping_address",
        value: shipping_address
      }]

      if payment.update( patch )
        logger.info "Payment[#{payment.id}]"
        logger.info "Redirect Shippment: #{redirect_url}"
      else
        logger.error payment.error.inspect
      end

      return redirect_url
    else
      logger.error payment.error.inspect
      return cancel_url
    end
  end

  def line_item_details_hash
    response_hash = {}
    response_hash[:item_list] = items
    response_hash[:amount] = totals
    response_hash[:description] = "This is the payment transaction description."
    [response_hash]
  end

  def items
    item_list = {}
    line_items_list = []
    @cart.line_items.each do|line_item|
      line_items_list << item_hash(line_item)
    end
    item_list[:items] = line_items_list
    item_list[:items] << discount_hash
    item_list
  end

  def item_hash(line_item)
    price = line_item.snapshot_price_currency(@currency_rate)
    line_item.line_item_addons.each do |li_addon|
      price += li_addon.snapshot_price_currency(@currency_rate)
    end
    {
      name: line_item.design.title,
      sku: line_item.design.design_code,
      price: '%.2f'%price,
      currency: @order_currency_code,
      quantity: line_item.quantity.to_s
    }
  end

  def discount_hash
    if (discount = @cart.total_discounts_currency(@currency_rate)) > 0
      { name: 'Discount',
        price: sprintf("%.2f", -discount),
        currency: @order_currency_code,
        quantity: 1 }
    end
  end

  def totals
    total = @cart.total_currency(@currency_rate)
    shipping_charges = @cart.shipping_cost_currency(@shipping_address.country, @currency_rate)
    discounts = @cart.wallet_referral_amount(@country_code)
    grandtotal =  total + shipping_charges - discounts
    tax_percent, tax_amount, grandtotal, tax_enable = @cart.get_total_with_tax(@country_code, grandtotal)
    {
      total: '%.2f'%grandtotal,
      currency: @order_currency_code,
      details: {
        subtotal: '%.2f'%total,
        shipping: '%.2f'%shipping_charges.to_f,
        tax: '%.2f'%tax_amount,
        shipping_discount: '%.2f'%discounts.to_f
      }
    }
  end

  def get_payment_details(params)
    response_hash = {error: false, error_message: '', data: nil}
    begin
      # Retrieve the payment object by calling the
      # `find` method
      # on the Payment class by passing Payment ID
      payment = PayPal::SDK::REST::Payment.find(params['paymentId'])
      logger.info "Got Payment Details for Payment[#{payment.id}]"
      response_hash[:data] = payment
    rescue
      # It will throw ResourceNotFound exception if the payment not found
      logger.error "Payment Not Found"
      response_hash[:error] = true
      response_hash[:error_message] = 'Payment Not Found'
    end
    return response_hash
  end

  def execute_payment_details(params)
    paypal_setup
    response_hash = {error: false, error_message: '', data: nil}
    payment_id = params['paymentId']
    payment = PayPal::SDK::REST::Payment.find(payment_id)

    # PayerID is required to approve the payment.
    if payment.execute( :payer_id => params['PayerID'] )  # return true or false
      logger.info "Payment[#{payment.id}] execute successfully"
      response_hash[:data] = payment
    else
      logger.error payment.error.inspect

      response_hash[:error] = true
      response_hash[:error_message] = payment.error.to_s
    end
    return response_hash
  end
end
