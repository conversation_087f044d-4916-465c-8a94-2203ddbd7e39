module CouponValidation
  extend ActiveSupport::Concern

  # checks validity of coupon on cart/ designer_order/ order
  #
  def coupon_validity
    device_id = self.class == Cart ? self.device_id : (self.class == Order ? self.cart.try(:device_id) : nil)
    if CART_COUPON_CONSTANT['Cart_coupon_Enable'] && coupon.code == Coupon.find_by_name('user_cart_coupon').try(:code) && device_id && (api_acc = ApiData.find_by_device_id(device_id)).present?
      if !api_acc.last_cart_coupon_shown || ((Time.now - api_acc.last_cart_coupon_shown)/60).to_i > (CART_COUPON_CONSTANT['Cart_coupon_valid_Minutes'] - 1)
        self.errors.add(:coupon, 'not applicable')
      end
    elsif !coupon_validate_on_cart? || !coupon_validate_with_limit?
      self.errors.add(:coupon, 'inactive or already used')
    elsif (self.coupon.min_amount.to_f > 0) && validate_with_min_amount 
      self.errors.add(:coupon, "minimum amount not satisfied.")
    else
      check_cart_specific_coupon_validity
    end
  end

  def check_cart_specific_coupon_validity
    if self.instance_of?(Cart)
      message = ''
      if validate_with_min_amount_for_stitoff_coupon && coupon_validate_on_stitching_addon?
        message = "minimum amount not satisfied."
      elsif ["STITOFF"].include?(coupon.coupon_type)
        if addons_excluded_from_stitoff_coupon
          message = "code is only applicable on stitching addon."
        elsif !coupon_validate_on_stitching_addon?
          message = "code is only available for stitching products."
        end
      elsif self.discount <= 0 && ["STITOFF", "SHIPOFF"].exclude?(coupon.coupon_type)
        message = "not applicable"
      elsif coupon_has_exclusions? && !has_eligible_items_for_coupon?
        message = "not applicable on this cart"
      end
      self.errors.add(:coupon, message) if message.present?
    end
  end
  
  #some addon type values are excluded for offers and that is the reason they are not applicable for stitoff coupons discount.
  #refer line_item_addon.rb
  def addons_excluded_from_stitoff_coupon
    line_item_ids = self.line_items.pluck(:id)
    addon_ids_in_cart = LineItemAddon.where(line_item_id: line_item_ids).pluck(:addon_type_value_id).map(&:to_s)

    remaining_addons_in_cart = addon_ids_in_cart - PROMOTION_EXCLUDED_ADDON_IDS
    default_addons_count = addon_ids_in_cart.count { |id| DEFAULT_ADDONS.include?(id) }

    has_excluded_addon = addon_ids_in_cart.any? { |id| PROMOTION_EXCLUDED_ADDON_IDS.include?(id) }
    all_excluded_addons_present = PROMOTION_EXCLUDED_ADDON_IDS.all? { |id| addon_ids_in_cart.include?(id) }
    only_default_addons = remaining_addons_in_cart.all? { |id| DEFAULT_ADDONS.include?(id) }
    
    has_excluded_addon && only_default_addons || all_excluded_addons_present && default_addons_count >= 2  
  end
  
  def validate_with_min_amount
    [Cart].include?(self.class) ? (self.item_total(1) < self.coupon.min_amount.to_f) : false
  end

  def validate_with_min_amount_for_stitoff_coupon
    [Cart].include?(self.class) ?  self.items_total_without_addons(1) < self.coupon.min_amount.to_f : false
  end

  def coupon_validate_on_cart?
    self.class == Cart ? self.coupon.active? : true
  end

  def coupon_validate_with_limit?
    [Order, DesignerOrder].include?(self.class) ? self.coupon.usable? : true
  end

  def coupon_validate_on_stitching_addon?
    self.line_items.any? { |line_item| line_item.stitching_addons?}
  end

  # Check if coupon has exclusion rules
  def coupon_has_exclusions?
    coupon.exclude_tier1_designers? || coupon.exclude_premium_designs?
  end

  # Check if cart has eligible items for coupon with exclusion rules
  def has_eligible_items_for_coupon?
    return true unless coupon_has_exclusions?

    self.line_items.any? { |line_item| coupon.item_eligible_for_coupon?(line_item) }
  end

  def self.included base
    base.class_eval do
      validate :coupon_validity, if: "self.coupon.present?"
    end
  end
end
