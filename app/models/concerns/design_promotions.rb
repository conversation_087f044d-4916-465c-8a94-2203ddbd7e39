module DesignPromotions
  extend ActiveSupport::Concern

  # Calculate global_discount based on promotion's discount percent
  #
  # == Returns:
  # Float
  #
  def global_price_percent(active_promotions)
    country_code = Design.country_code
    RequestStore.cache_fetch("global_sale_discount_percent_#{country_code}") do
      discount = 0
      promotion_pipelines = active_promotions
      var_hash = promotion_pipelines.find_by_name("global_sale_#{country_code}").try(:variables_hash) || promotion_pipelines.find_by_name("global_sale").try(:variables_hash)
      var_hash.present? ? JSON.parse(var_hash)['global_discount_percent'].to_i : 0
    end
  end

  def category_wise_global_price_percent(active_promotions)
    country_code = Design.country_code
    RequestStore.cache_fetch("category_wise_discount_percent_#{country_code}") do
      discount = 0
      promotion_pipelines = active_promotions
      var_hash = promotion_pipelines.find_by_name("category_discount_sale_#{country_code}").try(:variables_hash) || promotion_pipelines.find_by_name("category_discount_sale").try(:variables_hash)
      var_hash.present? ? JSON.parse(var_hash)['global_discount_percent'].to_i : 0
    end
  end

  def designer_wise_global_price_percent(active_promotions,country_code=nil)
    RequestStore.cache_fetch("designer_wise_global_price_percent_mobile_#{country_code}".freeze, expires_in: 1.day) do
      var_hash = if country_code.present?
                    active_promotions.find_by_name("designer_discount_sale_#{country_code}").try(:variables_hash) || active_promotions.find_by_name("designer_discount_sale").try(:variables_hash)
                  else
                    active_promotions.find_by_name("designer_discount_sale").try(:variables_hash)
                  end
      var_hash.present? ? JSON.parse(var_hash)['global_discount_percent'].to_i : 0
    end
  end

  def flash_deals_price_percent(active_promotions)
    RequestStore.cache_fetch("flash_deals_price_percent_mobile") do
      discount = 0
      promotion_pipelines = active_promotions
      var_hash = promotion_pipelines.find_by_name("flash_deals_promotion").try(:variables_hash)
      var_hash.present? ? JSON.parse(var_hash)['global_discount_percent'].to_i : 0
    end
  end

  def global_discount_on_amount(active_promotions)
    country_code = Design.country_code
    RequestStore.cache_fetch("global_discount_on_amount_mobile_#{country_code}") do
      promotion_pipelines = active_promotions
      var_hash = if country_code.present?
                    promotion_pipelines.find_by_name("global_sale_#{country_code}").try(:variables_hash) || promotion_pipelines.find_by_name("global_sale").try(:variables_hash)
                  else
                    promotion_pipelines.find_by_name("global_sale").try(:variables_hash)
                  end
      var_hash.present? ? JSON.parse(var_hash)['amount_on'].to_i : 0
   end
  end

  # Calculate effective price based on global sale and discount_price
  #
  # == Returns:
  # Integer
  #
  def effective_price
    country_code ||= Design.country_code
    if direct_dollar_applicable?(country_code)
      return direct_dollar_promotion(country_code)
    end
    active_promotions = PromotionPipeLine.active_promotions
    (self.price * (100 - effective_discount(active_promotions)) / 100.0).to_i
  end

  def fd_effective_price
    active_promotions = PromotionPipeLine.active_promotions
    (self.price * (100 - fd_effective_discount(active_promotions)) / 100.0).to_i
  end

  def fd_effective_discount(active_promotions)
    discount_percent = effective_discount(active_promotions).to_f
    flash_discount_percent = if upcoming_flash_deals_available? && flash_deals_price_percent(active_promotions) > 0
      flash_deals_price_percent(active_promotions).to_f
    end
    Promotion.cumulative_discount_percent(discount_percent, flash_discount_percent).round
  end

  # Calculate effective discount based on global sale and existing discount
  #
  # == Returns:
  # Integer
  #
  def effective_discount(active_promotions)
    country_code ||= Design.country_code
    if direct_dollar_applicable?(country_code)
      return (100 - ((self.effective_price / self.price.to_f) * 100).round())
    end
    @effective_discount ||= {}
    @effective_discount[country_code] ||= begin
      discount_percent = self.discount_percent.to_f
      seller = self.is_a?(Variant) ? self.design.designer : self.designer
      global_discount_percent = if !seller.try(:is_one_tier_designer?) && discount_sale_on_amount?(active_promotions) && sale_on_country?(active_promotions, country_code)
        global_price_percent(active_promotions)
      end.to_f
      category_discount_percent = if category_discount_available?(active_promotions) && category_sale_on_country?(active_promotions, country_code)
        category_wise_global_price_percent(active_promotions)
      end.to_f
      designer_discount_percent = if designer_discount_available?(active_promotions, country_code)
        designer_wise_global_price_percent(active_promotions,country_code)
      end.to_f
      flash_discount_percent = if flash_deals_available? && flash_deals_price_percent(active_promotions) > 0
        flash_deals_price_percent(active_promotions).to_f
      end
      Promotion.cumulative_discount_percent(discount_percent, global_discount_percent, category_discount_percent, designer_discount_percent, flash_discount_percent).round
    end
  end

  def direct_dollar_promotion(country_code = Design.country_code)
    promotion_pipelines = PromotionPipeLine.active_promotions.app_source(User.app_source)
    p_id = self.design_promotion_pipe_lines.map(&:promotion_pipe_line_id).first
    var_hash = if p_id.present?
      frozen_key="direct_dollar_promotion_id_#{p_id}".freeze
      direct_dollar_promotion = RequestStore.fetch(frozen_key) do
      promotion_pipelines.find_by_id(p_id) || {}
      end
      direct_dollar_promotion.try(:variables_hash)
    end
    if var_hash.present?
      var_hash = JSON.parse(var_hash)
      return var_hash["#{country_code}"].to_i if var_hash.keys.include?(country_code)
      0
    else
      return 0
    end
  end

  def direct_dollar_applicable?(country_code = Design.country_code)
    (self.class == Variant || self.buy_get_free == 4) && direct_dollar_promotion(country_code).between?(1,self.price)
  end

  def sale_on_country?(active_promotions, country=nil)
    if (country_code = Promotion.sale_discount_on_country(active_promotions,country)).present?
      country_code.split(',').include?(Design.country_code)
    else
      false
    end
  end

  def discount_sale_on_amount?(active_promotions)
    self.discount_price >= global_discount_on_amount(active_promotions)
  end

  def category_sale_on_country?(active_promotions, country=nil)
    if (country_code = Promotion.category_sale_discount_on_country(active_promotions,country)).present?
      country_code.split(',').include?(Design.country_code)
    else
      false
    end
  end

  def category_discount_available?(active_promotions)
    if (category_name = Promotion.discount_offer_on_category(active_promotions)).present?
      Promotion.category_promotion_ids(active_promotions).present? &&
        (self.category_ids & Promotion.category_promotion_ids(active_promotions)).present?
    end
  end

  def designer_discount_available?(active_promotions, country_code=nil)
    country_code ||= Design.country_code
    designer_discount_promotion = Rails.cache.fetch("designer_discount_promotions_mobile_#{country_code}".freeze, expires_in: 1.day) do
      if country_code.present?
        active_promotions.find_by_name("designer_discount_sale_#{country_code}") || active_promotions.find_by_name("designer_discount_sale") || []
      else
        active_promotions.find_by_name("designer_discount_sale") || []
      end
    end
    country_code_list = designer_discount_promotion.try(:country_code) || ''
    var_hash = designer_discount_promotion.try(:variables_hash)
    designer_ids = var_hash.present? ? JSON.parse(var_hash)['designers_on'] : ''
    designerid = is_a?(Variant) ? design.designer_id : designer_id
    country_code_list.split(',').include?(country_code) && designer_ids.split(',').collect(&:to_i).include?(designerid)
  end
end
