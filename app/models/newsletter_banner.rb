class NewsletterBanner < ActiveRecord::Base
  has_attached_file :subscription_banner,
    styles: {
      main: "984x378",
      mobile_main: "780x300",
      main_webp: ["984x378", :webp],
      mobile_main_webp: ["780x300", :webp]
    }
  scope :live , -> {where('start_date <= ? AND end_date >= ?', Time.zone.now, Time.zone.now)}
  scope :mirraw, -> { where(app_name: [nil,'']) }
  scope :luxe, -> { where(app_name: 'luxe') }

  def self.country(country)
    wildcard_search = "%#{country}%" if country.present?
    NewsletterBanner.where("country ILIKE :search OR country IS NULL OR country=''", search: wildcard_search)
  end

  def self.banner_hash(country_code)
    hash = {}
    if (banner = NewsletterBanner.where("app_source = 'mobile' OR app_source = 'universal'").mirraw.live.country(country_code).first).present?
      hash['image_url'] = banner.subscription_banner.url
      details = JSON.parse(banner.details) rescue {}
      hash.merge!(details)
    end
    hash['image_url'] ||= "#{IMAGE_PROTOCOL}assets0.mirraw.com/landing_images/13/pop_up_opt_original.jpg"
    hash['text'] ||= 'SUBSCRIBE'
    hash['color'] ||= '#FF8030'
    hash
  end
end
