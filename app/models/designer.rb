# == Schema Information
#
# Table name: designers
#
#  id                  :integer          not null, primary key
#  name                :string(255)
#  email               :string(255)
#  phone               :string(255)
#  alt_phone           :string(255)
#  city                :string(255)
#  state               :string(255)
#  street              :string(255)
#  country             :string(255)
#  pincode             :string(255)
#  description         :text
#  url                 :text
#  photo_file_name     :string(255)
#  photo_content_type  :string(255)
#  photo_file_size     :integer
#  photo_updated_at    :datetime
#  published           :boolean
#  policy              :text
#  transaction_rate    :integer
#  account_holder_name :string(255)
#  bank_name           :string(255)
#  branch              :string(255)
#  ifsc_code           :string(255)
#  account_number      :string(255)
#  grade               :integer
#  cached_slug         :string(255)
#  vacation_start_date :datetime
#  vacation_end_date   :datetime
#  vacation_message    :text
#  fb_page_name        :string(255)
#  fb_page_id          :string(255)
#  fb_page_token       :string(255)
#  fb_page_link        :string(255)
#  last_fb_post_at     :datetime
#  wholesaler          :boolean
#  banned              :boolean          default(FALSE)
#  state_machine       :string(255)
#  last_approved_on    :datetime
#  last_review_on      :datetime
#  last_banned_on      :datetime
#  seo_title           :string(255)
#  business_name       :string(255)
#  business_street     :string(255)
#  business_city       :string(255)
#  business_state      :string(255)
#  business_country    :string(255)
#  business_pincode    :string(255)
#  pan_no              :string(255)
#  tan_no              :string(255)
#  allow_addon         :boolean          default(FALSE)
#  state_code          :string(255)
#  mirraw_shipping     :boolean
#  cod                 :boolean
#  pickup_name         :string(255)
#  pickup_company_name :string(255)
#  pickup_phone        :string(255)
#  pickup_street       :string(255)
#  pickup_city         :string(255)
#  pickup_state_code   :string(255)
#  pickup_pincode      :string(255)
#  upload_panel        :boolean          default(TRUE)
#  photo_processing    :boolean
#  p_name              :string(255)
#

class Designer < ActiveRecord::Base
  extend FriendlyId
  has_many :designer_shippers
  has_many :designs
  has_many :designer_orders
  has_many :reviews
  has_many :adjustments
  has_many :limited_design_categories, -> { where("designs.state = 'in_stock'").select('distinct(categories.name), category_type').limit(3) }, class_name: 'Category', through: :designs, source: :categories
  has_one :account, as: :accountable
  has_many :designer_campaign_participations
  has_many :seller_campaigns, through: :designer_campaign_participations
  scope :changed_additional_discount, -> {where{(additional_discount_start_date.eq Time.zone.now.to_date) | (additional_discount_end_date.eq (Time.zone.now.to_date - 1.day))}}
  scope :active_additional_discount, -> {where{(additional_discount_percent.gt 0) & (additional_discount_start_date.lte Time.zone.now.to_date) & (additional_discount_end_date.gte Time.zone.now.to_date)} }
  scope :published, -> { where(state_machine: ['approved', 'review', 'vacation']) }
  scope :for_designer, ->(designer_id) { where(id: designer_id) }
  acts_as_followable
  # mirraw designer default ETA is assumed 2 days. we can increase that value with this constant
  SLA_CONFIG = {
    mirraw_designer_default_eta: 4, # mirraw designer default ETA is assumed 2 days. we can increase that value with this constant
    sla_critical_alert_days: 2,
    sla_offset: 10,
    vendor_default_dispatch_days: 5,
    non_stitching_preparation_days: 5,
    country_default_delivery_days: 5
  }.merge!(JSON.parse(SystemConstant.get('SLA_CONFIG')).symbolize_keys).freeze

  friendly_id :slug_candidates, use: [:slugged, :finders, :history], slug_column: :cached_slug, sequence_separator: '--'

  def slug_candidates
    [
      :name,
      [:name, :id],
    ]
  end

  include Imageable

  # Check whether designer is on vacation
  #
  # == Returns:
  # Boolean
  #
  def vacation?
    self.state_machine == 'vacation'
  end

  def address
   self.street.to_s + ", " + self.city.to_s + " - " + self.pincode.to_s + ", " + self.state.to_s + ", " + self.country.to_s
  end

  def pickup_location
    self.pickup_city.presence || self.city.presence || self.business_city
  end

  def vacation_mode_on?
    vacation_start_date && vacation_end_date && (Date.today >= vacation_start_date.to_date) && (Date.today <= vacation_end_date.to_date)
  end

  def vacation_days_count
    vacation_mode_on? ? (vacation_end_date.to_date - vacation_start_date.to_date).to_i : 0
  end

  def self.get_id_by_name(name)
    RequestStore.cache_fetch("designer_get_id_by_name_#{name}",expires_in: 24.hour) do
      unscoped.where("lower(name) = ?",name.downcase).pluck(:id).first.to_i
    end
  end

  def self.find_by_cached_slug(slug)
    find slug
  rescue ActiveRecord::RecordNotFound => e
    nil
  end

  def is_transfer_model?
    self.transfer_model_rate.to_i > 0
  end

  def is_gst_excluded?
    self.exclude_gst
  end

  def is_variable_gst_model?
    !self[:domestic_transaction_rate].to_f.zero?
  end

  def is_one_tier_designer?
    self.designer_type == "Tier 1 Designer"
  end

  def domestic_transaction_rate
    if is_gst_excluded? && super.to_f.nonzero?
      return super
    else
      return self.transaction_rate
    end
  end

  def transaction_rate
    if is_transfer_model?
      return 0
    elsif is_gst_excluded? && !is_variable_gst_model?
      return (self[:transaction_rate] + self[:transaction_rate] * IGST/100.0)
    else
      return super
    end
  end

  # Allow shippers according to region
  # == Parameters:
  #  region::
  #    String
  #
  # == Returns:
  #  Array
  #
  def allowed_shipper_ids(region)
    w_enabled = {designer_shippers: {enabled: true}, shippers: {enabled: true}}
    if region == 'international'
      w_region = {shippers: {international: true}, designer_shippers: {international: true}}
    else
      w_region = {shippers: {domestic: true}, designer_shippers: {cod: true}}
    end
    shipper_ids = self.designer_shippers.joins(:shipper).where(w_enabled).where(w_region).pluck(:shipper_id)
  end

  # Check designer has cod based on pincode
  # == Parameters:
  #  pincode::
  #    String
  #
  # == Returns:
  #  Boolean
  #
  def can_cod?(pincode)
    cod = false
    if self.cod && (pincode.length == 6) && (shipper_ids = self.allowed_shipper_ids('domestic')).present?
      cod = true if Courier.check_cod?(shipper_ids, self.pincode, pincode, true)
    end
    cod
  end

  def vendor_additional_discount_percent
    if self[:additional_discount_percent].present? && self[:additional_discount_start_date].present? && self[:additional_discount_end_date].present? && !self.is_transfer_model?
      today = Time.zone.now.to_date
      if self[:additional_discount_start_date] <= today && self[:additional_discount_end_date] >= today
        return self[:additional_discount_percent]
      end
    end
    0
  end

  # Update the designer average review
  #
  def update_avg_ratings
    self.update_column('average_rating', self.reviews.approved.nps.average('rating').round(1))
  end

  # Build image url
  #
  # == Returns:
  # String
  #
  def photo(style = 'original')
    "#{photo_file_base_url}#{id}/#{image_file('photo', style)}"
  end

  def self.get_designers(key, value)
    if key == 'list'
      designer_ids = value.split(',')
      Designer.published.where(id: designer_ids).order("position(id::text in '#{value}')")
    else
      get_sorted_list(value)
    end
  end

  def self.get_sorted_list(value)
    if value == 'created_at'
      Designer.includes(:account).published.order('accounts.created_at DESC')
    elsif Designer.column_names.include?(value)
      Designer.published.order(value => :desc)
    else
      Designer.published.order('average_rating IS NULL, average_rating DESC')
    end
  end

  def self.grouped_by_first_char
    first_char_query = Arel.sql("UPPER(REGEXP_REPLACE(name, '^[^a-zA-Z]*([a-zA-Z]).*$', '\\1'))")
    RequestStore.cache_fetch("designers_list", expires_in: 24.hours) do
      Designer
        .select("#{first_char_query} AS first_char, ARRAY_AGG(id) AS ids, ARRAY_AGG(name) AS names, ARRAY_AGG(cached_slug) AS cached_slugs")
        .where(state_machine: ['approved', 'review'])
        .where.not(name: nil)
        .where(banned:[nil,false])
        .group("#{first_char_query}")
        .order("first_char ASC")
        .each_with_object({}) do |designer, hash|
          hash[designer.first_char] = {
            ids: designer.ids,
            names: designer.names,
            cached_slugs: designer.cached_slugs
          }
        end
    end
  end

  def review_text
    Rails.cache.fetch("designer_#{id}_positive_review_count", expires_in: 1.day) do
      positive_review_count = reviews.nps.approved.select('count(case when rating > 4 then id end) as positive, count(id) as total')[0]
      positive_review_count && positive_review_count.total.to_i > 0 ? "#{(positive_review_count.positive.to_f/positive_review_count.total.to_i*100).to_i}% Positive reviews(out of #{positive_review_count.total.to_i})" : 'No reviews yet'
    end
  end

  def eta
    super.to_i <= 0 ? SLA_CONFIG[:mirraw_designer_default_eta] : super.to_f
  end

  def ship_time
    super.to_f == 0 ? SLA_CONFIG[:vendor_default_dispatch_days] : super.to_f
  end

end
