class GradingTag < ActiveRecord::Base
  has_many :design_grading_tags
  has_many :designs, through: :design_grading_tags
  belongs_to :grading_taggable, polymorphic: true
  before_create :set_name
  validates :name, uniqueness: true
  after_commit :update_grading_cache

  def set_name
    self.name ||= "#{app_name.downcase}_#{platform.downcase}_#{country_code.downcase}_#{grading_taggable_type}_#{grading_taggable_id}"
  end

  # Class method to fetch all tags with their counts
  def self.get_all_tags
    Rails.cache.fetch("grading_tag_with_count", expires_in: 30.minutes) do
      GradingTag.pluck(:name, :grading_tag_product_counter).to_h
    end
  end

  # Instance method to check if a grading tag is available
  def self.is_grading_tag_available?(tag_name)
    get_all_tags[tag_name].to_i > 0
  end

  # Clear the cache and refresh it after any update
  def update_grading_cache
    GradingTag.refresh_grading_cache
  end

  def self.refresh_grading_cache
    Rails.cache.delete("grading_tag_with_count")
    GradingTag.get_all_tags
  end

  def self.get_grading_tags_for_grade_name(id, type, country_code, app_source = 'Mobile', app_name = 'Mirraw')
    app_source = 'mobile' if app_source.downcase.eql?('desktop')
    tag_name = "#{app_name.downcase}_#{app_source.downcase}_#{country_code.downcase}_#{type}_#{id}"
    all_platform_tag_name = "#{app_name.downcase}_all_#{country_code.downcase}_#{type}_#{id}"
    all_country_tag_name = "#{app_name.downcase}_#{app_source.downcase}_all_#{type}_#{id}"
    all_platform_all_country_tag_name = "#{app_name.downcase}_all_all_#{type}_#{id}"
    if is_grading_tag_available?(tag_name)
      return "graderank_#{tag_name}"
    elsif is_grading_tag_available?(all_platform_tag_name)
      return "graderank_#{all_platform_tag_name}"
    elsif is_grading_tag_available?(all_country_tag_name)
      return "graderank_#{all_country_tag_name}"
    elsif is_grading_tag_available?(all_platform_all_country_tag_name)
      return "graderank_#{all_platform_all_country_tag_name}"
    else
      grading_tag = GradingTag.find_by(name: tag_name) || GradingTag.find_by(name: all_platform_tag_name) || GradingTag.find_by(name: all_country_tag_name) || GradingTag.find_by(name: all_platform_all_country_tag_name)
    end
    return nil if grading_tag.nil?
    if grading_tag && grading_tag.grading_tag_product_counter > 0
      # Refresh the grading cache and return the tag name
      GradingTag.refresh_grading_cache
      return "graderank_#{grading_tag.name}"
    end
  end

  def grading_taggable
    #adding this as acts_on_taggable giving model creation issue
    case grading_taggable_type
    when 'Catalogue'
      ActsAsTaggableOn::Tag.find(grading_taggable_id)
    when 'Collection'
      ActsAsTaggableOn::Tag.find(grading_taggable_id)
    else
      super
    end
  end
end
  