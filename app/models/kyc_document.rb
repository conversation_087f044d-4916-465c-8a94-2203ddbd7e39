class KycDocument < ActiveRecord::Base
  belongs_to :user
  has_attached_file :document
  validates_inclusion_of :name, in: KYC_DOCUMENT_TYPES, message: "is not a valid document type"
  validates :user, uniqueness: { scope: :name, message: "has already uploaded this document type" }
  validates_attachment_size :document, less_than: 5.megabytes, message: "file size should be less than 5 MB"
  validates_attachment_content_type :document, content_type: ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png']
end
  