class SubscriptionPlan < ActiveRecord::Base
  belongs_to :country
  has_many :user_subscriptions, dependent: :destroy
  has_many :users, through: :user_subscriptions
  has_many :subscription_plan_services, dependent: :destroy
  has_many :subscription_services, through: :subscription_plan_services

  validates :name, presence: true
  validates :price, presence: true, numericality: { greater_than: 0 }
  validates :country_id, presence: true
  validates :plan_duration, presence: true, inclusion: { in: ['daily', 'weekly', 'monthly', '3_month', '6_month', 'annually'] }

  state_machine :status, initial: :active do
    state :active
    state :inactive

    event :deactivate do
      transition :active => :inactive
    end

    event :reactivate do
      transition :inactive => :active
    end
  end

  def self.for_country(country_code, app_source, amount)
    app_source = app_source.downcase.strip
    plans_by_country = Rails.cache.fetch("all_subscription_plans_#{app_source}", expires_in: 6.hours) do
      SubscriptionPlan.where(status: 'active')
                      .where("(',' || LOWER(REPLACE(app_source, ' ', '')) || ',') LIKE ? 
                              OR (',' || LOWER(REPLACE(app_source, ' ', '')) || ',') LIKE ?", 
                             "%,#{app_source},%", 
                             "%,all,%")
                      .includes(:country)
                      .group_by { |plan| plan.country.iso3166_alpha2.upcase }
    end
  
    plans = plans_by_country[country_code] || []
    plans.find do |plan|
      amount >= plan.minimum_order_value && amount <= plan.maximum_order_value
    end
  end

  def get_plan_date_in_days
    case plan_duration
    when 'daily'
      1
    when 'weekly'
      7
    when 'monthly'
      (Date.today.advance(months: 1) - Date.today).to_i
    when '3_month'
      (Date.today.advance(months: 3) - Date.today).to_i
    when '6_month'
      (Date.today.advance(months: 6) - Date.today).to_i
    when 'annually'
      (Date.today.advance(years: 1) - Date.today).to_i
    else
      0
    end
  end

  def get_plan_expiration_date
    case plan_duration
    when 'daily'
      Date.today + 1.day
    when 'weekly'
      Date.today + 1.week
    when 'monthly'
      Date.today + 1.month
    when '3_month'
      Date.today + 3.months
    when '6_month'
      Date.today + 6.months
    when 'annually'
      Date.today + 1.year
    else
      nil
    end
  end
  
  def sub_title
    "Membership Fee - (" + name + " - " + get_plan_date_in_days.to_s + " days)"
  end

  def membership_description
    "Your #{name} Membership has waived off the platform fee. Valid till #{get_plan_expiration_date.strftime('%d %b %Y')}"
  end

  def self.for_id(id)
    Rails.cache.fetch("subscription_plan_#{id}", expires_in: 6.hours) do
      s = SubscriptionPlan.find_by_id(id)
      s = s.try(:attributes).merge({membership_description: s.membership_description, sub_title:  "Membership Fee - (" + s.name + " - " + s.get_plan_date_in_days.to_s + " days)"})
    end
  end
end