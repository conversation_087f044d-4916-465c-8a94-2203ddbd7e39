class Block < ActiveRecord::Base
  include Imageable

  belongs_to :board
  belongs_to :landing

  scope :graded, -> { order(:grade) }
  scope :mirraw, -> { where(app_name: [nil,'']) }
  scope :luxe, -> { where(app_name: 'luxe') }

  IMAGE_STYLES = [:main]

  def banner(style)
    "#{self.image_file_base_url}#{self.class.name.tableize}/"\
    "#{id}/#{image_file('banner', style)}"
  end

  def self.country(country)
    wildcard_search = "%#{country}%" if country.present?
    Block.where("country ILIKE :search OR country IS NULL OR country=''", search: wildcard_search)
  end

  def self.app_source(app_source)
    Block.where("app_source ILIKE ? OR app_source IS NULL OR app_source=''", "%#{app_source}%")
  end
end
