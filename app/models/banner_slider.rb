# == Schema Information
#
# Table name: banner_sliders
#
#  id                 :integer          not null, primary key
#  name               :string(255)
#  link               :text
#  grade              :integer
#  description        :text
#  start_date         :datetime
#  end_date           :datetime
#  created_at         :datetime         not null
#  updated_at         :datetime         not null
#  photo_file_name    :string(255)
#  photo_content_type :string(255)
#  photo_file_size    :integer
#  photo_updated_at   :datetime
#  photo_processing   :boolean
#

class BannerSlider < ActiveRecord::Base

  has_attached_file :photo, :styles => {
    main: "1170x407",
    main_m: "1150x400",
    main_webp: ["1170x407", :webp],
    main_m_webp: ["1150x400", :webp],
  }
  scope :graded, -> { order(:grade) }
  scope :mirraw, -> { where(app_name: nil) }
  scope :luxe, -> { where(app_name: 'luxe') }

  scope :mirraw, -> { where(app_name: [nil,'']) }
  scope :luxe, -> { where(app_name: 'luxe') }

  IMAGE_STYLES = [:main, :main_m, :main_webp, :main_m_webp] # just for api compatibility

  # Build array of active records for banner_slider based on date
  #
  # == Returns:
  # Array of active records
  #
  def self.live
    BannerSlider.where('start_date <= ?', Time.now).where('end_date >= ?', Time.now)
  end

  def self.with_link_value
    BannerSlider.where.not(link_value: '', link_type: nil)
  end

  def self.country(country)
    wildcard_search = "%#{country}%" if country.present?
    BannerSlider.where("country ILIKE :search OR country IS NULL OR country=''", search: wildcard_search)
  end

  def self.app_source(app_source)
    BannerSlider.where("app_source ILIKE ? OR app_source ILIKE 'universal' OR app_source IS NULL OR app_source=''", "%#{app_source}%")
  end

  def self.un_apped
    BannerSlider.where.not(link_type: 'app')
  end

  def self.app_source_with_version(app_source, app_version)
    if app_source.downcase == 'android'
      if ALLOWED_APP_VERSIONS[39..-1].include?(app_version)
        BannerSlider.where("app_source ILIKE ? OR app_source ILIKE 'universal' OR app_source IS NULL OR app_source=''", "%#{app_source}%")
      elsif ALLOWED_APP_VERSIONS[28..-1].include?(app_version)
        BannerSlider.where("app_source ILIKE ? OR app_source ILIKE 'universal' OR app_source IS NULL OR app_source=''", "%#{app_source}%").where.not(link_type: 'webview')
      end
    elsif app_source.downcase == 'ios'
      BannerSlider.where("app_source ILIKE ? OR app_source ILIKE 'universal'", "%#{app_source}%")
    else
      BannerSlider.where("app_source ILIKE ? OR app_source ILIKE 'universal' OR app_source IS NULL OR app_source=''", "%#{app_source}%")
                  .where.not(link_value: 'offers')
                  .where.not(link_type: 'webview')
    end
  end

end
