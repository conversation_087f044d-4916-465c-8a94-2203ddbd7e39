# == Schema Information
#
# Table name: promotion_pipe_lines
#
#  id             :integer          not null, primary key
#  start_date     :datetime         not null
#  end_date       :datetime         not null
#  event_name     :string(255)      not null
#  user_id        :integer
#  variables_hash :text             default("{}"), not null
#  images_hash    :text             default("{}"), not null
#  methods_hash   :text             default("{}"), not null
#  created_at     :datetime         not null
#  updated_at     :datetime         not null
#  name           :string(255)
#

class PromotionPipeLine < ActiveRecord::Base
  scope :active_promotions, ->{ where{(start_date.lt Time.zone.now.change(sec: 0)) & (end_date.gt Time.zone.now.change(sec: 0))} }
  scope :changed_promotion, ->{where{(date(start_date).eq  (Time.zone.now.to_date + 1.day)) | (date(end_date).eq  (Time.zone.now.to_date - 1.day) )}}
  scope :app_source, ->(app_source) { where { "app_source ILIKE '%#{app_source.to_s.split(',')[0]}%' OR app_source IS NULL OR app_source=''" } }
  # scope :trackable, -> {where { "name ILIKE '%bmgnx%'" } }

  def self.for_country(country_code)
    where("promotion_pipe_lines.country_code = '' OR promotion_pipe_lines.country_code IS NULL OR promotion_pipe_lines.country_code ILIKE ?","%#{country_code}%")
  end

  def self.get_active_promotion(country_code)
    RequestStore.cache_fetch("deal_end_mo_global_category_#{country_code}_#{User.app_source}", :expires_in => 24.hours) {
      PromotionPipeLine.active_promotions.app_source(User.app_source).where("name SIMILAR TO ?",'%(global_sale|category_discount_sale|bmgnx)%').where("country_code = '' OR country_code IS NULL OR country_code ILIKE ?","%#{country_code}%").order(:end_date).first || []
    }
  end

  def self.bmgnx_hash
    country_code = Design.country_code
    RequestStore.cache_fetch("bmgnx_hash_#{country_code}") do
      if (promo = PromotionPipeLine.active_promotions.where("name ILIKE ?",'%bmgnx%').where("country_code = '' OR country_code IS NULL OR country_code ILIKE ?","%#{country_code}%").first).present?
        bmgnx_var_hash = JSON.parse(promo.variables_hash, symbolize_names: true).inject({}) { |result,(key,value)|
          if key == :scale
            result[key.to_sym] = value.to_f
          else
            result[key.to_sym] = value.to_i
          end
          result
        }
        if [bmgnx_var_hash[:m], bmgnx_var_hash[:n], bmgnx_var_hash[:x]].all?{|val|  val.present? && val.is_a?(Numeric) && val > 0}
          bmgnx_var_hash.merge({start_date: promo.start_date, end_date: promo.end_date})
        else
          {}
        end
      else
        {}
      end
    end
  end

  def self.current_bmgn_offer
    bmgn_config = self.bmgnx_hash
    return if bmgn_config.blank?
    "B#{bmgn_config[:m]}G#{bmgn_config[:n]}"
  end

  def self.global_sale_discount_promotion
    @global_sale_promotions ||= {}
    @global_sale_promotions[Design.country_code] ||= promise do
      promotion_pipelines = PromotionPipeLine.active_promotions
        if (country_code = Design.country_code).present?
          promotion_pipelines.find_by_name("global_sale_#{country_code}") || promotion_pipelines.find_by_name("global_sale")
        else
          promotion_pipelines.find_by_name('global_sale')
        end
      end
  end

  def self.category_wise_discount_promotion
    promotion_pipelines = PromotionPipeLine.active_promotions
    if Promotion.discount_offer_on_category(promotion_pipelines).present?
      if (country_code = Design.country_code).present?
        promotion_pipelines.find_by_name("category_discount_sale_#{country_code}") || promotion_pipelines.find_by_name("category_discount_sale")
      else
        promotion_pipelines.find_by_name('category_discount_sale')
      end
    end
  end

  def country_code?(cc)
    country_code.split(',').include?(cc)
  end

  def parsed_variables_hash
    @parsed_variables_hash ||= JSON.parse(variables_hash) rescue {}
  end
  
end
