# == Schema Information
#
# Table name: categories
#
#  id                 :integer          not null, primary key
#  name               :string(255)
#  parent_id          :integer
#  lft                :integer
#  rgt                :integer
#  created_at         :datetime
#  updated_at         :datetime
#  title              :text
#  description        :text
#  photo_file_name    :string(255)
#  photo_content_type :string(255)
#  photo_file_size    :integer
#  photo_updated_at   :datetime
#  url                :text
#  order              :integer
#  size_required      :string(255)
#  weight             :integer
#  international      :string(255)
#  is_master          :boolean
#  hide               :boolean
#  invoice_name       :string(255)
#  invoice_price      :integer
#  p_name             :string(255)
#

class Category < ActiveRecord::Base
  acts_as_nested_set
  has_and_belongs_to_many :designs
  has_and_belongs_to_many :designers
  has_and_belongs_to_many :properties
  has_and_belongs_to_many :faqs
  has_and_belongs_to_many :popular_links
  has_many :grading_tags, as: :grading_taggable
  has_one :seo_list
  has_many :category_new_arrivals
  has_many :dynamic_size_charts
  has_many :option_types
  has_one :default_size_chart, -> {where(designer_id: nil, default: true)},class_name: 'DynamicSizeChart'
  enum dynamic_size_chart_state: {dynamic_size_chart_none: 0,dynamic_size_chart_active: 1, dynamic_size_chart_inherit: 2}
  serialize :breadcrumb_path, Hash

  PRICE_MATCH_GUARANTEE_CATEGORIES = promise do
    required_category_ids = []
    price_match_categories = SystemConstant.get('PRICE_MATCH_CATEGORY').to_s.split(',')
    Category.where(id: price_match_categories).each do |category|
      required_category_ids.push(*category.cached_self_and_descendants_id)
    end unless price_match_categories == ["-1"]
    required_category_ids.uniq
  end

  # Finds category by name case insensitive
  #
  # == Parameters:
  #   name::
  #     String
  #
  # == Returns:
  # Category Object
  #
  def self.find_by_namei(name)
    key = 'category_find_by_namei_' + name.to_s
    RequestStore.cache_fetch(key, expires_in: 24.hour) do
      where("lower(name) = ?", name.downcase).take
    end
  end

  def self.getids(name)
    Rails.cache.fetch("getids_#{name}", expires_in: API_CACHE_LIFESPAN.minutes) do
      if (category = find_by_namei(name)).present?
        category.self_and_descendants.pluck(:id)
      else
        []
      end
    end
  end

  def cached_self_and_ancestors_name
    RequestStore.cache_fetch("category_self_and_ancestors_name_#{id}") do
      self_and_ancestors.pluck(:name)
    end
  end

  def dynamic_size_chart_category
    if dynamic_size_chart_active?
      self
    elsif dynamic_size_chart_inherit?
      ancestors.reverse.each do |category|
        if category.dynamic_size_chart_active?
          return category
        elsif !category.dynamic_size_chart_inherit?
          return nil
        end
      end
    end
  end

  def cached_self_and_ancestors_id
    RequestStore.cache_fetch("category_self_and_ancestors_id_#{id}") do
      self_and_ancestors.pluck(:id)
    end
  end

  def cached_self_and_descendants_id
    RequestStore.cache_fetch("category_self_and_descendants_id_#{id}") do
      self_and_descendants.pluck(:id)
    end
  end

  def get_menu_list_by_count(name)
    Rails.cache.fetch("category_menu_list_#{name}_mobile_h2l",expires_in: 24.hours) do
      Design.joins(:categories).where(categories:{id: self.self_and_descendants.pluck(:id)}).order('count(designs.id) desc').group('categories.id').count(:id)
    end
  end

  def get_menu_list_by_name(name)
    Rails.cache.fetch("category_menu_list_#{name}_mobile_alpha",expires_in: 24.hours) do
      Design.joins(:categories).where(categories:{id: self.self_and_descendants.pluck(:id)}).order('categories.name').group('categories.id').count(:id)
    end
  end

  def weight
    self[:weight]
  end

  def approximate_shipping(country, rate)
    ApplicationController.helpers.get_price_in_currency(
      Order.shipping_cost_for(weight, country),
      rate
    )
  end

  def self.get_name_to_ids
    Rails.cache.fetch('category_names_to_ids',expires_in: 24.hours) do
      Category.all.map{|c|[c.id,c.name]}.to_h
    end
  end
end
