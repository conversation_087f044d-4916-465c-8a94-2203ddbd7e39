# == Schema Information
#
# Table name: line_items
#
#  id                       :integer          not null, primary key
#  design_id                :integer
#  cart_id                  :integer
#  quantity                 :integer          default(1)
#  created_at               :datetime
#  updated_at               :datetime
#  note                     :text
#  designer_order_id        :integer
#  snapshot_price           :integer
#  received                 :string(255)
#  status                   :string(255)
#  return_id                :integer
#  return_designer_order_id :integer
#  variant_id               :integer
#  received_by              :string(255)
#  received_on              :datetime
#  shipment_id              :integer
#  inbound_shipment_id      :integer
#  snapshot_discount_price  :integer
#  design_quantity_reduce   :boolean          default(FALSE)
#  qc_pending               :string(255)
#  qc_done                  :string(255)
#  stitching_pending        :string(255)
#  stitching_done           :string(255)
#  stitching_required       :string(255)
#  qc_done_by               :integer
#  stitching_done_by        :integer
#  qc_done_on               :datetime
#  stitching_done_on        :datetime
#  stitching_sent           :string(255)
#  stitching_sent_on        :datetime
#  stitching_sent_by        :integer
#  stitching_received_by    :integer
#  measuremnet_received_on  :datetime
#  measuremnet_received_by  :integer
#  measuremnet_confirmed    :string(255)
#  fabric_measured_on       :datetime
#  fabric_measured_by       :integer
#

class LineItem < ActiveRecord::Base
  belongs_to :design
  belongs_to :designer_order
  belongs_to :variant
  belongs_to :cart
  belongs_to :return
  belongs_to :return_designer_order
  belongs_to :shipment
  has_many :tailoring_info, as: :item
  has_one :order, through: :designer_order
  has_many :discount_line_items
  has_many :stitching_measurements
  has_many :survey_answers, as: :surveyable
  has_many :line_item_addons, dependent: :destroy, autosave: true, inverse_of: :line_item
  has_many :item_promotion_trackings, dependent: :destroy
  has_many :audit_product_scans, as: :auditable
  #after_create { Rails.cache.delete("line_item_count_for_design_#{design_id}") }

  attr_accessor :skip_quantity_validation
  attr_accessor :is_duplicate

  attr_reader :pair_product

  validates :design, presence: true, if: ->(line_item){ line_item.design_id_changed? && !is_duplicate}
  validates :design_id, presence: true
  validates_numericality_of :quantity, greater_than_or_equal_to: 1,
    less_than_or_equal_to: :available_quantity, unless: :skip_quantity_validation

  accepts_nested_attributes_for :line_item_addons

  # Attributes that require currency conversion
  # Needs to always be above Priceable
  PRICE_ATTR = [:snapshot_price, :snapshot_discount_price]
  TAILOR_OTHER_STITCHABLE_ADDONS = ['Fall and Pico', 'Pre-Stitched Saree']

  validates :design_id, uniqueness: { scope: [:design_id, :variant_id,
    :cart_id] }, if: :validate_design_id_uniq? #Proc.new{|a| a.cart_id.blank?}

  include Priceable

  has_attached_file :return_image
  validates_attachment :return_image,
    file_name: {matches: [/png\Z/, /jpe?g\Z/, /gif\Z/]}
  do_not_validate_attachment_file_type :return_image

  has_attached_file :return_image2
  validates_attachment :return_image2,
    file_name: {matches: [/png\Z/, /jpe?g\Z/, /gif\Z/]}
  do_not_validate_attachment_file_type :return_image2

  before_save :update_triggers
  after_create :send_localytics_notification, :track_events

  scope :sane_line_items, -> { where.not('designer_orders.state IN (?)', %w[new pending canceled rto vendor_canceled]).where('line_items.status IS NULL OR line_items.status <> ?', 'cancel') }

  # Cost of LineItem
  #
  # == Parameters:
  #   rate::
  #     Integer
  #   payable::
  #     String
  #
  # == Returns:
  # Integer
  #

  def sor_available?
    #This is to check sor availability in cart before order is placed
    (self.variant || self.design).sor_available?
  end

  def pair_product=(value)
    @pair_product = value.to_s == 'true'
  end

  def total_currency(rate, payable_to = 'all', return_type = false, return_quantity = 0)
    if !return_type
      amount =  self.snapshot_price_currency(rate,(payable_to != 'designer'))
    else
      amount =  self.snapshot_price_currency(rate)
    end
    self.line_item_addons.each do |line_item_addon|
      if payable_to == 'all' || line_item_addon.snapshot_payable_to == payable_to || line_item_addon.line_item.inhouse_pre_stitching
        amount += line_item_addon.snapshot_price_currency(rate)
      end
    end
    (amount * (return_quantity.zero? ? self.quantity : return_quantity)).round(CurrencyConvert.round_to)
  end

  def addons_total(rate)
    (self.quantity * self.line_item_addons.flatten.sum{|addon| addon.snapshot_price_currency(rate)}).round(CurrencyConvert.round_to)
  end
  
  def addons_total_without_quantity(rate)
    (self.line_item_addons.flatten.sum{|addon| addon.snapshot_price_currency(rate)}).round(CurrencyConvert.round_to)
  end

  def total_currency_without_addons(rate, payable_to = 'all', return_type = false)
    if !return_type
      amount = self.snapshot_price_currency(rate,(payable_to != 'designer'))
    else
      amount =  self.snapshot_price_currency(rate)
    end
    (amount * self.quantity).round(CurrencyConvert.round_to)
  end

  def max_quantity_on_cart
    klass = self.variant.presence || self.design
    klass.sor_available? ? klass.in_stock_warehouse.to_i : klass.quantity
  end

  def snapshot_price
    is_cart_dynamic_and_item_is_not_ordered? ? (variant || design).effective_price : self[:snapshot_price]
  end

  def buy_get_free
    return if design.cart_addon?
    is_cart_dynamic_and_item_is_not_ordered? ? PromotionPipeLine.bmgnx_hash.presence && design.buy_get_free : self[:buy_get_free]
  end

  def scaling_factor
    is_cart_dynamic_and_item_is_not_ordered? ? design.get_scale : self[:scaling_factor]
  end

  def is_cart_dynamic_and_item_is_not_ordered?
    not_ordered? && LINE_ITEM_SYNC_DESIGN
  end

  def not_ordered?
    designer_order_id.blank?
  end

  def snapshot_currency_rate
    is_cart_dynamic_and_item_is_not_ordered? ? CurrencyConvert.get_rate_by_code(snapshot_country_code) : self[:snapshot_currency_rate]
  end

  def snapshot_country_code
    is_cart_dynamic_and_item_is_not_ordered? ? Design.country_code : self[:snapshot_country_code]
  end

  def assign_synced_attributes
    self.snapshot_country_code = snapshot_country_code
    self.snapshot_price = snapshot_price
    self.scaling_factor = scaling_factor
    self.snapshot_currency_rate = snapshot_currency_rate
    self.buy_get_free = buy_get_free
    line_item_addons.each(&:assign_synced_attributes)
  end

  # Setting snapshot_price
  #
  def update_triggers
    if self.design_id_changed? && !is_duplicate
      self.snapshot_price = (variant || design).effective_price
      self.scaling_factor = self.design.get_scale
      self.vendor_selling_price = (variant || design).get_vendor_selling_amount
    end
    if designer_order_id_was.nil?
      country = RequestStore.fetch("currency_convert_country_code_#{Design.country_code}") do
        CurrencyConvert.currency_convert_cache_by_country_code(Design.country_code || 'US')
      end
      country ||= CurrencyConvert.find_by_country_code(Design.country_code) || CurrencyConvert.find_by_country_code('US')
      self.snapshot_country_code = country.try(:country_code)
      self.snapshot_currency_rate = country.try(:rate)
    end
  end

  # Updating Addons
  #
  # == Parameters:
  #   params::
  #     Hash with following structure
  #      line_item_addons_attributes: [:addon_type_value_id, :notes]
  #     like nested attribute
  #
  def clear_add_addons(params)
    self.line_item_addons = []
    self.line_item_addons_attributes = params[:line_item_addons_attributes] ||
      {}
  end

  # Check whether there is any paid line item addon
  #
  # == Returns:
  # Boolean
  #
  def paid_addons?
    inhouse_prestitch = self.inhouse_pre_stitching
    self.line_item_addons.each do |line_item_addon|
      if line_item_addon.snapshot_price.present? &&
      	(line_item_addon.snapshot_price >0 && (line_item_addon.snapshot_payable_to.downcase == 'mirraw' || inhouse_prestitch )) || ['standard_stitching','custom_stitching'].include?(line_item_addon.addon_type_value.addon_type_value_group.try(:name))
        return true
      end
    end
    false
  end

  # Check whether there is any stitching addon
  #
  # == Return:
  # Boolean
  #
  def stitching_addons?
    self.line_item_addons.each do |line_item_addon|
      return true if line_item_addon.stitching?
    end
    false
  end

  # Checks for domestic Pre-Stitching
  def inhouse_pre_stitching
    if self.item_details['Pre-Stitch'].present?
      return true
    else
      if self.line_item_addons.any?{|lia| lia.check_inhouse_pre_stitching}
        self.update_columns(item_details: {'Pre-Stitch'=>'True'})
        return true
      end
    end
    return false
  end

  # Total cost addon - conditionally if payable_to.present?
  #
  # == Parameters:
  #   payable_to::
  #     String
  #
  # == Returns:
  # Integer
  #
  def addons_value_for(payable_to)
    total = 0
    self.line_item_addons.each do |line_item_addon|
      total +=
        if payable_to.blank? || line_item_addon.snapshot_payable_to == payable_to
          line_item_addon.total
        else
          0
        end
    end
    total
  end

  def is_product_plus_size?
    self.line_item_addons.any?{|la| la.notes.to_s.downcase.include?("plus size")}
  end

  # Updates snapshot discount price with discount percent and
  # returns discounted amount
  #
  # == Parameters:
  #   discount_percent::
  #     Integer
  #   order_discount_percent::
  #     Integer
  #
  # == Returns:
  # Integer
  #
  def apply_discount(discount_percent, order_discount_percent)
    # Using update attribute to avoid line item and line item addon callback
    # Discount price for line item
    self.snapshot_discount_price = (self.snapshot_price * (1 - discount_percent)
      ).floor
    self.snapshot_discount_price = 0 if self.snapshot_discount_price < 0

    discount = self.snapshot_price - self.snapshot_discount_price

    self.line_item_addons.each do |line_item_addon|
      line_item_addon.apply_discount(
      discount_percent, order_discount_percent).save
      discount += line_item_addon.discount
    end
    discount * self.quantity
  end

  def get_rack_auditable_barcodes
    [
      get_unpacking_barcode,
      get_rack_barcode            
    ].flatten
  end

  # Update snapshot_discount_price and provides new price and discount given
  # as per quantity
  #
  # == Parameters:
  #   discount::
  #     Integer
  #
  # == Returns:
  # Array
  #
  def adjust_discount(discount)
    if discount < 0 || self.snapshot_discount_price >= discount
      # Difference can also be negative
      [discount * self.quantity, self.snapshot_discount_price - discount]
    elsif self.snapshot_discount_price < discount
      [self.snapshot_discount_price * self.quantity, 0]
    end
  end

  # Get total weight of items
  #
  # == Returns:
  # Integer
  #
  def weight
    return  0 if design.cart_addon?
    design.approx_weight * self.quantity
  end
  # Provides current stock for product and whether it meets required quantity
  #
  # == Returns:
  # Array - [Integer, Boolean]
  #
  def available_quantity(country=nil)
    quantity =
      if ['banned', 'on_hold', 'inactive'].include?(design.designer.state_machine)
        0
      elsif self.design.in_stock?
        if country=='IN' && !ALLOWED_DOMESTIC_SOR
          if self.variant_id.present?
            self.variant.quantity - self.variant.in_stock_warehouse.to_i
          else
            self.design.quantity - self.design.in_stock_warehouse.to_i
          end
        else
          self.variant_id.present? ? self.variant.quantity : self.design.quantity
        end
      else
        0
      end
  end

  def add_variant(variant)
    if variant.present?
      self.variant = variant
      notes = ''
      variant.option_type_values.each do |optv|
        notes = optv.option_type.name + ' ' + optv.name
      end
      self.add_note(notes)
    end
  end

  def display_image size
    if (image = design.master_image).present?
      return (IMAGE_PROTOCOL + image.photo(size))
    else
      return ('default_image.jpg')
    end
  end

  def note_without_bmgn
    if note.present?
      separator = ' ... '
      notes_without_bmgn = note.split(separator).reject do |note_iter|
        /\| Discount items \(B\d+G\d+\): \d+ At \d+ % off \|/ === note_iter
      end
      notes_without_bmgn.join(' ... ')
    else
      ''
    end
  end

  def add_note(note, ignore_if_exists: false)
    separator = '...'
    
    if self.note.blank?
      self.note = note
    elsif !self.note.include?(note) || !ignore_if_exists
      self.note += " #{separator} #{note}"
    end
  end

  def variants
    if self.variant_id.present?
      variants = []
      self.variant.option_type_values.each do |option_type_value|
        variants << {
          p_name: option_type_value.p_name,
          option_type: option_type_value.option_type.p_name
        }
      end
      {
        variant_id: self.variant_id,
        option_type_values: variants
      }
    end
  end

  def addons(rate)
    addons = []
    self.line_item_addons.each do |addon|
      addons << {
        name: addon.addon_type_value.name,
        price: addon.snapshot_price_currency(rate)
      }
    end
    addons
  end

  def addon_type_value_group(name)
    self.line_item_addons.joins(addon_type_value: :addon_type_value_group).where(addon_type_value_groups: {name: name})
  end

  # input params - none
  #
  # Get details about forms required
  #
  # Returns Array
  def required_forms
    forms = { blouse: ['custom blouse stitching', 'regular blouse stitching'],
              suit: ['custom stitching', 'custom tailoring', 'standard stitching sizes',
              'standard stitching size', 'custom_stitching', 'standard_stitching_sizes','standard_stitching', 'standard stitching']}
    ids = addon_type_value_group(['custom_stitching','standard_stitching']).pluck('line_item_addons.id')
    if ids.count > 0
      forms.each do |key, value|
        if AddonTypeValue.joins(:line_item_addons).where(line_item_addons: {id: ids}).where('LOWER(name) IN (?)', value).count > 0
          forms[key] = true
        else
          forms[key] = false
        end
      end
    end
    forms
  end

  # input params - none
  #
  # Get urls for required forms
  #
  # Returns Hash
  def get_forms
    forms = self.required_forms
    forms_url = {}
    forms_url[:blouse] = true
    forms_url[:suit] = true
    forms.each do |key, value|
      if value == true && key == :suit && self.design.designable_type == "Lehenga"
        forms[:blouse] = forms_url[:blouse].present? ? forms_url[:blouse] : nil
        forms[:suit] = nil
      else
        forms[key] = (value == true && forms_url[key].present?) ? forms_url[key] : nil
      end    
    end
    forms
  end

  def update_snapshot_price
    self.update_attribute(:snapshot_price, (variant || design).effective_price)
    self.update_attribute(:vendor_selling_price, (variant || design).get_vendor_selling_amount)
  end

  def stitched?
    self.stitching_done.present?
  end

  def returned?
    self.return_designer_order_id.present?
  end

  def returnable?
    !!(
      !self.returned? &&
      !self.stitched? &&
      self.designer_order.try(:returnable?)
    )
  end

  # Checks whether return_designer_order_id is present and
  # is not equal to provided rdo id
  #
  # == Return
  # Boolean
  #
  def already_returned?(rdo_id)
    self.returned? && self.return_designer_order_id != rdo_id
  end

  def check_for_valid_return(rdo)
    rdo.errors[:line_items] << 'return request already initiated' if self.already_returned?(rdo.id)
    rdo.errors[:line_items] << 'stitched item can not be returned' if self.stitched?
  end

  def get_return_amount(order_total,designer_order_total,discount,order_discount,order, order_total_only_bmgn_products = 0.0)
    total_discount,addon_price = 0,0
    item_price = (self.snapshot_price_currency(1) * (self.return_quantity.to_i > 0 ? self.return_quantity : 1)).to_f

    total_discount += ((item_price / designer_order_total) * discount.to_f) if discount.to_f > 0
    order_discount -= order.additional_discount.to_i if order_total_only_bmgn_products > 0.0 && !buy_get_free
    if order_discount.to_f > 0 && order_total.to_i > 0
      if (buy_get_free && order_total_only_bmgn_products > 0.0)
        total_discount += ((item_price/ order_total_only_bmgn_products.to_f) * order.additional_discount.to_f)
        total_discount += ((item_price/ order_total).to_f * (order.discount.to_i + (order.referral_discount.to_f * order.currency_rate.to_f)))
      else
        total_discount += ((item_price/ order_total.to_f) * order_discount.to_f)
      end
    end
    admin_discount  = self.discount_line_items.to_a.sum(&:price)
    item_price -= (total_discount + admin_discount.to_i).to_i
    return item_price,total_discount,admin_discount
  end

  def to_amazon_hash
    {
      SKU: design_id,
      MerchantId: MirrawMobile::Application.config.pay_with_amazon[:merchant_id],
      Title: design.title.to_s,
      Description: design.description.to_s.truncate(80),
      Price: {Amount: snapshot_price,CurrencyCode: 'INR'},
      Quantity: quantity,
      FulfillmentNetwork: 'MERCHANT',
      HandlingTime:{MinDays:2,MaxDays:DOMESTIC_SHIPPING_TIME + 2},
      ItemCustomData: {LineItemId: id,VendorSellingPrice: vendor_selling_price,VariantId: variant_id,Note: note}
    }
  end

  def ga_data(other_data={}, old_quantity: nil)
    country_code = Design.country_code
    conversion_rate = CurrencyConvert.currency_convert_cache_by_country_code(Design.country_code).rate
    new_quantity = old_quantity ? (quantity - old_quantity) : quantity
    market_rate = CurrencyConvert.countries_marketrate[country_code] || 1
    customization = (self.addons_total_without_quantity(conversion_rate)* market_rate).round(CurrencyConvert.round_to)
    a = self.cart.present? ? self.cart.get_free_items_bmgnx(bmgnx_hash = PromotionPipeLine.bmgnx_hash) : {}
    bmgn = ""
    if a.keys.present?
      a.each_key do |key|
        if self.id == key
          bmgn = "B1G1"
        end
      end
    end
    
    design.ga_data_v2.merge!({
      'quantity' => new_quantity,
      'item_customization' => customization,
      'item_offer' => bmgn,
    }).merge!(other_data)
  end
  
  def google_ads_data
    @googe_add_hash_new = {
        "item_ids": [
          {
            "id": "#{self.design.id}",
            "google_business_vertical": "retail"
          }
        ],
        "content_ids": ["#{self.design.id}"],
        "contents": [{"id": "#{self.design.id}","quantity":self.quantity}]
      }
  end

  def get_vendor_payout_for_item
    item_total = 0
    sor_item_total = 0
    if self.sor_available?
      sor_item_total += self.vendor_selling_price * self.quantity
    else
      item_total += self.vendor_selling_price.to_f * self.quantity
    end
    item_total += self.line_item_addons.select { |li| li.snapshot_payable_to == 'designer' }.sum { |addon| addon.total }
  
    payout = if (item_total + sor_item_total) > 0
      if (v_discount = self.designer_order.discount).present?
        if item_total > 0
          item_total = (item_total - v_discount) * (100 - self.designer_order.transaction_rate) / 100
        else
          sor_item_total = (sor_item_total - v_discount) * (100 - self.designer_order.transaction_rate) / 100
        end
      else
        if item_total > 0
          item_total = item_total * (100 - self.designer_order.transaction_rate) / 100
        else
          sor_item_total = sor_item_total * (100 - self.designer_order.transaction_rate) / 100
        end
      end
      item_total + sor_item_total
    elsif design.designer.is_transfer_model?
      item_total + sor_item_total
    else
      discount_amt = (100 - self.designer_order.promotion_discount)
      self.designer_order.total * (100 - self.designer_order.transaction_rate) / discount_amt
    end
  
    return payout
      
  end

  def total_value
    snapshot_price * scaling_factor * quantity
  end

  def send_return_status(less_stages = true)
    notes = {'return_initiated'=>'Your Return has been initiated and will be picked up in 1 to 2 days.',
      'return_pickedup'=>'Your return has been picked up and your refund will initiate shortly.',
      'refund_initiated'=>'Your refund has been initiated and will be completed in  3 to 5 days.',
      'refund_completed'=>'Your refund has been Completed.',
      'refund_processing'=>'Your refund is under processing.'}
    if less_stages
      stages = ["Refund Initiated-refund_initiated-created_at","Refund Processing-refund_processing-pending_on", "Refund Completed-refund_completed-completed_on"]
    else
      stages = ["Return Initiated-return_initiated-created_at","Return Pickedup-return_pickedup-pending_on","Refund Initiated-refund_initiated-pending_on", "Refund Completed-refund_completed-completed_on"]
    end
    count = 0
    timestamp_hash = {}
    if (line_item_return = self.return).present?
      count = 1
      timestamp_hash[:created_at] = line_item_return.created_at 
      if line_item_return.state == 'pending_payment'
        count = less_stages ? 1 : 2
        timestamp_hash[:pending_on] = line_item_return.pending_on
      elsif line_item_return.state == 'payment_complete'
        count = less_stages ? 2 : 3
        timestamp_hash[:pending_on] = line_item_return.pending_on 
        timestamp_hash[:completed_on] = line_item_return.completed_on
      end
    end
    return count, timestamp_hash, stages, notes
  end

  def get_process_barcodes
    [
      get_rack_barcode,
      get_stitching_tailor_assign_barcode,
      get_stitching_tailor_inscan_barcode,
      get_unpacking_barcode
    ].flatten
  end

  def get_unpacking_barcode
    "Unpack-#{id}"
  end

  def get_rack_barcode
    order = designer_order.order
    item_rack_code = get_item_rack_code(designer_order.rack_list.try(:code))
    "#{item_rack_code}-#{order.number}-#{designer_order.rack_code}-#{design_id}"
  end

  def get_stitching_tailor_assign_barcode
    product_code = get_stitching_product_code
    stitching_measurements.collect do |measurement|
      product_code = 'F' if measurement.product_designable_type == 'fnp'
      "#{measurement.id}-#{measurement.order_id}-#{id}-#{quantity}-#{product_code}"
    end
  end

  def get_stitching_tailor_inscan_barcode
    stitching_measurements.collect do |measurement|
      "#{designer_order.order.try(:number)}-#{measurement.design_id}-#{measurement.id}-#{id}"
    end
  end

  def get_stitching_product_code
    ['Saree', 'Lehenga'].include?(design.designable_type) ? 'B' : 'D'
  end

  def get_item_rack_code(rack_code)
    ((fake_rack_code = item_details['fake_rack']).present? ? fake_rack_code : rack_code)
  end

  def get_other_stitchable_product_addons
    (get_addon_names & LineItem::TAILOR_OTHER_STITCHABLE_ADDONS)
  end

  def get_stitching_type
    (get_addon_names & ['Custom Blouse Stitching', 'Custom Stitching']).present? ? 
      'Custom' : 
    ((get_addon_names & ['Regular Blouse Stitching', 'Standard Stitching']).present? ?
      'Standard' : 
      'Unstitched')
  end

  def get_addon_names
    @addon_names ||= (line_item_addons.map(&:addon_type_value).map(&:name))
  end

  private

  def send_localytics_notification
    if !is_duplicate && changed? && cart.present? && NOTIFICATIONS_ENABLE == 'true' && ((variant.try(:quantity) || design.quantity) <= SystemConstant.get('THRESHOLD_QTY_DESIGN_NOTIFICATION').to_i) && self.design.available?
      if (user_id = self.cart.user_id).present?
        acc_id = Account.where(accountable_id: user_id).pluck(:id)
        Design.sidekiq_delay.individual_less_qty_notification_android_cart(acc_id, self.design_id)
      end
    end
  end

  def track_events
    track_promotions = PromotionTracking.get_trackable_promotions(Design.country_code)
    if pair_product && (track_event_id = track_promotions['complete_the_look']).present?
      item_promotion_trackings.create(promotion_tracking_id: track_event_id)
    end
  end

  def validate_design_id_uniq?
    cart_id.present? && (new_record? || design_id_changed? || variant_id_changed? || cart_id_changed?)
  end
end
