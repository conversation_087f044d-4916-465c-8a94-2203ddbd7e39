class Promotion

  extend Api::V1::AssetsHelper

  # Returns list of classes, method_list and varibale_list as variables,
  # which are used in promotion pipe line on particular promotion period
  def self.initialize_variables(todays_date=Time.zone.now)
    promotion_pipelines = PromotionPipeLine.where{(start_date.lt todays_date) & (end_date.gt todays_date)}
    p_methods = promotion_pipelines.pluck(:methods_hash)
    p_variables = promotion_pipelines.pluck(:variables_hash)

    variables_hash = []
    p_variables.each do |p_var|
      variables_hash << JSON.parse(p_var)
    end
    pipeline_methods = []
    klass = []
    p_methods.each do |p_method|
      parsed_methods = JSON.parse(p_method)
      pipeline_methods << parsed_methods
      klass << parsed_methods.keys
    end
    klass = klass.flatten.uniq
    return [variables_hash, pipeline_methods, klass]
  end

  # Return array of method_name list of particular class
  def self.get_method_list(method_hash, klass)
    p_methods = []
    method_hash.each do |p_method|
      p_methods << p_method[klass] if p_method[klass].present?
    end
    p_methods.flatten.uniq
  end

  # Check to use existing method or new
  def self.use_old_method?(method_hash, klass_list, klass, method_name)
    p_methods = get_method_list(method_hash, klass)
    !(klass_list.include?(klass) && p_methods.include?(method_name))
  end

  def self.shipping_offer_on_category
    Rails.cache.fetch("Promotion_category_free_shipping", expires_in: 1.day) do
      promotion_pipelines = PromotionPipeLine.active_promotions
      var_hash = promotion_pipelines.find_by_name('category_free_shipping').try(:variables_hash)
      (JSON.parse(var_hash)['sale_on'] if var_hash) || ''
    end
  end

  def self.mastercard_discount_percent
    Rails.cache.fetch("mastercard_discount_promotion_#{User.app_source.split('-')[0]}", expires_in: 1.day) do
      promotion_pipelines = PromotionPipeLine.active_promotions.app_source(User.app_source.split('-')[0])
      var_hash = promotion_pipelines.find_by_name('mastercard_discount_promotion').try(:variables_hash)
      (JSON.parse(var_hash)['percent'] rescue '' if var_hash) || ''
    end
  end

  def self.discount_offer_on_category(active_promotions)
    RequestStore.cache_fetch('category_name_discount_sale') do
      promotion_pipelines = active_promotions
      var_hash = promotion_pipelines.find_by_name('category_discount_sale').try(:variables_hash)
      (JSON.parse(var_hash)['sale_on'] if var_hash) || ''
    end
  end

  # Check free_shipping available or not and if available then on which amount
  # == Returns:
  # Array
  def self.get_free_shipping(country=nil, design = nil, country_code: Design.country_code)
    amount = 0
    available = false
    country_name = country || Address.shipping_country
    var_hash = RequestStore.fetch("get_free_shipping_#{country_name}_#{User.app_source}") do
                  country_code ||= Country.get_country_code(country)
                  promotion_pipelines = PromotionPipeLine.active_promotions.app_source(User.app_source)
                  if country_code.present?
                    promotion_pipelines.find_by_name("free_shipping_#{country_code}").try(:variables_hash) ||  promotion_pipelines.find_by_name("free_shipping").try(:variables_hash)
                  else
                    promotion_pipelines.find_by_name("free_shipping").try(:variables_hash)
                  end
    end
    if var_hash.present?
      if design == true
        amount = JSON.parse(var_hash)['on_design_rate']
        available = true
      else
        amount = JSON.parse(var_hash)['free_shipping_rate']
        available = true
      end
    end
    [amount.to_i, available]
  end

  def self.additional_discount_percent(country_code=nil)
    RequestStore.cache_fetch("additional_discount_mobile_#{country_code}_#{User.app_source}", expires_in: 1.day) do
      promotion_pipelines = PromotionPipeLine.active_promotions.app_source(User.app_source)
      country_promotion = if country_code.present?
        promotion_pipelines.find_by_name("additional_discount_#{country_code}") || promotion_pipelines.find_by_name("additional_discount")
      else
        promotion_pipelines.find_by_name("additional_discount")
      end
      country_code_list = country_promotion.present? ? country_promotion.country_code : ''
      var_hash = country_promotion.try(:variables_hash)

      discount_percent = [0]
      amount_on = [0]
      if var_hash && country_code_list.split(',').include?(country_code)
       discount_percent = JSON.parse(var_hash)['discount_percent'].split(',')
       amount_on = JSON.parse(var_hash)['amount'].split(',')
     end
     [discount_percent, amount_on]
    end
  end

  def self.discount_promotion(active_promotions, country_code=nil)
    RequestStore.cache_fetch("discount_promotion_record_mobile_#{country_code}", expires_in: 1.day) do
      promotion_pipelines = active_promotions
      if country_code.present?
        promotion_pipelines.find_by_name("global_sale_#{country_code}") || promotion_pipelines.find_by_name("global_sale") || []
      else
        promotion_pipelines.find_by_name('global_sale') || []
      end
    end
  end

  def self.category_discount_promotion(active_promotions, country_code=nil)
    RequestStore.cache_fetch("category_discount_promotion_record_mobile_#{country_code}", expires_in: 1.day) do
      promotion_pipelines = active_promotions
      if (category_name = Promotion.discount_offer_on_category(active_promotions)).present?
        if country_code.present?
          promotion_pipelines.find_by_name("category_discount_sale_#{country_code}") || promotion_pipelines.find_by_name("category_discount_sale") || []
        else
          promotion_pipelines.find_by_name('category_discount_sale') || []
        end
      else
        []
      end
    end
  end

  def self.category_promotion_ids(active_promotions)
    RequestStore.cache_fetch("mobile_category_promotion_ids", expires_in: 1.day) do
      category_name = Promotion.discount_offer_on_category(active_promotions)
      if category_name.present?
        id_list = []
        category_name.split(',').each do |cat_name|
          id_list.push(*Category.getids(cat_name))
        end
        id_list
      else
        []
      end
    end
  end

  def self.sale_discount_on_country(active_promotions, country_code=nil)
    RequestStore.cache_fetch("sale_discount_country_mobile_#{country_code}", expires_in: 1.day) do
      country_promotion = Promotion.discount_promotion(active_promotions,country_code)
      country_promotion.present? ? country_promotion.country_code : ''
    end
  end

  def self.category_sale_discount_on_country(active_promotions, country_code=nil)
    RequestStore.cache_fetch("category_sale_discount_country_mobile_#{country_code}", expires_in: 1.day) do
      country_promotion = Promotion.category_discount_promotion(active_promotions,country_code)
      country_promotion.present? ? country_promotion.country_code : ''
    end
  end

  def self.free_shipping_on_country(country_code=nil)
    promotion_pipelines = PromotionPipeLine.active_promotions.app_source(User.app_source)
    country_promotion =   if country_code.present?
                            promotion_pipelines.find_by_name("free_shipping_#{country_code}") || promotion_pipelines.find_by_name("free_shipping")
                          else
                            promotion_pipelines.find_by_name("free_shipping")
                          end
    country_promotion.present? ? country_promotion.country_code : ''
  end

  def self.free_shipping_on_country?(country=nil)
    country = country || Address.shipping_country
    promotion_pipelines = PromotionPipeLine.active_promotions.app_source(User.app_source)
    shipping_country_code = country.present? ? Country.get_country_code(country) : Design.country_code
    if (country_code = Promotion.free_shipping_on_country(shipping_country_code)).present?
      country_code.split(',').include?(shipping_country_code)
    elsif promotion_pipelines.where('name like ?', '%free_shipping%').pluck(:country_code).include?(shipping_country_code)
      true
    else
      false
    end
  end

  def self.stitching_offer(country_code)
    RequestStore.cache_fetch("stitching_offer_#{User.app_source}_#{country_code}", expires_in: 1.day) do
      var_hash = (stitching_offer = Promotion.get_stitching_offer_in_country(country_code).try(:variables_hash))
      stitching_rate = 0
      stitching_on = 0
      category_names = ""
      addon_names = ""
      if stitching_offer.present? && var_hash.present?
        var_hash = JSON.parse(var_hash)
        stitching_rate = var_hash['stitching_rate']
        stitching_rate_standard = var_hash['standard_stitching']
        stitching_rate_standard_on = var_hash['standard_stitching_on']
        stitching_rate_custom = var_hash['custom_stitching']
        stitching_rate_custom_on = var_hash['custom_stitching_on']
        stitching_on = var_hash['stitching_on']
        category_names = var_hash['category_name'].downcase
        addon_names = var_hash['addon_name'].downcase
      end
      { stitching_rate: stitching_rate, stitching_rate_standard: stitching_rate_standard, stitching_rate_standard_on: stitching_rate_standard_on, stitching_rate_custom: stitching_rate_custom, stitching_rate_custom_on: stitching_rate_custom_on, stitching_on:  stitching_on.to_i, category_names: category_names.split(','), addon_names: addon_names.split(',') }
    end
  end

  def self.get_stitching_offer_in_country(country_code)
    promotion_pipelines = PromotionPipeLine.active_promotions.app_source(User.app_source)
    offer_name = 'stitching_offer'.freeze
    required_attrs = [:country_code, :variables_hash]
    stitching_offer = if country_code.present?
        RequestStore.cache_fetch("available_#{offer_name}_#{User.app_source}_#{country_code}", expires_in: 1.day) do
          offer = promotion_pipelines.select(required_attrs).find_by_name("#{offer_name}_#{country_code}")
          offer ||= promotion_pipelines.select(required_attrs).find_by_name(offer_name)
          offer.present? && offer.country_code.present? && offer.country_code.include?(country_code) ? offer : ''
        end
      else
        RequestStore.cache_fetch("available_#{offer_name}_#{User.app_source}", expires_in: 1.day) do
          promotion_pipelines.select(required_attrs).find_by_name(offer_name) || ''
        end
      end
    stitching_offer.presence
  end

  def self.stitching_offer_on?(design_type, addon_type)
    Promotion.stitching_offer(Design.country_code)[:category_names].include?(design_type.try(:downcase)) && Promotion.stitching_offer(Design.country_code)[:addon_names].include?(addon_type.try(:downcase))
  end

  def self.offer_message(country_code)
    RequestStore.cache_fetch("offer_message_#{country_code}", expires_in: 1.day) do
      active_promotions = PromotionPipeLine.active_promotions
      promotion_offer = active_promotions.find_by_name("offer_message_#{country_code}") || active_promotions.find_by_name('offer_message')
      if promotion_offer.present? && promotion_offer.country_code?(country_code)
        promotion_offer.variables_hash.try{|var_hash| JSON.parse(var_hash)['message']}
      end.to_s
    end
  end

  def self.app_source_offer_message(app_source, country_code)
    RequestStore.cache_fetch("offer_message_#{app_source}_#{country_code}", expires_in: 1.day) do
      active_promotion_offer = PromotionPipeLine.active_promotions
      promotion_offer = (country_code != 'IN' ? active_promotion_offer.find_by_name('app_source_offer_message_international') : active_promotion_offer.find_by_name('app_source_offer_message_domestic'))
      if promotion_offer.present? && (promotion_offer.country_code.blank? ? true : promotion_offer.country_code?(country_code))
        promotion_offer.variables_hash.try{|var_hash| (JSON.parse(var_hash)[app_source] != '0' ? JSON.parse(var_hash)[app_source] : nil)}
      end.to_s
    end
  end

  def self.bmgnx_offer_message(bmgnx_hash = PromotionPipeLine.bmgnx_hash)
    message = "Buy #{bmgnx_hash[:m]} Get #{bmgnx_hash[:n]}"
    message += bmgnx_hash[:x] != 100 ? " at #{bmgnx_hash[:x]} % off" : " free".freeze
  end
  
  def self.bmgnx_tnc(bmgnx_hash = PromotionPipeLine.bmgnx_hash)
    if bmgnx_hash.present?
      m = bmgnx_hash[:m]
      n = bmgnx_hash[:n]
      [
        "Frequently Asked Questions:",
        "Q: What is the offer? \n A: You can add #{m} Product(s) to the cart And get #{n} Product(s) marked under B#{m}G#{n} of your choice for free.",
        "Q: Is the offer valid on all the products? \n A: No, the offer is only valid on the products marked under B#{m}G#{n} offer. The products which are not marked with the ‘B#{m}G#{n} tag’ are not valid and cannot be clubbed with the ‘B#{m}G#{n}’ offer.",
        "Q: What is the offer duration? \n A: The offer is valid from #{bmgnx_hash[:start_date].try(:strftime, '%e %B, %Y')} (00:00 hours) to #{bmgnx_hash[:end_date].try(:strftime, '%e %B, %Y')} (23:59 hours)",
        "Q: What other conditions should apply to avail the offer? \n A: 1: There is no minimum cart amount to avail this offer \n  2: There is no minimum number of times for this offer to be applied on orders until the offer duration gets expired.",
        "Q: How to Avail this offer? \n A: It’s simple. Just follow the steps below: \n 1: Add the product you like which is marked under the ‘B#{m}G#{n} offer’ to cart. \n 2: Now You can choose between two options shown on the cart page i.e to add the same product in cart as the remaining ‘#{n}’ free or to add another ‘#{n}’ products marked under ‘B#{m}G#{n} offer’. \n 3: Add the ‘#{n}’ free products you love to the cart as mentioned in Step{ii} and  check out. (NOTE : out of #{m}+#{n} products in the cart , the products with the lowest prices will be considered as FREE! )",
        "Other Terms and Conditions:",
        "The Offer Buy #{m} Get #{n} free is valid only on selected products that are marked as 'Buy #{m} Get #{n} free'.",
        "Products NOT marked with 'Buy #{m} Get #{n} free' icon are excluded from this offer.",
        "You can buy any product at the normal discount that is available, however if you buy #{m.to_i + n.to_i} products marked as 'Buy #{m} Get #{n} free', you can get the products with Lower price Free.",
        "If 'Buy #{m} Get #{n} free' products have any additional stitching charges or shipping charges then Additional Charges will NOT be considered Free. Additional charges will be added to your order total.",
        "For returning the products the whole set has to be returned for a refund or exchange. A Single product from the combination cannot be returned."
      ]
    end || []
  end

  def self.landing_page
    RequestStore.cache_fetch('offer_landing_page', expires_in: 1.day) do
      if (landing = TagSlider.with_link_value.where("title ILIKE '%#{SystemConstant.get('PROMOTION_OFFER_LANDING_TITLE')}%'").first).present?
        {
          title: landing.title,
          link: landing.link,
          key: search_key(landing.link_type),
          value: search_value(landing.link_type, landing.link_value, landing.link)
        }
      else
        {}
      end
    end
  end

  def self.free_stitching_find_by(country_code)
    promotion_pipelines = PromotionPipeLine.active_promotions.app_source(User.app_source)
    if country_code.present?
      frozen_key="free_stitching_mobile_#{country_code}".freeze
      free_stitching = RequestStore.cache_fetch(frozen_key) do
        promotion_pipelines.find_by_name("free_stitching_#{country_code}") || promotion_pipelines.find_by_name("free_stitching") || []
      end
    else
      free_stitching = RequestStore.cache_fetch(:free_stitching) do
        promotion_pipelines.find_by_name("free_stitching") || []
      end
    end
  end

  def self.free_stitching_at(country_code)
    var_hash = Promotion.free_stitching_find_by(country_code).try(:variables_hash)
    free_stitching_rate = 0
    free_stitching_rate = JSON.parse(var_hash)['stitching_on'] if var_hash.present?
    return free_stitching_rate.to_i
  end


  def self.free_stitching_on_country(country_code)
    country_promotion = Promotion.free_stitching_find_by(country_code)
    country_promotion.present? ? country_promotion.country_code : ''
  end

  def self.flash_deals_on?(country_code)
    RequestStore.cache_fetch("flash_deals_promotion_available_#{country_code}", expires_in: 1.day) do
      PromotionPipeLine.active_promotions.app_source('mobile').for_country(country_code).where(name: 'flash_deals_promotion').any?
    end
  end

  def self.flash_deals_promotion?(country_code, design_id)
    Promotion.flash_deals_on?(country_code) && DesignPromotionPipeLine.active_flash_deals_designs_ids.include?(design_id)
  end

  def self.flash_deals_upcoming_promotion?(country_code, design_id)
    Promotion.flash_deals_on?(country_code) && DesignPromotionPipeLine.upcoming_flash_deals_designs_ids.include?(design_id)
  end

  def self.cumulative_discount_percent(*discount_percents)
    discount_percents.collect!(&:to_f)

    first_discount, second_discount = discount_percents.shift(2)

    return 0 unless first_discount
    return first_discount.round(2) unless second_discount

    cumulative_discount_percent = first_discount + (100 - first_discount) * (second_discount / 100.0)
    cumulative_discount_percent(cumulative_discount_percent, *discount_percents)
  end
end
