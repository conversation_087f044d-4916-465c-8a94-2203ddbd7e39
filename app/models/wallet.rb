include ActionView::Helpers::DateHelper
class Wallet < ActiveRecord::Base
  belongs_to :currency_convert
  has_many :wallet_transactions
  delegate :country_code, to: :currency_convert, allow_nil: true
  has_one :user

  def deduct_amount(cart_wallet)
    self.referral_amount -= cart_wallet.referral_amount
    self.return_amount -= cart_wallet.return_amount
    self.save
  end

  def self.cashback_tnc
    @cashback_tnc ||= if (percentage = cashback_percent.to_f) > 0.0
      [
        "Frequently Asked Questions:",
        "Q: What are Loyalty Points? \n A: 1. Loyalty points are the points that you earn for being our valued customer on the purchases you make. \n 2. You will get “#{percentage}%” of the Subtotal as cashback in your Mirraw Wallet.",
        "Q: Where and when can i see the points i have earned through this offer? \n A: 1. These points will be seen in the Referral amount section of the Mirraw Wallet. \n 2. The points you earn will reflect in your Mirraw Wallet within 24 hours after you have successfully placed an order",
        "Q: How can i use these loyalty points? \n A: 1. You can use these points while placing your next order \n 2. For more details about the usage of points under Referral section visit the wallet terms and conditions page present on the cart page's top right corner if you are an iOS user OR on the my account page if you are an Android user. \n",
        "Terms and Conditions:",
        "Loyalty points are the points that you earn for being our valued customer on the purchases you make.",
        "You will get “#{percentage}%” of the Subtotal as cashback in your Mirraw Wallet.",
        "These points will be seen in the Referral amount section of the Mirraw Wallet.",
        "The points you earn will reflect in your Mirraw Wallet within 24 hours after you successfully place an order.",
        "These points you have earned will expire after “#{distance_of_time_in_words(REFERRAL_EXPIRY_PERIOD)}” . We recommend you to use them before they get expired."
      ]
    else
      []
    end
  end

  def credit_referral
    amount = self.referral_amount + self.currency_convert.referral_amount
    self.update_attribute(:referral_amount, amount)
  end

  def total_amount
    referral_amount + return_amount
  end

  def remove_discount
    update_attributes(referral_amount: 0.0, return_amount: 0.0)
  end

  def currency_symbol
    currency_convert.symbol
  end
  
  def currency_hex_symbol
    currency_convert.hex_symbol
  end

  def referral_amount
    self[:referral_amount].to_f
  end

  def return_amount
    self[:return_amount] + promotional_balance
  end

  def usable_referral_amount(cart_total, round_to_figure)
    cart_total_limit =  (cart_total * self.class.usage_percent.to_f / 100).round(round_to_figure)
    [referral_amount, cart_total_limit, self.class.usage_upto].min
  end

  def reward_pending_gift_cards
    pending_gift_card_orders = user.received_gift_card_orders.where(state: 'sane')

    return unless pending_gift_card_orders.present?

    pending_gift_card_orders.each do |gco|
      source_rate = gco.currency_rate
      destination_rate = currency_convert.market_rate
      amount_to_credit = gco.paid_amount * source_rate / destination_rate

      self.return_amount += amount_to_credit

      gco.build_wallet_transaction({
        user_id: user.id,
        return_amount: amount_to_credit,
        fund_type: 'gift_card',
        wallet_id: id,
        state: 'succeeded'
      })
    end

    ActiveRecord::Base.transaction do
      self.save!
      pending_gift_card_orders.each(&:complete!)
    end
  end

  class << self
    # NOTE: Put every +self+ methods inside this block.
    # Also delete this comment once it is done.

    def cashback_for_amount(amount)
      if amount.to_f >= min_order_total_for_cashback.to_i
        return [(amount.to_f * cashback_percent / 100.0).round(2), cashback_upto].min
      end
      0
    end

    def cashback_percent
      WALLET_CONFIG[geo]['cashback']
    end

    def usage_percent
      WALLET_CONFIG[geo]['usage']
    end

    def usage_upto
      WALLET_CONFIG[geo]['usage_upto'] || Float::INFINITY
    end

    def cashback_upto
      WALLET_CONFIG[geo]['cashback_upto'] || Float::INFINITY
    end

    def min_order_total_for_cashback
      WALLET_CONFIG[geo]['min_amount']
    end

    def usage_upto?
      usage_upto < Float::INFINITY
    end

    def cashback_upto?
      cashback_upto < Float::INFINITY
    end

    private

    def geo
      Design.country_code == 'IN' ? 'domestic' : 'international'
    end
  end
end
