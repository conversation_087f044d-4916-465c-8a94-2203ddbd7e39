# == Schema Information
#
# Table name: countries
#
#  id             :integer          not null, primary key
#  name           :string(255)
#  iso3166_alpha2 :string(255)
#  created_at     :datetime         not null
#  updated_at     :datetime         not null
#  priority       :integer
#  time_zone      :string(255)
#

class Country < ActiveRecord::Base
  has_many :states
  has_many :subscription_plans, dependent: :destroy
  has_many :platform_fees, dependent: :destroy
  alias_attribute :code, :iso3166_alpha2
  after_save :flush_express_delivery_countries_cache, if: -> {:express_delivery_charge_changed? || :shipping_time_changed?}

  def has_states?
    self.states.any?
  end
  alias_method :has_states, :has_states?

  # Priority wise Countries list
  #
  # == Returns:
  #   Array
  #

  def self.get_helpline_number(country_code)
    find_by_country_codei(country_code).helpline_number
  end 

  def self.find_by_country_codei(country_code)
    where("lower(iso3166_alpha2) = ?", country_code.downcase).first
  end

  def self.priority_wise_countries
    Country.order(:priority).order(:name).includes(:states)
  end

  def self.find_by_namei(name)
    where('LOWER(name) = ?', name.try(:downcase)).first
  end

  def self.get_express_delivery_countries
    Rails.cache.fetch(:express_delivery_countries, expires_in: 1.day) do
      Country.where('express_delivery_charge > 0').pluck(:name)
    end
  end

  def self.country_wise_delivery_time(country)
    self.get_country_shipping_time[country.try(:downcase)].to_f + Designer::SLA_CONFIG[:non_stitching_preparation_days]
  end

  def self.get_country_shipping_time
    Rails.cache.fetch(:country_with_shipping_time, expires_in: 1.day) do
      Country.pluck('lower(name),shipping_time').to_h
    end
  end

  def self.get_express_delivery_charge(country)
    express_delivery_charge = Rails.cache.fetch(:express_delivery_prices, expires_in: 1.day) do
      Country.where('express_delivery_charge > 0').inject({}) { |price, country| price[country.name] = country.express_delivery_charge; price }
    end
    express_delivery_charge[country].to_i
  end

  def flush_express_delivery_countries_cache
    Rails.cache.delete(:express_delivery_countries)
    Rails.cache.delete(:express_delivery_prices)
  end

  def Country.get_country_group
    RequestStore.cache_fetch('country_group_data') do
      country_data = {}
      Country.preload(:states).each do |country|
        country_data[country.name ] = {id: country.id, name: country.name, shipping_multiplier: country.shipping_multiplier,  iso3166_alpha2: country.iso3166_alpha2, express_delivery_charge:  country.express_delivery_charge, ship_time: (country.shipping_time.to_f.nonzero? || (country.name == 'India' ? SHIPPING_TIME : INTERNATIONAL_SHIPPING_TIME)), states: country.states.collect{|s| [s.name, s.iso3166_alpha2]}.to_h}
      end
      country_data
    end
  end

  def self.find_by_namei_cached(name)
    Country.get_country_group.find{|country_name, values| country_name.downcase == name.downcase}.try(:last)
  end

  def self.get_country_code(country)
    RequestStore.cache_fetch("get_iso_alpha_#{country.try(:downcase)}") do
      find_by_namei(country).try(:iso3166_alpha2)
    end
  end

  def self.country_name_by_code
    Rails.cache.fetch(:country_name_by_code_mobile) do
      params = {}
      Country.all.each do |country|
        params[country.iso3166_alpha2] = country.name
      end
      params
    end
  end
end
