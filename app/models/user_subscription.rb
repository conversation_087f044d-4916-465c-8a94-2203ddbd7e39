class UserSubscription < ActiveRecord::Base
  belongs_to :user
  belongs_to :subscription_plan

  validates :user_id, presence: true
  validates :subscription_plan_id, presence: true
  validates :start_date, presence: true
  validates :end_date, presence: true

  before_validation :set_dates, on: :create
  
  scope :active, -> { where(status: 'active').where('end_date >= ?', Date.current) }

  state_machine :status, initial: :active do
    state :active
    state :inactive
    state :cancelled
    state :expired

    event :activate do
      transition :pending => :active
    end

    event :deactivate do
      transition :active => :inactive
    end

    event :cancel do
      transition [:pending, :active] => :cancelled
    end

    event :expire do
      transition :active => :expired
    end
  end
  

  def plan_name
    self.subscription_plan.name
  end

  def membership_description
    "🎉 Congratulations! Your #{plan_name} Membership has waived off the platform fee. Valid till #{end_date.strftime('%d %b %Y')}"
  end

  private

  def self.active?
    active.exists?
  end

  def set_dates
    if self.subscription_plan && self.subscription_plan.plan_duration.present?
      self.start_date = Date.current
      case self.subscription_plan.plan_duration
      when 'daily'
        self.end_date = self.start_date.end_of_day
      when 'weekly'
        self.end_date = self.start_date + 1.week
      when 'monthly'
        self.end_date = self.start_date + 1.month
      when '3_month'
        self.end_date = self.start_date + 3.months
      when '6_month'
        self.end_date = self.start_date + 6.months
      when 'annually'
        self.end_date = self.start_date + 1.year
      end
    end
  end
end