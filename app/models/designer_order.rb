# == Schema Information
#
# Table name: designer_orders
#
#  id                       :integer          not null, primary key
#  email_sent               :boolean
#  sms_sent                 :boolean
#  pickup                   :datetime
#  designer_id              :integer
#  order_id                 :integer
#  created_at               :datetime
#  updated_at               :datetime
#  shipping                 :integer
#  state                    :string(255)
#  total                    :integer
#  payout                   :integer
#  tracking                 :text
#  tracking_num             :string(255)
#  delivered_at             :datetime
#  discount                 :integer
#  designer_payout_status   :string(255)
#  designer_payout_date     :datetime
#  designer_payout_notes    :string(255)
#  tracking_partner         :string(255)
#  item_total               :integer
#  coupon_id                :integer
#  notes                    :text
#  transaction_rate         :integer
#  in_critical_at           :datetime
#  return_designer_order_id :integer
#  package_received_on      :datetime
#  package_received_by      :string(255)
#  package_status           :string(255)      default("packed")
#  confirmed_at             :datetime
#  shipper_id               :integer
#  fedex_serviceable        :boolean
#  shipment_status          :string(255)      default("pending")
#  mirraw_shipping_cost     :integer
#  discount_provided        :integer          default(0)
#  delhivery_serviceable    :boolean
#  ecomexpress_serviceable  :boolean
#  priority_shipper_id      :integer
#  ship_to                  :string(255)
#  city_courier_serviceable :boolean
#  bluedart_serviceable     :boolean
#  dtdc_serviceable         :boolean
#  speedpost_serviceable    :boolean
#  fedex_mirraw_serviceable :boolean
#  shipment_error           :text
#  priority_shipper_cod_id  :integer
#  aramex_serviceable       :boolean
#  rack_code                :string(255)
#  shipment_update          :string(255)      default("none"), not null
#  rack_list_id             :integer
#  replacement              :boolean          default(FALSE)
#

class DesignerOrder < ActiveRecord::Base
  has_paper_trail
  # Attributes that require currency conversion
  # Needs to always be above Priceable
  PRICE_ATTR = [:discount, :scaled_discount]

  include Priceable
  include CouponValidation

  belongs_to :designer
  belongs_to :order
  belongs_to :coupon
  belongs_to :shipper
  has_many :return_designer_orders
  has_many :line_items
  has_one :shipment
  belongs_to :rack_list

  before_save :update_triggers

  TRANSACTION_RATE = SystemConstant.get('TRANSACTION_RATE').to_i
  SHIPPING_CHARGE = SystemConstant.get('SHIPPING_CHARGE').to_i
  MIN_TOTAL_PER_STORE = SystemConstant.get('MIN_TOTAL_PER_STORE').to_i
  DESIGN_ORDER_RETURN_DAYS = SystemConstant.get('DESIGN_ORDER_RETURN_DAYS').to_i

  # Post update operations
  #
  # == Returns:
  # Boolean
  #
  def update_triggers
    build_totals
    add_discount_notes
    true
  end

  def check_stitching_or_jewellery_des_order(items=nil)
    is_stitch = false
    all_items = items || self.line_items
    all_items.each do |item|
      item_design = item.design
      des_ord_type_initial = item_design.get_product_type_initial
      if des_ord_type_initial == 'J-'
        return 'jewellery'
      elsif item.stitching_required == 'Y' || (item.line_item_addons.to_a.present? && item.paid_addons?)
        is_stitch = true
      end
    end
    return 'stitching' if is_stitch
  end


  def set_warehouse_address

    all_dos = self.order.designer_orders
        
    
    if all_dos.count > 1
      address_id = DEFAULT_WAREHOUSE_ADDRESS_ID
    else
      
      is_sor_order = self.line_items.all? {|item| item.available_in_warehouse}
      
      is_inhouse_vendor = INHOUSE_VENDOR_IDS.include? self.designer_id.to_s

      is_order_stitching = self.check_stitching_or_jewellery_des_order

      contains_stitching = is_order_stitching == 'stitching'

      address_id = (is_sor_order || is_inhouse_vendor || contains_stitching) ? DEFAULT_WAREHOUSE_ADDRESS_ID : self.designer.warehouse_address_id
    end
    address_id = DEFAULT_WAREHOUSE_ADDRESS_ID unless address_id.present?


    all_dos.each do |dos|
      dos.assign_attributes(warehouse_address_id: address_id)
    end
  end

  # Payout for designer
  #
  # == Returns:
  # Integer
  #
  def get_payout
    item_total = 0
    self.line_items.each {|item| item_total += item.vendor_selling_price.to_f*item.quantity}
    if item_total > 0
      if (v_discount = self.discount).present?
        (item_total-v_discount)*(100 - self.transaction_rate)/100
      else
        item_total*(100 - self.transaction_rate)/100
      end
    elsif designer.is_transfer_model?
      item_total
    else
      discount_amt = (100 - self.promotion_discount)
      self.total * (100 - self.transaction_rate) / discount_amt
    end
  end

  # Commission percentage
  #
  # == Returns:
  # Integer
  #
  def commision
    if self.designer.blank? || self.designer.transaction_rate.blank?
      TRANSACTION_RATE
    else
      self.order.international? ? self.designer.transaction_rate : self.designer.domestic_transaction_rate
    end
  end

  # Check whether any line item has a paid addon
  #
  # == Returns:
  # Boolean
  #
  def paid_line_item_addons?
    return_value = false
    self.line_items.each do |line_item|
      if line_item.paid_addons?
        return_value = true
        break
      end
    end
    return_value || false
  end

  # Total cost addon - conditionally if payable_to.present?
  #
  # == Parameters:
  #   payable_to::
  #     String
  #
  # == Returns:
  # Integer
  #
  def addons_value_for(payable_to = nil)
    total = 0
    self.line_items.each do |line_item|
      total += line_item.addons_value_for(payable_to)
    end
    total
  end

  # Total without discount
  #
  # == Returns:
  # Integer
  #
  def item_total
    self.total + self.discount
  end

  # Get total value for designer
  #
  # == Returns:
  # Integer
  #
  def designer_item_total
    total = 0
    self.line_items.each do |line_item|
      total += line_item.total_currency(1, 'designer')
    end
    total
  end
  
  def designer_item_scaled_total
   total = 0
   self.line_items.each do |line_item|
     total += line_item.total_currency(1,'scaled_total_for_designer')
   end
   total
  end
  # Proportionally add discount(order & designer order discount) to line items
  #
  # == Parameters:
  #   order::
  #     Order object
  #
  # == Returns:
  # Integer
  #
  def update_line_item_discount_by(order)
    self.discount = 0 if self.discount.blank?

    # Get discount percent based designer order and order discount proportional
    # to designer order total
    discount_percent = self.item_total < 0 ? 0 :
      (self.discount + order.discount_for(self.item_total)) / self.item_total.to_f

    discount = discount_percent > 0 ?
      apply_discount(discount_percent, order.discount_percent) : 0

    DesignerOrder.where(id: self.id).update_all(discount_provided: discount)
  end

  def sale_on_country?(active_promotions, country=nil)
    if (country_code = Promotion.sale_discount_on_country(active_promotions,country)).present?
      country_code.split(',').include?(Design.country_code)
    else
      false
    end
  end

  # Get total weight of items
  #
  # == Returns:
  # Integer
  #
  def weight
    value = 0
    self.line_items.each {|line_item| value += line_item.weight }
    value
  end

  def canceled?
    self.state == 'canceled'
  end

  def returnable?
    self.order.domestic? &&
    self.state.in?(['completed', 'dispatched']) &&
    self.order.confirmed_at.between?(DESIGN_ORDER_RETURN_DAYS.days.ago,Time.now)
  end

  def check_for_valid_return(rdo)
    rdo.errors[:designer_order] << 'Invalid Tracking details' if same_tracking_number_as_dispatched rdo
    rdo.errors[:designer_order] << 'International orders can not be returned' unless self.order.domestic?
    rdo.errors[:designer_order] << 'Designers items not completed or dispatched' unless self.state.in?(['completed', 'dispatched','buyer_returned'])
    rdo.errors[:designer_order] << 'returnable period over!' unless self.order.confirmed_at.present? && self.order.confirmed_at.between?(DESIGN_ORDER_RETURN_DAYS.days.ago,Time.now)
  end

  def same_tracking_number_as_dispatched(rdo)
    if self.tracking_num.present? && self.tracking_partner.present?
      (self.tracking_num.downcase.eql?rdo.tracking_number.downcase if rdo.tracking_number.present?) &&
      (self.tracking_partner.downcase.eql?rdo.tracking_company.downcase if rdo.tracking_company.present?)
    end
  end

  state_machine :invoice_state, initial: :not_uploaded do
    event :designer_uploaded_invoice do
        transition not_uploaded: :invoice_uploaded ,if: lambda {|designer_order| designer_order.state == 'dispatched'}
    end
    event :mark_for_payout do
      transition invoice_uploaded: :completed_from_designer
    end

    event :reject do
      transition [:invoice_uploaded,:completed_from_designer] => :not_uploaded
    end
  end

  state_machine initial: :new do
    state :completed
    state :pending
    state :pickup_done
    state :dispatched
    state :scheduled
    state :canceled
    state :critical
    state :vendor_cancel
    state :rto

    # before_transition :to => :pending do |designer_order|
    #   abort()
    #   designer_order.set_warehouse_address
    # end

    event :force_buyer_return do
      transition all - [:buyer_returned] => :buyer_returned
    end

    before_transition :to => :pending do |designer_order|
      designer_order.set_warehouse_address
      designer_order.cancel_reason = nil
    end

    after_transition to: :buyer_returned do |designer_order|
      Order.sidekiq_delay.convey_status_update_to_designer(designer_order.id)
      ###### Payouts #####
      order = designer_order.order
      if designer_order.designer_payout_status == "paid" || designer_order.designer_payout_status == "processing" || designer_order.designer_payout_notes.present?
        # We have or are already making payment for this order. Create new adjustment entry for the same
        designer_order.designer.adjustments.where(:order => order, :notes => "Refund for #{order.number}", :amount => -1 * designer_order.payout, :status => 'unpaid', designer_order_id: designer_order.id).first_or_create
      end
      if (mirraw_cost = designer_order.mirraw_shipping_cost.to_i).present? && mirraw_cost > 0
        Adjustment.where(amount: -1 * mirraw_cost,designer_id: designer_order.designer.id,
          order_id: designer_order.order_id, designer_order_id: designer_order.id).first_or_create(notes: "Shipping Cost for Order #{order.number}",status: 'unpaid')
      end
      # ReverseCommission.sidekiq_delay(queue: 'high').add_reverse_commission(designer_order) if designer_order.invoice_id.present?
      DesignerOrder.sidekiq_delay(queue: 'high').enqueue_job("ReverseCommission", nil, "add_reverse_commission", {designer_order.class.to_s => designer_order.id}) if designer_order.invoice_id.present?
    end
  end

  def add_notes(note, to_save, current_account = nil)
    first = current_account.nil? ? 'System' : current_account.email.split('@')[0]
    note_content = "#{Date.today.strftime("%m/%d")} : #{first} : #{note}"
    self.notes = if self[:notes].blank?
      ''
    else
      self[:notes] + ' ... '
    end
    self.notes = self[:notes] + note_content
    self.save if to_save
  end


  def scaled_discount
    if self[:scaled_discount].present?
      self[:scaled_discount]
    elsif discount.present?
      discount
    else
      0
    end
  end

  def order_status
    states = {
      'pending' => 'order_confirmed',
      'pickedup' => 'order_picked_up',
      'dispatched' => 'order_dispatched',
      'completed' => 'order_dispatched',
      'delivered' => 'order_delivered',
      'canceled' => 'order_cancelled'
    }
    status = { 'order_placed' => 'Order Placed' }
    if state == 'canceled'
      status.merge!(get_states(states[state], status))
    elsif (shipment = self.shipment).present? && shipment.shipment_state == 'delivered'
      status.merge!(get_states(states['delivered'], status))
    else
      status.merge!(get_states(states[state], status))
    end
    unless order.cod?
      status.slice!(*['order_placed', 'order_confirmed', 'order_dispatched'])
      status['order_dispatched'] = 'Your Order will be Dispatched shortly.' if status['order_dispatched'].nil? && status['order_confirmed'] == 'Order Confirmed'
    end
    status
  end

  private

  # Add discount to each line item
  #
  # == Parameters:
  #   discount_percent::
  #     Integer
  #   order_discount_percent::
  #     Integer
  #
  # == Returns:
  # Integer
  #
  def apply_discount(discount_percent, order_discount_percent)
    discount_value = 0
    self.line_items.each do |line_item|
      discount_value += line_item.apply_discount(
        discount_percent, order_discount_percent)
      line_item.save
    end
    discount_value
  end


  # Calculate various totals
  #
  def build_totals
    self.total = 0
    self.discount = 0 if self.discount.blank?
    unless self.canceled?
      self.total = self.designer_item_total - self.discount
      self.scaled_total = self.designer_item_scaled_total - self.scaled_discount
      self.total = 0 if self.total < 0
    end

    self.shipping = (self.total > MIN_TOTAL_PER_STORE || self.total <= 0) ? 0 :
      SHIPPING_CHARGE
    if self.designer_payout_status != 'paid' && self.designer_payout_notes.blank?
      self.transaction_rate = self.commision if self.transaction_rate.blank?

      self.mirraw_shipping_cost = 0 if self.mirraw_shipping_cost.blank?
      self.payout = self.get_payout - self.mirraw_shipping_cost
    end
  end

  def add_discount_notes
    if is_on_global_sale_period?(Design.country_code)
      note_text = 'Mirraw Discount Applied'
      if (self[:notes] =~ /#{note_text}/).blank?
        self.add_notes(note_text, false)
      end
    end
  end

  def is_on_global_sale_period?(country=nil)
    promotion_pipeline = PromotionPipeLine.active_promotions
    if (country_code = Promotion.sale_discount_on_country(promotion_pipeline,country)).present?
      country_code.split(',').include?(Design.country_code)
    else
      false
    end
  end

  # Set ship to
  #
  # == Return:
  # String
  #
  def set_ship_to
    self.ship_to = self.order.international? || self.mirraw_payable_addons? ?
      'mirraw' : 'customer'
  end

  def get_notes(state, position = nil)
    completed_states = ['Order Confirmed', "Order PickedUp", "Order Dispatched", "Order Delivered"]
    case state
    when 'completed'
      completed_states[position]
    when 'processing'
      "Your Order will be #{completed_states[position].split(' ')[1]} shortly."
    when 'cancelled'
      'Your order has been canceled. You can try again by visiting the link emailed to you.'
    end
  end

  def get_states(state, status)
    states = ['order_confirmed', 'order_picked_up' , 'order_dispatched', 'order_delivered']
    case state
    when 'order_cancelled'
      status.merge!({
        'order_confirmed' => get_notes('cancelled')
      })
    when 'order_delivered'
      states.each_with_index do |s,i|
        status.merge!({ s => get_notes('completed', i)})
      end
    when 'order_confirmed', 'order_picked_up', 'order_dispatched'
      states.each_with_index do |s,i|
        status.merge!({ s => get_notes('processing', i)}) and break if status[state]
        status.merge!({ s => get_notes('completed', i)})
      end
    else
      status.merge!({
        'order_confirmed' => get_notes('processing', 0)
      })
    end
    states.each{ |s| status[s] ||= nil }
    status
  end
end
