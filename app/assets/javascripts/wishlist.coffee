WishlistForm = undefined

WishlistForm = (form) ->
  @$form = $(form)
  @$submitButton = $('button[type=submit]', @$form)
  @$errorField = @$form.siblings('.wishlist-error')
  @$wishlistLoader = @$form.siblings('.wishlist-loader')
  @action = @$form.prop('action')
  @isAddToWishlist = @$form.hasClass('new_wishlist')
  @$sibling = if @isAddToWishlist then @$form.siblings('.delete_wishlist') else @$form.siblings('.new_wishlist')
  @$siblingSubmitButton = $('button[type=submit]', @$sibling)
  @switchingForms = false
  @submitingForm = false
  @error = undefined

  @showSibling = ->
    form = this
    @startTransition('switchingForms')
    @$form.fadeOut 200, ()->
      form.$sibling.fadeIn 200, ()->
        form.completeTransition('switchingForms')

  @showSelf = ->
    @$sibling.hide()
    @$form.show()

  @askSignIn = ->
    window.location = '/accounts/sign_in'

  @disableButtons = ->
    @$submitButton.prop 'disabled', true
    @$siblingSubmitButton.prop 'disabled', true

  @enableButtons = ->
    if !@inTransition()
      @$submitButton.prop 'disabled', false
      @$siblingSubmitButton.prop 'disabled', false

  @serialize = ->
    @$form.serialize()

  @beforSubmitForm = ->
    @startTransition('submitingForm')
    @showSibling()

  @completeSubmitForm = (error)->
    @error = error
    @completeTransition('submitingForm')

  @submitForm = ()->
    form = this
    if signedIn()
      $.ajax
        type: 'POST'
        url: @action
        dataType: 'JSON'
        data: @serialize()
        beforeSend: ->
          form.beforSubmitForm()
        success: (data, status, jqxhr)->
          error = if form.isAddToWishlist then data.error else null
          form.completeSubmitForm(error)
        error: ()->
          form.completeSubmitForm('something went wrong')
        statusCode:
          401: ()->
            form.askSignIn()
    else
      form.askSignIn()

  @startTransition = (medium)->
    if medium == 'switchingForms'
      @switchingForms = true
    else if medium == 'submitingForm'
      @submitingForm = true

    @disableButtons()

  @completeTransition = (medium)->
    if medium == 'switchingForms'
      @switchingForms = false
    else if medium == 'submitingForm'
      @submitingForm = false

    @showSelf() if @error
    @enableButtons()

  @inTransition = ->
    @switchingForms || @submitingForm

  return

$(document).on 'submit', '.new_wishlist', (e)->
  e.preventDefault()

  form = new WishlistForm(this)
  form.submitForm();


$(document).on 'submit', '.delete_wishlist', (e)->
  e.preventDefault()

  form = new WishlistForm(this)
  form.submitForm()

$ ->

window.enableWishList = (designIdList)->
  designIds = design_ids: designIdList || $.map($('.wishlist-forms.hide'), (val) ->
    val.dataset.designId
  ).join(',')

  if signedIn() && designIds.design_ids.length > 0
    $.get '/user/wishlists/filter', designIds, (data) ->
      showWishlistForms data.wishlist_design_ids
      return
    return
  else
    showWishlistForms([])

showWishlistForms = (wishlistedDesignIds) ->
  if wishlistedDesignIds and wishlistedDesignIds.length > 0
    hideAddWishlist wishlistedDesignIds
    showRemoveWishlist wishlistedDesignIds
  $('.wishlist-forms').removeClass 'hide'
  return

hideAddWishlist = (designIds) ->
  $.each designIds, (index, id) ->
    $('#design_' + id + '_new_wishlist').hide()
    return
  return

showRemoveWishlist = (designIds) ->
  $.each designIds, (index, id) ->
    $('#design_' + id + '_delete_wishlist').show()
    return
  return

document.addEventListener "turbolinks:load", ()->
  enableWishList()


