"use strict";
var root, observer;
root = typeof exports !== "undefined" && exports !== null ? exports : this;
function preloadImage(img, src_type){
  img.setAttribute(src_type, img.dataset.original);
  if(src_type == 'srcset'){
    img.classList.remove('js-lazy');
  }
  img.onload = function() {
    img.classList.remove('js-lazy');
  };
}

root.lazyLoad = function(){
  if (!('IntersectionObserver' in window)) {
    if (navigator.userAgent.indexOf('Opera') == -1){
      if (typeof ($(window).inView) === 'function') {
        loadInViewImage();
      } else {
        loadScript("<%= asset_url('inview.min.js') %>", function(){
          loadInViewImage()
        });
      }
    } else{
      images = document.querySelectorAll('.js-lazy');
      for (var i = 0, len = images.length; i < len; i++) {
        preloadImage(images[i], 'src');
      };
    }
  } else{
    var images = document.querySelectorAll('.js-lazy')
    var config = {
      rootMargin: '500px 0px 0px 0px',
      threshold: 0.1
    }
    observer = new IntersectionObserver(onIntersection, config);
    images.forEach( function(img){
      observer.observe(img);
    });
  }
}

function loadInViewImage(){
  inView.offset({
    top: -50,
    right: -10,
    bottom: 0,
    left: -10
  });
  inView('.js-lazy').on('enter', function(element){
    preloadImage(element);
  });
}

function onIntersection(entries){
  entries.forEach( function(entry){
    if (entry.intersectionRatio > 0){
      observer.unobserve(entry.target);
      if(!!entry.target.srcset){
        preloadImage(entry.target, 'srcset');
      }
      else{
        preloadImage(entry.target, 'src');
      }
    }
  });
}
function loadAllLazyImages() 
{ 
  var images = document.querySelectorAll('.design_image');
    images.forEach(function(image) { 
      if (image.classList.contains('js-lazy')){
        preloadImage(image, image.srcset ? 'srcset' : 'src');
      }
    });
}