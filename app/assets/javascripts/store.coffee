//= require  'catalog/store.js'
$(document).on 'click', 'div#toggle-design-box', ->
  if $('#toggle-design-icon').hasClass('fi-list')
    $('#toggle-design-icon').removeClass('fi-list').addClass 'fi-thumbnails'
    $('ul.store_page_design').removeClass('small-block-grid-2').addClass 'small-block-grid-1'
    # ga 'send', 'event', 'DesignToggle', 'click', 'One-Design-View'
  else
    $('#toggle-design-icon').removeClass('fi-thumbnails').addClass 'fi-list'
    $('ul.store_page_design').removeClass('small-block-grid-1').addClass 'small-block-grid-2'
    # ga 'send', 'event', 'DesignToggle', 'click', 'Two-Design-View'
  return

redirectToBannerURL = (event) ->
  window.location = event.currentTarget.attributes.bannerurl.value

if typeof Turbolinks != 'undefined'
  $(".mp-carousel-card").click redirectToBannerURL
  
document.addEventListener 'turbolinks:load', ->
  $(".mp-carousel-card").click redirectToBannerURL


# $ ->
  # if $('#uuid').length == 1
    # ga('set', 'dimension11', $('#uuid').val());

nextPageUrl = ->
  current_page = $(".current_page").data().page;
  if $('.next') && (!($('.next').attr('id')))
    $('.next').attr('id', 'page_' + current_page+1)
  nextPage = $('.next').attr('id').split('_')[1]
  if window.location.href.indexOf('?') == -1
    nextUrl = window.location.href + '?more_designs=true&page=' + nextPage
  else
    nextUrl = window.location.href.replace(/&?((pid=)|(page=))[^\&]*/, '') + '&more_designs=true&page=' + nextPage
  nextUrl

$(document).on 'click', 'div#load-more-designs-btn', ->
  nextUrl = nextPageUrl()
  getDesignsData nextUrl
  return

getMoreDesigns = ->
  $(window).on 'scroll', ->
    static_header_scroll()
    buttonTop = $('.navigate_page').position().top if $('.navigate_page').length > 0
    if $('.next').length > 0 and ($('.catalog_product').length < 200) and (buttonTop > $(window).scrollTop())
      nextUrl = nextPageUrl()
      if nextUrl and (buttonTop - $(window).scrollTop() < 1100)
        getDesignsData nextUrl
    else if $('.next').length > 0 and $('.catalog_product').length >= 200
      $('.next, .previous').hide()
      $('#load-more-designs-btn').css 'display', 'inline-block'
    if $(this).scrollTop() > 400
      $('#back-top').fadeIn()
    else
      $('#back-top').fadeOut()
    getFooterPng()
    return
  return
$ ->
  isDesktop = ->
    $(window).width() > 768 
  $(document).on 'click', '.design-detail-link', (e) ->
    e.preventDefault()
    url = $(this).attr('href')
    currentPage = $(".page-delimeters").data('page_number')
    scrollTop = $(window).scrollTop()
    if isDesktop()
      window.open url, '_blank'
    else
      if Turbolinks? and Turbolinks.supported
        Turbolinks.visit url

# uncomment if scroll-spy needed on design load
# getCurrentUrl = (pageNo) ->
#   currentUrl = window.location.search
#   if (currentUrl.length > 0)
#     if(currentUrl.indexOf('page=') == -1)
#       pageUrl = currentUrl.concat('&page='+pageNo);
#     else
#       pageUrl = currentUrl.replace(/(page=)[\d]+/, '$1'+pageNo);
#   else
#     pageUrl = currentUrl.concat('?page='+pageNo);
#   return pageUrl
loadedDesigns = {}
removedFirstHalf = false
getDesignsData = (nextUrl) ->
  $.ajax
    type: 'GET'
    url: nextUrl
    dataType: 'script'
    beforeSend: ->
      $('#load-more-designs-btn, .next, .previous').hide()
      $('#more-designs-loader').show()
      static_header_scroll()
      $(window).off 'scroll'
      return
    complete: ->
      if $('.catalog_product').length >= 200
        $('#load-more-designs-btn').css 'display', 'inline-block'
      else
        $('.next, .previous').show()
      # uncomment if scroll-spy needed on design load
      # if(window.history && window.history.pushState)
      # history.pushState(null, null, null);
      # pageNo = $('.current_page').data('page');
      # currentUrl = window.location.search
      # if(currentUrl.length > 0)
      #   if(currentUrl.indexOf('page=') == -1)
      #     pageUrl = currentUrl.concat('&page='+pageNo);
      #   else
      #     pageUrl = currentUrl.replace(/(page=)[\d]+/, '$1'+pageNo);
      # else
      #   pageUrl = currentUrl.concat('?page='+pageNo);
      # pageUrl = getCurrentUrl(pageNo);
      $('#more-designs-loader').hide()
      gaImpressions()
      gaPageview(false)
      getMoreDesigns()
      applyCTimer()
      lazyLoad()
      makeDealTimerSticky()
      window.enableWishList()
      return
    success: (response) ->
      searchInput = document.getElementById('search_input')
      unless searchInput?.value.trim().length > 0
        designContainers = document.querySelectorAll('.ga_design_container')
        ga4_catalog_params = []
        item_ids = []
        for container in designContainers
          gaDataValue = container.getAttribute('data-ga-data')
          parsedData = JSON.parse(gaDataValue)
          if parsedData == null
            continue

          if (!loadedDesigns[parsedData.item_id])
            loadedDesigns[parsedData.item_id] = true; 
            simplifiedData =
              item_id: parsedData.item_id
              item_name: parsedData.item_name
              price: parsedData.price
              discount: parsedData.discount
              index: parsedData.index
              item_category: parsedData.item_category
              item_category2: parsedData.item_category2
              item_category3: parsedData.item_category3
              item_category4: parsedData.item_category4
              item_category5: parsedData.item_category5
              item_variant: parsedData.item_variant
              item_brand: parsedData.item_brand
              quantity: 1
              item_list_name: parsedData.item_list_name
              item_list_id: parsedData.item_list_id

            ga4_catalog_params.push(simplifiedData)
            itemIds = 
              id: parsedData.item_id
              google_business_vertical: 'retail'
            item_ids.push(itemIds)
        unless removedFirstHalf
          halfLength = Math.ceil(ga4_catalog_params.length / 2)
          ga4_catalog_params.splice(0, halfLength)
          halfItemIds = Math.ceil(item_ids.length/2)
          item_ids.splice(0,halfItemIds)
          removedFirstHalf = true
        if parsedData != null  
          dataLayer.push({
            event: "ga4_view_item_list"
            ecommerce: {
              country_code: parsedData.country_code
              item_list_name: parsedData.main_category
              item_list_id: parsedData.main_category_id
              items: ga4_catalog_params,
              item_ids: item_ids
            }
          })
      response
      return
    error: (xhr, status, error) ->
  return
  


# uncomment if scroll-spy needed on design load
# $(document).scroll ->
#   currentHash = window.location.search
#   $('.page-delimeters').each ->
#     top = parseInt(window.pageYOffset)
#     currentTop = top - parseInt($(this).offset().top)
#     hash = getCurrentUrl($(this).data('page-number'))
#     if(((currentTop < 50 && currentTop > -50) || ($(this).index() == 0 && top == 0)) && currentHash != hash)
#       if(window.history && window.history.pushState)
#         pageUrl = getCurrentUrl($(this).data('page-number'))
#         history.pushState(null, null, null)
#       currentHash = hash

$ ->
  $(document).on 'click', 'div#back-top', ->
    static_header_scroll()
    $('body,html').animate { scrollTop: 0 }, 500
    return
  getMoreDesigns()
  return
afterWindowOrTrubolinksLoad ->
  $(window).off 'scroll'
  makeDealTimerSticky()
  if $('.pages_home').length
    getMoreItems()
  else
    getMoreDesigns()

$(document).on 'change', '.select_box', ->
  selectedValue = $('.form_input_m_select option:selected').val()
  if window.location.href.indexOf('?') == -1
    url = window.location.href + '?sort=' + selectedValue
  else
    url = window.location.href.replace(/&?sort=[^\&]+/, '') + '&sort=' + selectedValue
  if Turbolinks.supported
    Turbolinks.visit url
  else
    window.location.assign url

$(document).on 'change', '.sort-by-wrap', ->
  selectedValue = $('.select_sort_value option:selected').val()
  if window.location.href.indexOf('?') == -1
    url = window.location.href + '?sort=' + selectedValue
  else
    url = window.location.href.replace(/&?sort=[^\&]+/, '') + '&sort=' + selectedValue
  if Turbolinks.supported
    Turbolinks.visit url
  else
    window.location.assign url


$(document).on 'click', '#filter-button', ->
  $('body').addClass 'modal-open'
  $('.reveal-modal-bg').css 'display', 'block'
  return

$(document).foundation 'reveal', animation: false

$(document).on 'click', '.tab-filter-fix', (event) ->
  event.preventDefault()
  url = $(this).attr('href')
  $('nav > .tab-filter-fix').removeClass 'active-tab'
  $(this).addClass 'active-tab'
  $('.tabs-content > .content').removeClass 'active'
  $(url).addClass 'active'
  return

searchTag = (url) ->
  # Getting Selected Tags/Search term
  if window.location.href.indexOf('/tags?q=') != -1
    url += '?q=' + $('#tag_list').val()
  else if window.location.href.indexOf('/search?utf8') != -1
    url += '?' + $('.search_margin form').serialize()
  url

window.checkForColourInUrl = (url) ->
  if (index = url.indexOf('/colour')) != -1
    url = url.substr(0, index)
  url

appendFilters = (url) ->
  category_child_ids = []
  designer_ids = []
  colour_names = []
  colour_ids = []
  gender = []
  imp_properties = []
  property_value_ids = []
  option_type_value_ids = []
  ready_to_ship = []
  price_discount = ''
  hash = {}
  category_name = $('#facet-data').data('category-name')
  link = window.location.href.slice(window.location.href.indexOf('?') + 1)
  $('.on-off-radiobox input:radio:checked, .on-off-checkbox input:checkbox:checked').each ->
    property = $(this).data('propertyKey')
    if property != undefined
      if hash.hasOwnProperty(property)
        hash[property]['value'].push($(this).val())
      else
        hash[property] = {'value': [$(this).val()], 'type': $(this).data('returnType')}
    switch $(this).data('key')
      when 'ready_to_ship'
        ready_to_ship.push $(this).val()
      when 'category_child_ids[]'
        category_child_ids.push $(this).val()
      when 'designer_ids[]'
        designer_ids.push $(this).val()
      when 'property_value_ids[]'
        if category_name == undefined
          if $(this).attr('class').match(/color-append/g)
            colour_names.push $(this).attr('placeholder')
            colour_ids.push $(this).val()
          else
            property_value_ids.push $(this).val()
            break
        else
          priority = $(this).attr('data-priority')
          name = $(this).data('name')
          if priority > 0
            is_duplicate = imp_properties.some((ele) ->
              ele.name == name
            )
            if !is_duplicate
              property =  {
                            name: name
                            priority: priority
                          }
              imp_properties.push(property)
          else
            property_value_ids.push $(this).val()
      when 'option_type_value_ids[]'
        option_type_value_ids.push $(this).val()
      when 'gender'
        gender.push $(this).val()
    return
  $('.hidden-facet-data').each ->
    priority = $(this).attr('data-priority')
    name = $(this).data('name')
    if priority > 0
      is_duplicate = imp_properties.some((ele) ->
        ele.name == name
      )
      if !is_duplicate
        property =  {
                      name: $(this).data('name')
                      priority: $(this).data('priority')
                    }
        imp_properties.push(property)
  $('input:hidden.range_filter_input').each ->
    if $(this).val() != ''
      price_discount += '&' + $(this).attr('id') + '=' + $(this).val()
    return
  url = searchTag(url)
  if category_name == undefined
    if colour_names.length != 0
      if url.indexOf('?') == -1
        url += '/colour-'
      else
        url += '&colour-'
        property_value_ids = property_value_ids.concat(colour_ids)
    url += colour_names.join('--')
  else
    imp_properties.sort (a,b) ->
      if a['priority'] > b['priority']
        value = 1
      else if a['priority'] == b['priority']
        if a['name'] >= b['name']
          value = 1
        else
          value = -1
      else
        value = -1
      return value
    if url[url.length - 1] != '/' && imp_properties.length > 0
      url += '/'
    if imp_properties.length > 0
      for property in imp_properties
        url += property['name'] + '_'
      url = url[0..(url.length - 2)] + '-'
      url += category_name
  if ready_to_ship.length != 0
    url += '&ready_to_ship=' + ready_to_ship
  # Getting Selected Price/Discount
  if price_discount != ''
    url += price_discount
  # Getting Selected CategoryIds
  if category_child_ids.length != 0
    url += '&category_child_ids=' + category_child_ids.join(',')
  # Getting Selected DesignerIds
  if designer_ids.length != 0
    url += '&designer_ids=' + designer_ids.join(',')
  if property_value_ids.length > 0
    url += '&property_value_ids=' + property_value_ids
  # Getting Selected OptionTypeValues
  if option_type_value_ids.length != 0
    url += '&option_type_value_ids=' + option_type_value_ids.join(',')
  if gender.length != 0
    url += '&gender=' + gender
  # Getting Selected Sort if present
  if window.location.href.indexOf('sort=') != -1
    selectSort = $('#sort').val()
    url += '&sort=' + selectSort
  if (max_odr = $('#max-odr-parameter').data('max-odr')) != undefined
    url += '&max_odr=' + max_odr
  if (preference = getUrlParams(link).preference) != undefined
    url += "&preference=" + preference
  if Object.keys(hash).length > 0
    url += "&facet=" + encodeURIComponent(JSON.stringify(hash))
  url
  
window.appendFiltersDesktop = (url) ->
  category_child_ids = []
  designer_ids = []
  colour_names = []
  colour_ids = []
  gender = []
  imp_properties = []
  property_value_ids = []
  option_type_value_ids = []
  ready_to_ship = []
  price_discount = ''
  hash = {}
  category_name = $('#facet-data').data('category-name')
  link = window.location.href.slice(window.location.href.indexOf('?') + 1)
  $('.switch-desk input:radio:checked, .switch-desk input:checkbox:checked').each ->
    property = $(this).data('propertyKey')
    if property != undefined
      if hash.hasOwnProperty(property)
        hash[property]['value'].push($(this).val())
      else
        hash[property] = {'value': [$(this).val()], 'type': $(this).data('returnType')}
    switch $(this).data('key')
      when 'ready_to_ship'
        ready_to_ship.push $(this).val()
      when 'category_child_ids[]'
        category_child_ids.push $(this).val()
      when 'designer_ids[]'
        designer_ids.push $(this).val()
      when 'property_value_ids[]'
        if category_name == undefined
          if $(this).attr('class').match(/color-append/g)
            colour_names.push $(this).attr('placeholder')
            colour_ids.push $(this).val()
          else
            property_value_ids.push $(this).val()
            break
        else
          priority = $(this).attr('data-priority')
          name = $(this).data('name')
          if priority > 0
            is_duplicate = imp_properties.some((ele) ->
              ele.name == name
            )
            if !is_duplicate
              property =  {
                            name: name
                            priority: priority
                          }
              imp_properties.push(property)
          else
            property_value_ids.push $(this).val()
      when 'option_type_value_ids[]'
        option_type_value_ids.push $(this).val()
      when 'gender'
        gender.push $(this).val()
    return
  $('.hidden-facet-data').each ->
    priority = $(this).attr('data-priority')
    name = $(this).data('name')
    if priority > 0
      is_duplicate = imp_properties.some((ele) ->
        ele.name == name
      )
      if !is_duplicate
        property =  {
                      name: $(this).data('name')
                      priority: $(this).data('priority')
                    }
        imp_properties.push(property)
  $('input:hidden.range_filter_input').each ->
    if $(this).val() != ''
      price_discount += '&' + $(this).attr('id') + '=' + $(this).val()
    return
  url = searchTag(url)
  if category_name == undefined
    if colour_names.length != 0
      if url.indexOf('?') == -1
        url += '/colour-'
      else
        url += '&colour-'
        property_value_ids = property_value_ids.concat(colour_ids)
    url += colour_names.join('--')
  else
    imp_properties.sort (a,b) ->
      if a['priority'] > b['priority']
        value = 1
      else if a['priority'] == b['priority']
        if a['name'] >= b['name']
          value = 1
        else
          value = -1
      else
        value = -1
      return value
    if url[url.length - 1] != '/' && imp_properties.length > 0
      url += '/'
    if imp_properties.length > 0
      for property in imp_properties
        url += property['name'] + '_'
      url = url[0..(url.length - 2)] + '-'
      url += category_name

  if ready_to_ship.length != 0
    url += '&ready_to_ship=' + ready_to_ship
  # Getting Selected Price/Discount
  if price_discount != ''
    url += price_discount
  # Getting Selected CategoryIds
  if category_child_ids.length != 0
    url += '&category_child_ids=' + category_child_ids.join(',')
  # Getting Selected DesignerIds
  if designer_ids.length != 0
    url += '&designer_ids=' + designer_ids.join(',')
  if property_value_ids.length > 0
    url += '&property_value_ids=' + property_value_ids
  # Getting Selected OptionTypeValues
  if option_type_value_ids.length != 0
    url += '&option_type_value_ids=' + option_type_value_ids.join(',')
  if gender.length != 0
    url += '&gender=' + gender
  # Getting Selected Sort if present
  if window.location.href.indexOf('sort=') != -1
    selectSort = $('#sort').val()
    url += '&sort=' + selectSort
  if (max_odr = $('#max-odr-parameter').data('max-odr')) != undefined
    url += '&max_odr=' + max_odr
  if (preference = getUrlParams(link).preference) != undefined
    url += "&preference=" + preference
  if Object.keys(hash).length > 0
    url += "&facet=" + encodeURIComponent(JSON.stringify(hash))
  url

appendChips = ->
  $('.on-off-radiobox input:radio:checked, .switch-desk input:checkbox:checked').each ->
    id = $(this).attr('id')
    if $(this).is(':checked')
      excluded_facets = ['availability', 'discount', 'rating', 'carat']
      if ($('#chip-'+id).length == 0) && $(this).attr('name') not in excluded_facets
        chip_id = 'chip-' + id
        chip = "<div class='chip chip-filter' id='" + chip_id + "'>" + $(this).data('chip') + "<span class='remove-filter closebtn' data-remove='" + id + "'>&#10006;</span></div>"
        $('#filter-chips').prepend chip
    else
      if $('#chip-'+id).length > 0
        $('#chip-'+id).remove()
  if $('.chip').length == 0
    $('#clear-all-btn').css('display','none')
  else
    $('#clear-all-btn').css('display','inline-block')
    
$(document).on 'click', '.clear_all_filters', ->
  $('.switch-desk input:radio:checked, .switch-desk input:checkbox:checked').each ->
    return $(this).prop 'checked', false
  $('#max_price').removeAttr 'value'
  $('#min_price').removeAttr 'value'
  $('#max_discount').removeAttr 'value'
  $('#max_discount').removeAttr 'value'
  $('#gender').removeAttr 'value'
  $('body').removeClass 'modal-open'
  url = $('#facet-data').attr('data-url')
  url = searchTag(checkForColourInUrl(url))
  if Turbolinks.supported
    return Turbolinks.visit url
  else
    return window.location.assign url
  
  
$(document).on 'click', 'span.remove-filter', ->
  id = $(this).data('remove')
  element = $("#"+id)
  element.prop('checked', false)
  remove_hidden_range_filter_input(element.attr('data-max'))
  remove_hidden_range_filter_input(element.attr('data-min'))
  updateSelectedFiltersDesktop()
  $('.switch-desk input:radio:checked, .switch-desk input:checkbox:checked').each ->
    id = $(this).attr('id')
    chip = '<div class=\'chip\'>' + $(this).data('chip') + '<span class=\'closebtn\' onclick=\'removeChips(this);\' data-remove=\'' + id + '\'>&times;</span></div>'
    $('#designable_details > .chips_maker').append chip
  $('.switch-desk input:radio:checked').each ->
    $('#' + $(this).data('minInput')).val $(this).data('min')
    $('#' + $(this).data('maxInput')).val $(this).data('max')
    $('#' + $(this).data('gender')).val $(this).data('gender')
    return
  url = ''
  valueString = appendFiltersDesktop(url)
  if valueString != '' and valueString.indexOf('?') == -1
    valueString = valueString.replace('&', '?')
  correct_url = checkForColourInUrl($('#facet-data').attr('data-url'))
  correct_url += valueString
  if Turbolinks.supported
    Turbolinks.visit correct_url
  else
    window.location.assign correct_url

window.remove_hidden_range_filter_input = (value) -> 
  $('input:hidden.range_filter_input').each ->
    if $(this).val() != '' and $(this).val() == value
      $(this).val('')
    return

keepSelectedPropertyActive = ->
  selected_property = window.sessionStorage.getItem('last-filter')
  selected_property = 'price' unless $('#'+selected_property).length > 0
  selected_tab = $('#'+selected_property).attr('href')
  $('nav > .tab-filter-fix').removeClass 'active-tab'
  $('#'+selected_property).addClass 'active-tab'
  $('.tabs-content > .content').removeClass('active').addClass('content')
  $(selected_tab).addClass 'content active'

updateSelectedFilters = ->
  $('.on-off-radiobox input:radio:checked').each ->
    $('#' + $(this).data('minInput')).val $(this).data('min')
    $('#' + $(this).data('maxInput')).val $(this).data('max')
    $('#' + $(this).data('gender')).val $(this).data('gender')
    return
  url = ''
  valueString = appendFilters(url)
  if valueString != '' and valueString.indexOf('?') == -1
    valueString = valueString.replace('&', '?')
  correct_url = checkForColourInUrl($('#facet-data').attr('data-url'))
  correct_url += valueString
  $.ajax
    type: 'GET'
    url: correct_url
    dataType: 'script'
    beforeSend: ->
      $('#designable_details').css 'opacity', '0.5'
      $('#loader').show()
      return
    complete: ->
      $('#loader').hide()
      $('#designable_details').css 'opacity', '1'
      appendChips()
      return
    success: (response) ->
      response
      keepSelectedPropertyActive()
      return
    error: (xhr, status, error) ->
      alert 'oops! Something went wrong'
      return
  return
  
window.updateSelectedFiltersDesktop = ->
  $('.switch-desk input:radio:checked').each ->
    $('#' + $(this).data('minInput')).val $(this).data('min')
    $('#' + $(this).data('maxInput')).val $(this).data('max')
    $('#' + $(this).data('gender')).val $(this).data('gender')
    return
  url = ''
  valueString = appendFiltersDesktop(url)
  if valueString != '' and valueString.indexOf('?') == -1
    valueString = valueString.replace('&', '?')
  correct_url = checkForColourInUrl($('#facet-data').attr('data-url'))
  correct_url += valueString
  $.ajax
    type: 'GET'
    url: correct_url
    dataType: 'script'
    beforeSend: ->
      $('#designable_details').css 'opacity', '0.5'
      $('#loader').show()
      return
    complete: ->
      $('#loader').hide()
      $('#designable_details').css 'opacity', '1'
      return
    success: (response) ->
      response
      keepSelectedPropertyActive()
      return
    error: (xhr, status, error) ->
      alert 'oops! Something went wrong'
      return
  return

appendChips()

$(document).on 'click', '.facet-link, .facet-gen', (e) ->
  e.preventDefault()
  selected_button = $(this).children()[0].firstElementChild
  selected_property = $(selected_button).data('property')
  current_value = $(selected_button).prop('checked')
  $(selected_button).prop('checked', !current_value)
  remove_hidden_range_filter_input($(selected_button).attr('data-min'))
  remove_hidden_range_filter_input($(selected_button).attr('data-max'))
  window.sessionStorage.setItem('last-filter', selected_property.split(' ').join('-'))
  return updateSelectedFilters()

removeChips = (id) ->
  id = $(id).data('remove')
  $('#' + id).prop 'checked', false
  updateSelectedFilters()
  return

$(document).on 'click', '#filter-apply-btn', ->
  url = ''
  valueString = appendFilters(url)
  if valueString != '' and valueString.indexOf('?') == -1
    valueString = valueString.replace('&', '?')
  correct_url = checkForColourInUrl($('#facet-data').attr('data-url'))
  correct_url += valueString
  if Turbolinks.supported
    Turbolinks.visit correct_url
  else
    window.location.assign correct_url

$(document).on 'click', '.facet-link-desktop label', (e) ->
  e.preventDefault()
  selected_button = $(this).closest('div').find('input')
  selected_property = $(selected_button).data('property')
  current_value = $(selected_button).prop('checked')
  $(selected_button).prop('checked', !current_value)
  window.sessionStorage.setItem('last-filter', selected_property.split(' ').join('-'))
  updateSelectedFiltersDesktop()
  $('.switch-desk input:radio:checked, .switch-desk input:checkbox:checked').each ->
    id = $(this).attr('id')
    chip = '<div class=\'chip\'>' + $(this).data('chip') + '<span class=\'closebtn\' onclick=\'removeChips(this);\' data-remove=\'' + id + '\'>&times;</span></div>'
    $('#designable_details > .chips_maker').append chip
  $('.on-off-radiobox input:radio:checked').each ->
    $('#' + $(this).data('minInput')).val $(this).data('min')
    $('#' + $(this).data('maxInput')).val $(this).data('max')
    $('#' + $(this).data('gender')).val $(this).data('gender')
    return
  url = ''
  valueString = appendFiltersDesktop(url)
  if valueString != '' and valueString.indexOf('?') == -1
    valueString = valueString.replace('&', '?')
  correct_url = checkForColourInUrl($('#facet-data').attr('data-url'))
  correct_url += valueString
  if Turbolinks.supported
    Turbolinks.visit correct_url
  else
    window.location.assign correct_url

$(document).on 'click', '.facet-link-desktop input', (e) ->
  e.preventDefault()
  selected_button = $(this)
  selected_property = $(selected_button).data('property')
  current_value = $(selected_button).prop('checked')
  $(selected_button).prop('checked', !!current_value)
  window.sessionStorage.setItem('last-filter', selected_property.split(' ').join('-'))
  updateSelectedFiltersDesktop()
  $('.switch-desk input:radio:checked, .switch-desk input:checkbox:checked').each ->
    id = $(this).attr('id')
    chip = '<div class=\'chip\'>' + $(this).data('chip') + '<span class=\'closebtn\' onclick=\'removeChips(this);\' data-remove=\'' + id + '\'>&times;</span></div>'
    $('.category_links > .chips_maker').append chip
  $('.on-off-radiobox input:radio:checked').each ->
    $('#' + $(this).data('minInput')).val $(this).data('min')
    $('#' + $(this).data('maxInput')).val $(this).data('max')
    $('#' + $(this).data('gender')).val $(this).data('gender')
    return
  url = ''
  valueString = appendFiltersDesktop(url)
  if valueString != '' and valueString.indexOf('?') == -1
    valueString = valueString.replace('&', '?')
  correct_url = checkForColourInUrl($('#facet-data').attr('data-url'))
  correct_url += valueString
  if Turbolinks.supported
    Turbolinks.visit correct_url
  else
    window.location.assign correct_url

$(document).on 'click', '#filter-clear-btn', ->
  $('.on-off-radiobox input:radio:checked, .on-off-checkbox input:checkbox:checked').each ->
    return $(this).prop 'checked', false
  $('#max_price').removeAttr 'value'
  $('#min_price').removeAttr 'value'
  $('#max_discount').removeAttr 'value'
  $('#max_discount').removeAttr 'value'
  $('#gender').removeAttr 'value'
  $('body').removeClass 'modal-open'
  if window.location.search != ''
    url = $('#facet-data').attr('data-url')
    url = searchTag(checkForColourInUrl(url))
    if Turbolinks.supported
      return Turbolinks.visit url
    else
      return window.location.assign url
  else
    updateSelectedFilters()
  return

$(document).on 'click', '#filter-modal-close', ->
  $('body').removeClass 'modal-open'
  $('#filterModal').foundation 'reveal', 'close'
  $('.reveal-modal-bg').css 'display', 'none'
  return

EnableActiveTab = () ->
  active_tab = true
  appendChips()
  $('.tab-filter-fix').each ->
    if $(this).hasClass('active-tab')
      active_tab = false
    return
  if active_tab
    $('.tab-filter-fix:first').addClass 'active-tab'
    $('#tab_1').addClass 'active'
  return

EnableActiveTab()

$(document).on 'turbolinks:render', ->
  EnableActiveTab()
  keepSelectedPropertyActive()

$ ->
  if $('.store_page_block .previous').length
    prevPage = $('.previous').attr('id').split('_')[1]
    prevUrl = window.location.href.replace(/&?page=[^\&]*/, '') + '&page=' + prevPage
    $('.previous').attr 'href', prevUrl
  if $('.store_page_block .next').length
    current_page = $(".current_page").data().page;
    if $('.next') && (!($('.next').attr('id')))
      $('.next').attr('id', 'page_' + current_page+1)
    nextPage = $('.next').attr('id').split('_')[1]
    if window.location.href.indexOf('?') == -1
      nextUrl = window.location.href + '?page=' + nextPage
    else
      nextUrl = window.location.href.replace(/&?page=[^\&]*/, '') + '&page=' + nextPage
    $('.next').attr 'href', nextUrl
  return

$(document).on 'click', '.flash-deal-header .tab-fix', ->
  $(this).addClass('fd-active-tab').siblings().removeClass('fd-active-tab')
  if $(this).hasClass 'fd-ongoing-tab'
    $('.ongoing-fd-timer').fadeIn()
  else
    $('.ongoing-fd-timer').fadeOut()
  a = parseInt($(this).attr('tab_id'))
  $('[fd_id='+a+']').fadeIn().siblings().fadeOut()
  if isNaN(a)
    $('.fd_active').fadeIn().siblings().fadeOut()

$('#FAQs').foundation
  accordion:
    multi_expand: false

$(document).ready ->
  price_list_wrapper = $('.price-list-wrapper')
  if price_list_wrapper.length == 0
    $('.footer-info').css 'flex-direction', 'unset'
    $(".seo-text-box").addClass("seo-text-full");
  return