{div, h1, ul, li, a, form, select, option, table, tr, td, h5, img} = React.DOM

@DesignTitle = React.createClass
  render: ->
    h1
      id: 'design_title'
      @props.title

@DesignPrice = React.createClass
  render: ->
    if @props.discount_percent > 0
      div
        className: 'product_design_price'
        div
          className: 'row'
          div
            className: 'small-8 columns product_discount_price'
            @props.discount_price
          div
            className: 'small-4 columns percent_disc text-center'
            @props.discount_percent + '% OFF'
          div
            className: 'small-12 columns line_through_text'
            @props.price
          
    else
      div
        className: 'row product_design_price'
        ul
          className: 'no-bullet columns'
          li
            className: 'product_discount_price'
            @props.price

@DesignVariants = React.createClass
  render: ->
    div
      id: 'variants_block'
      @props.variants.map (variant) ->
        variant_class = 'variant'
        if variant.quantity <= 0
          variant_class += ' line_through_text disabled'
        variant.option_type_values.map (option_type_value) ->
          React.createElement(
            DesignVariantList,
            {id: variant.id, class_name: variant_class, options: option_type_value.name}
          )

@DesignVariantList = React.createClass
  on_click: (e)->
    if $(e.target).hasClass('disabled') == false
      $('.variant').removeClass('selected alert')
      $(e.target).addClass('selected alert')

  render: ->
    a
      id: @props.id
      onClick: this.on_click
      className: @props.class_name + ' button secondary round tiny'
      @props.options

@DesignAddons = React.createClass
  componentDidMount: ->
    $('.addon_option_values').hide()
  render: ->
    currency = @props.currency
    form
      id: 'design_addon_form'
      @props.addon_types.map (addon_type, index) ->
        div
          className: 'row'
          React.createElement(
            DesignAddonTypeValues,
            {id: 'addon_type_' + index, title: addon_type.name, options: addon_type.addon_type_values, currency: currency}
          )

@DesignAddonTypeValues = React.createClass
  render: ->
    currency = @props.currency
    div
      className: 'columns'
      @props.title
      React.createElement(
        AddonSelectList,
        {id: @props.id, options: @props.options, type: 'atv', currency: currency, class_name: 'addon_types', key: 'atv_'+@props.id}
      )
      @props.options.map (at_option) ->
        div
          className: 'row'
          div
            className: 'small-10 small-offset-1 medium-10 medium-offset-1 columns addon_option_values'
            id: 'atv_'+at_option.id
            key: 'atv_'+at_option.id
            at_option.addon_option_types.map (aot_key, index) ->
              key_name = aot_key.p_name
              div
                className: 'row'
                key: key_name + '_main'
                div
                  className: 'columns'
                  key: key_name
                  key_name
                  React.createElement(
                    AddonSelectList,
                    {id: 'atov_'+index,options: aot_key.addon_option_values, currency: currency, key: 'atov_'+index, default_option: key_name}
                  )

@AddonSelectList = React.createClass
  getInitialState: ->
    if @props.type == 'atv'
      value: @props.options[0].id
    else
      value: 0
  change: (event) ->
    select_list = event.target
    select_val = select_list.value
    this.setState({value: select_val})
    option_value_select = $('select',select_val)
    $('select#'+ select_list.id).find("option").each ->
      current_val = $(this).val()
      if current_val != select_val
        $('#atv_'+current_val).hide()
    if select_list.id.match(/addon_type/)
      atv_block = '#atv_'+ select_val
      $(atv_block).show()
      prod_time = $('#'+ select_list.id + ' option:selected' ).data('prodtime')
      if prod_time != 'undefined'
        today = new Date()
        today.setDate(today.getDate() + prod_time)
        $('#delivery_date').html(today.toDateString())
  render: ->
    currency = @props.currency
    list_type = @props.type
    select
      id: @props.id
      value: this.state.value
      className: @props.class_name
      onChange: this.change
      if list_type != 'atv'
        option
          value: 0
          @props.default_option
      @props.options.map (at_option) ->
        option_value = at_option.p_name
        prod_time = ''
        if at_option.price
          option_value += ' : ' + currency + ' ' + at_option.price
        if at_option.prod_time
          prod_time = at_option.prod_time
        else if list_type == 'atv'
          prod_time = '0'
        option
          value: at_option.id
          'data-prodtime': prod_time
          option_value

@DesignSpecification = React.createClass
  render: ->
    specifications = @props.specifications
    properties = specifications.properties
    designables = specifications.designable

    keys = ["product_id", "specification"]
    table
      className: 'specifications-table'
      id: 'two'
      keys.map (key, index) ->
        React.createElement(
          TableRows,
          {id: 'specification_'+index, value: specifications[key], index: index, key_name: key, key: 'specification_'+index}
        )
      properties.map (key, index) ->
        if key.value != []
          React.createElement(
            TableRows,
            {id: 'properties_'+index, value: key.value, index: index, key_name: key.type, key: 'properties_'+index}
          )
      designables.map (key, index) ->
        if key.value != ''
          React.createElement(
            TableRows,
            {id: 'designable_'+index, value: key.value, index: index, key_name: key.type, key: 'designable_'+index}
          )

@TableRows = React.createClass
  render: ->
    tr
      id: @props.id
      td {}, toCamelCase @props.key_name
      td {}, @props.value

@DesignPolicies = React.createClass
  render: ->
    div
      className: 'policy_content'
      @props.policies

@ActionButtons = React.createClass
  render: ->
    ul
      className: 'small-block-grid-2 medium-block-grid-2'
      @props.action_buttons.map (action_button, index) ->
        React.createElement(
          ActionButton,
          {key: 'ab_'+index,class_name: action_button.class_name, link: action_button.link, name: action_button.name}
        )

checkVariantSelection = () ->
  status = true
  if $('.variant').length > 0
    status = false
    if $('.variant.selected').length == 1
      status = true
  if status == false
    alert 'Please select options'
  return status

checkAddons = () ->
  status = true
  $('.addon_types').each ->
    addon_option_id = '#atv_'+this.value
    if $(addon_option_id).length > 0
      option_value_select = $('select',addon_option_id)
      option_value_select.css({'border-color':'', 'border-width':''})
      option_value_select.each ->
        if this.value == '0'
          status = false
      if status == false
        $(addon_option_id).show()
        option_value_select.each ->
          if this.value == '0'
            $(this).css({'border-color':'red', 'border-width':'3px'})
      else
        option_value_select.css({'border-color':'', 'border-width':''})
  if status == false
    alert 'Please select highlighted options'
  return status

sendAddToCartGa = (ga_hash) ->
  <%# ga('ec:addProduct', {'id' : ga_hash.id, 'name' : ga_hash.id + '_', 'category' : ga_hash.category, 'brand' : ga_hash.brand, 'price' : ga_hash.price, 'quantity' : ga_hash.quantity}) %>
  <%# ga('ec:setAction', 'add') %>
  <%# ga('send', 'event', 'UX', 'click', 'add to cart') %>
  window._pq = window._pq || []
  _pq.push(['track', 'add_to_cart'])

addToCart = (url) ->
  design_id = $('#product_id').html().split(' ').pop()
  line_item_addons_attributes = []
  $('.addon_types').each (index) ->
    notes = ''
    atv_id = $(this).val()
    atv_block = $('#atv_'+atv_id)
    addon_attributes = {}
    if atv_block.length > 0
      option_value_select = $('select',atv_block)
      option_value_select.each ->
        notes = notes + $('option:first', this).text() + ' : ' + $('option:selected', this).text() + ', '
    addon_attributes['addon_type_value_id'] = atv_id
    addon_attributes['notes'] = notes
    line_item_addons_attributes.push(addon_attributes)
  $.ajax
    url: url
    type: 'POST'
    dataType: 'json'
    data:
      line_items:
        design_id: design_id
        quantity: 1
        line_item_addons_attributes: line_item_addons_attributes
        variant_id: $('.variant.selected').attr('id')
      design_page: true
    success: (response) ->
      sendAddToCartGa(response.ga_hash)
      window.location.href = response.redirect_url

@ActionButton = React.createClass
  logUnbxd: (e) ->
    unbxdTrack("addToCart", {"pid": $('#product_id').html().split(' ').pop()})
  on_click: (e)->
    e.preventDefault()
    if checkAddons() == true && checkVariantSelection() == true
      addToCart(e.target.href)
  render: ->
    li
      className: 'action_button_btn'
      onClick: this.on_click
      a
        className: @props.class_name
        href: @props.link
        method: @props.method
        onClick: @logUnbxd
        @props.name

toCamelCase = (str) ->
  full_text = []
  arr = str.replace("_", " ").split(" ")
  i = 0
  while i < arr.length
    full_text.push(arr[i].charAt(0).toUpperCase() + arr[i].substr(1).toLowerCase())
    ++i
  full_text.join " "

@SimilarDesigns = React.createClass
  componentDidMount: ->
    $('#similar_designs').slick
      dots: true
      arrows: false
      slidesToShow: 4
      slidesToScroll: 4
      responsive: [
        {
          breakpoint: 1024
          settings:
            slidesToShow: 3
            slidesToScroll: 3
        }
        {
          breakpoint: 600
          settings:
            slidesToShow: 2
            slidesToScroll: 2
        }
      ]
  render: ->
    div
      id: 'similar_designs'
      if @props.data.designs
        for similar_design in @props.data.designs
          a
            href: similar_design.design_path
            img
              className: ''
              src :"#{IMAGE_PROTOCOL}#{if similar_design.sizes then similar_design.sizes.small else '<%= asset_path('default_image.jpg') %>'}"
              alt: "Buy #{similar_design.title} online"