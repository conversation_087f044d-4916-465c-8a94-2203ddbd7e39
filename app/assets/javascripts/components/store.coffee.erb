applySlick = ->
  $('#specification_tabs.filters_tabs').slick
    variableWidth: true
    infinite: false
    centerMode: false
    slidesToShow: 4
    slidesToScroll: 1
    arrows: false
    setPosition: true
    responsive: [
      {
        breakpoint: 479
      }
    ]
{div, img, ul, li, a, h1, hr, p, input, label, form} = React.DOM

@Filters = React.createClass

  getInitialState: ->
    selectedFilters: ''
    tab_active: 'active'
    valueString: ''
    filters: @props.data.filters

  # Updates selected checked filter values to selectedFilters
  updateSelectedFilters: ->
    @state.selectedFilters = $('.on-off-radiobox input:radio:checked, .on-off-checkbox input:checkbox:checked').map(-> "##{@id}").get().join(', ')
    @setValueString()

  # redirects to the url formed with applied filters
  applyFilters: ->
    url = "#{window.location.href.replace(/\?.*/, '')}?#{@state.valueString}"
    window.location.href = url

  # Clears the filters by either closing modal or refreshing page if filters were selected
  clearFilters: ->
    if @state.valueString != ''
      window.location.replace('')
    else
      $('#filterModal').foundation('reveal', 'close');

  # Sets the parameters in valueString
  setValueString: ->
    $('.on-off-radiobox input:radio:checked').each ->
      $("##{$(@).data('min_input')}").val($(@).data('min'))
      $("##{$(@).data('max_input')}").val($(@).data('max'))

    @state.valueString = $('.search-box-margin form, input:hidden.range_filter_input, .on-off-checkbox input:checkbox:checked').serialize()
    @getSearchResult()


  getSearchResult: ->
    # forming the url to be fired for filter updation
    url = "#{window.location.href.replace(/\?.*/, '')}?#{@state.valueString}"

    # Updating filters
    _this = @
    $.getJSON url, (response)->
      return true unless response and Object.keys(response).length

      # Removing slick from filters tab titles
      if $('#specification_tabs.filters_tabs.slick-slider')
        $('#specification_tabs.filters_tabs').slick('unslick')

      _this.setState filters: response.filters

      # Checking all the preselected filter inputs
      $("#{_this.state.selectedFilters}").each -> $(@).prop('checked', 'true')

      # Reloading tab
      $(document).foundation('tab', 'reflow')

      # Re-Applying slick to tab titles
      applySlick()

  render: ->
    div
      className: 'row panel_block'
      id: 'designable_details'
      div
        className: 'columns panel_heading'
        ul
          className: 'filters_tabs'
          id: 'specification_tabs'
          for range_filter,range_index in @state.filters.range_filters
            li
              className: 'tabs'
              'data-tab': ''
              div
                className: "tab-title #{if range_index == 0 then 'active'}"
                a
                  className: "button radius tiny"
                  href: '#tab'+range_index
                  range_filter.name
          for id_filter,index in @state.filters.id_filters
            li
              className: 'tabs'
              'data-tab': ''
              div
                className: 'tab-title'
                a
                  className: 'button radius tiny'
                  href: '#tab'+ (range_index + index)
                  id_filter.name
      div
        className: 'columns panel_content'
        div
          className: 'tabs-content'
          for range_filter,range_index in @state.filters.range_filters
            div
              id: 'tab'+ range_index
              className: "content #{if range_index == 0 then 'active'}"
              input
                className: 'range_filter_input'
                id: range_filter.keys.min
                type: 'hidden'
                name: range_filter.keys.min
              input
                className: 'range_filter_input'
                id: range_filter.keys.max
                type: 'hidden'
                name: range_filter.keys.max
              for list in range_filter.list
                div
                  className: 'on-off-radiobox'
                  input
                    name: "#{range_filter.name}_range"
                    value: ''
                    type: 'radio'
                    id: "#{range_filter.name.replace(/\s/g, '_')}_#{list.values.min}-#{list.values.max}"
                    'data-min_input': range_filter.keys.min
                    'data-max_input': range_filter.keys.max
                    'data-min': list.values.min
                    'data-max': list.values.max
                    onChange: @updateSelectedFilters
                  label
                    htmlFor: "#{range_filter.name.replace(/\s/g, '_')}_#{list.values.min}-#{list.values.max}"
                    "#{list.name} [#{list.count}]"

          for id_filter,index in @state.filters.id_filters
            div
              id: 'tab'+ (range_index + index)
              className: 'content'
              for list in id_filter.list
                div
                  className: 'on-off-checkbox'
                  input
                    key: list.value
                    class: id_filter.name
                    name: "#{id_filter.key}[]"
                    id: "#{id_filter.key}_#{list.value}"
                    type: 'checkbox'
                    value: list.value
                    onChange: @updateSelectedFilters
                  label
                    htmlFor: "#{id_filter.key}_#{list.value}"
                    "#{list.name} [#{list.count}]"
      div
        className: 'columns short_filter_btn'
        div
          className:'btn_apply button success small-12'
          onClick: @applyFilters
          'Apply'

        div
          className: 'btn_clear button secondary small-12'
          onClick: @clearFilters
          'Clear'

window.onpopstate= (event) ->
  window.back_click = true
  window.addEventListener 'load', () ->
    if $(".previous").length > 0
      $('.previous')[0].click()
    else if $(".next").length > 0
      $('.next')[0].click()

@Store = React.createClass
  componentDidMount: ->
    @addToCart()
    @sortDesigns()
    @hideShowButton()

  componentDidUpdate: ->
    node = $('.off-canvas-wrap #main-section, .main-section')
    node.push($(document))
    node.each ->
      $(this).scrollTop(node.offset().top)
    new_path = window.location.href.split('?')[0]
    if typeof back_click is "undefined" or back_click is false
      window.history.pushState({path:new_path},'',store_param)
      window.back_click = false

  addToCart: ->
    _this = @
    $('.store_block').on 'click','.add_to_cart_link', (e) ->
      design_id = $(this).attr('id')
      _this.createLineItem(design_id)

  hideShowButton: ->
    lastScrollTop = 0
    $('body #main-section').scroll (event) ->
      st = $(this).scrollTop()
      if st > lastScrollTop
        $('#action_buttons').fadeOut 100
      else
        $('#action_buttons').fadeIn 100
      lastScrollTop = st

  createLineItem: (design_id) ->
    $.ajax
      type: 'POST'
      dataType: 'json'
      url: '/line_items'
      data:
        line_items:
          design_id: design_id
      success: (response) ->
        if (response.url == undefined)
          $('#cart_count').html(response.cart_count)
          window.location.href = '/cart'
        else if (response.cart_count == undefined)
          window.location.href = response.url

  getInitialState: ->
    {
      designs: @props.data.designs
      prevPage: @props.data.previous_page
      page: @props.data.next_page
      loadingFlag: false
    }

  defaultLoadImg: (img, event)->
    img.target.src = "<%= asset_path('default_image.jpg') %>"

  handleScroll: (e) ->
    if $('.page_view_port:in-viewport').length and !@state.loadingFlag
      @setState loadingFlag: true
      @getDesigns()
  sortDesigns: ->
    _this = @
    $('#shortModal').on 'change', ->
      selectedValue = $( "#shortModal option:selected" ).val();
      $('#shortModal').foundation('reveal', 'close');
      if window.location.href.indexOf('?') == -1
        url = window.location.href + '?sort=' + selectedValue
      else
        url = window.location.href.replace(/&?sort=[^\&]+/, '') + '&sort=' + selectedValue
      window.location.replace(url)

  getDesigns: (e) ->
    pageNumber = $(e.target).attr('id').split('_')[1]
    if window.location.href.indexOf('?') == -1
      url = window.location.href + '?page=' + pageNumber
    else
      url = window.location.href.replace(/&?page=[^\&]*/, '') + '&page=' + pageNumber
    _this = @
    window.store_param = '?' + url.split('?').pop()
    $.getJSON url, (result) ->
      if result and result.designs.length > 27
        if ga
          <%# ga('send', 'pageview', url) %>
        _this.setState
          designs: result.designs
          loadingFlag: false
          page: result.next_page
          prevPage: result.previous_page
      else
        _this.setState
          designs: []
          loadingFlag: false
          page: result.next_page
          prevPage: result.previous_page

  gaClick: (elementData) ->
    # Will need to change id if added sku in unbxd 
    <%# ga('ec:addProduct', {'id' : elementData.unbxdparam_sku, 'name' : elementData.unbxdparam_sku + '_', 'category' : elementData.category, 'brand' : elementData.brand, 'price' : elementData.price, 'grade' : elementData.grade, 'state' : elementData.state}) %>
    <%# ga('ec:setAction', 'click',{'list':  @props.store_page.ga_list }) %>
    <%# ga('send', 'event', 'UX', 'click', 'Results')  %>

  logImpressions: (e) ->
    elementData = $(e.target).parent().data()
    @gaClick(elementData)
    unbxdTrack 'click',
    'pid': elementData.unbxdparam_sku
    'prank': elementData.unbxdparam_prank

  logImpressions_anchor_tag: (e) ->
    elementData = $(e.target).data()
    @gaClick(elementData)
    unbxdTrack 'click',
    'pid': elementData.unbxdparam_sku
    'prank': elementData.unbxdparam_prank

  render: ->
    div
      className: 'store_block'
      div
        class: 'heading_title'
        h1
          className: 'product-title'
          @props.store_page.title
        p
          className: 'product_count'
          "#{@props.data.results} Products"

      hr {}
      ul
        className: 'store_page_design small-block-grid-2 medium-block-grid-3 large-block-grid-4'
        for block,index in (if @state.designs.length > 0 then @state.designs else @props.data.designs)  
          li
            key: 'storepage_' + index
            div
              className: 'fr_page'
              a
                key: 'link' + index
                'data-unbxdattr': 'product'
                'data-unbxdparam_sku': block.id
                'data-unbxdparam_prank': index + 1
                'data-name': block.cached_slug
                'data-category': block.category_name
                'data-brand': block.brand
                'data-price': block.price
                'data-state': block.state
                'data-grade': block.grade
                onClick: @logImpressions
                href: block.design_path
                img
                  key: 'img_' + index
                  className: 'error_img'
                  src : "#{IMAGE_PROTOCOL}#{if block.sizes then block.sizes.small else '<%= asset_path('default_image.jpg') %>'}"
                  alt: "Buy #{block.title} #{block.category_name} online"
                  onError: @defaultLoadImg
              div
                className: 'panel design_desc'
                div
                  key: 'text_trim' + index
                  className: 'truncate'
                  a
                    'data-unbxdattr': 'product'
                    'data-unbxdparam_sku': block.id
                    'data-unbxdparam_prank': index + 1
                    'data-name': block.cached_slug
                    'data-category': block.category_name
                    'data-brand': block.brand
                    'data-price': block.price
                    'data-state': block.state
                    'data-grade': block.grade
                    onClick: @logImpressions_anchor_tag
                    href: block.design_path
                    block.title
                div
                  id: 'design_details'
                  className: 'details_block'
                  div
                    className: 'design-col1'
                    div
                      className: 'design_price'
                      "#{@props.data.symbol}" + " " + block.discount_price
                    div
                      className: 'discount_font line_through_text'
                      "#{@props.data.symbol}" +" "+ block.price
                  div
                    className:  'design-col2'
                    div
                      className: 'discount_new_wrap'
                      block.discount_percent + '% OFF'
                if block.state == 'in_stock'
                  div
                    id: block.id
                    className: 'add_to_cart_link button tiny add_new_pos'
                    'ADD TO CART'
                else
                  div
                    className: 'sold_out_link button disabled tiny alert'
                    'SOLD OUT'
        ul
          className: 'append_images store_page_design small-block-grid-2 medium-block-grid-3 large-block-grid-4'
        hr {}
        div
          className: 'navigate_page text-center li_append'
          if @state.prevPage
            a
              className: 'button secondary nav-button tiny previous'
              id: "page_#{@state.prevPage - 1}"
              onClick: @getDesigns
              'Previous'
          div
          if @state.page
            a
              className: 'button nav-button tiny next'
              id: "page_#{@state.page + 1}"
              onClick: @getDesigns
              'Next'

