var INT_PAYMENT_OPTIONS = INT_PAYMENT_OPTIONS || {};

INT_PAYMENT_OPTIONS = (function(window, document, Payment){

  Payment.methods ={
    formSubmitHandler: function(){
      $("#new_order").on('submit', function(e){
        if(!INT_PAYMENT_OPTIONS.methods.isValidForm()){
          e.preventDefault()
          ACCORDIAN.methods.animateError('.dom-payments','.dom-payment-error', "Please select a Payment Option")
          return false
        }
        else{
          return true
        }
      })
    },

    postApiCall: function(url, params){
      url += '?' + ( new URLSearchParams( params ) ).toString();
      return fetch( url, {method: "POST"} ).then( function(response){return response.json();});
    },

    getPaymentPayload: function(){
      var payload_old = $("#new_order").serializeArray();
      var payload = {}
      for (var i in payload_old) {
        var ele = payload_old[i]
        payload[ele['name']] = ele['value']
      } 
      return payload
    },

    isValidForm: function(){
      if (INT_PAYMENT_OPTIONS.methods.isValidAccordian() || INT_PAYMENT_OPTIONS.methods.isValidWallet()){
        return true;
      }
    },

    isValidAccordian: function(){
      $active_accordion =  $(".accordion-navigation.active")
      checked_option = $('[name="order[pay_type]"').filter(":checked").val()
      return ($active_accordion.length > 0 || checked_option != undefined)
    },

    isValidWallet: function(){
      if(!WALLET.methods.useCompleteWallet()){
        return false
      }
      $('#wallet_return').val('fully_used_wallet')
      return true
    },

    disableFormSubmit: function(){
      if (window.innerWidth <= 1024){
        $(".sticky-desktop-int").css("display", "none")
      }else{
        $("#action_buttons").css("display", "none")
      }
    },

    enableFormSubmit: function(){
      if (window.innerWidth <= 1024) {
        $(".sticky-desktop-int").css("display", "");
        $(".sticky-mobile-int").css("display", "none");
      } else {
      $(".sticky-mobile-int").css("display", "");
      $(".sticky-desktop-int").css("display", "none");
      }
    },


  }
  return Payment; 
})(this, this.document, INT_PAYMENT_OPTIONS);

