# when the current carousel slide is of video, play the video 
# when the current caraousel slide is of image, pause and mute the video 




product_video = document.getElementById('product_video')

play_Video = ->
  product_video.play()
  return

pause_Video = ->
  product_video.pause()
  return

mute_unmute_switch = ->
  product_video.muted = true
  return

$('#design_images').on 'after-slide-change.fndtn.orbit', (e) ->
  current_caraousel = document.getElementsByClassName('active')[0]
  if current_caraousel.contains(product_video)
    play_Video()
  else
    if product_video
      pause_Video()
      mute_unmute_switch()
  return







  
