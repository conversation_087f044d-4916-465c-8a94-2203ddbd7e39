var MR = MR || {};
var MR = (function(window, document, Mirraw){

Mirraw.productRecommendation ={
    
    initialiseRecommendations: function(ele) {
        ele.each(function(idx, recommendationsHtml) {

            if (recommendationsHtml.dataset == undefined || recommendationsHtml.innerHTML.length > 1 ) {
                return
            }

            MR.productRecommendation.initProductShowRecommendation(recommendationsHtml.dataset)
        })          
    },
    recommendationsClickAction: function() {
        var similar_products_btn = document.querySelector(".recommendations_modal_btn")
        if (!similar_products_btn) {
            return
        }
        similar_products_btn.addEventListener("click", function () {
            var similar_product_partial = $("#similar_products")      
            
            var transform, translate;
            similar_product_partial.addClass('animate');
            translate = window.innerHeight - similar_product_partial.children('.content').height();
            transform = "translate3d(0px," + (translate - 20) + "px, 0px)";
            similar_product_partial.css("transform", transform);
            similar_product_partial.siblings('.variant-bg').css({
            'pointer-events': 'auto',
            'opacity': '1'
            });
        });          
    },
    initProductShowRecommendation: function(eleAttribute){
        var data = {
            'page_type' : eleAttribute['pageType'],
            'widget' : eleAttribute['widget'],
            'uid' : eleAttribute['uid'],
            'modal': eleAttribute['modal'],
            'id': eleAttribute['id'],
            'catlevel1Name': eleAttribute['catLevel1Name']
        }

        $.ajax({
            url: "/unbxd_recommendations_in",
            type: "get", //send it through get method
            data: data,
            success: function(response) {
                // $("#unbxd_recs_container")[0].innerHTML = response.html;
                unbxdHtmlString = "#" + eleAttribute['htmlId']
                $(unbxdHtmlString).html(response.html);
                
                if (unbxdHtmlString == '#unbxd_pdp_modal_recommendations' && response.html == '') {
                    $('.recommendations_modal_btn').hide()
                }
                MR.product.unbxdSwiperRecommendation();
            },
            error: function(xhr) {
                unbxdHtmlString = "#" + eleAttribute['htmlId']
                if (unbxdHtmlString == '#unbxd_pdp_modal_recommendations') {
                    $('.recommendations_modal_btn').hide()
                }
                throw new Error('Recommendation issue for ' + unbxdHtmlString, xhr.statusText);
            }
        });

        // Used for Unbxd Analytics
        // As this is an object, when 3 calls are of widgets are there
        UnbxdAnalyticsConf=window.UnbxdAnalyticsConf ||{};
        UnbxdAnalyticsConf["experience_pagetype"]=eleAttribute['pageType'];
        UnbxdAnalyticsConf["experience_widget"]=eleAttribute['widget'];
  
    },
} 
return Mirraw;
})(this, this.document, MR);