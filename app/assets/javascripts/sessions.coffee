$ ->
  $('#send_otp_form').on 'submit', (e)->
    e.preventDefault();
    $('.otp-sent-phone').html($('#phone').val())
    $.ajax(generateOtp($('#phone').val(), $(this).attr('action'), false))

  $('.closebtn').on 'click', ->
    $('#error-message').hide()
    $('#otp-error-message').hide()

  $('#verify_otp_form').on 'submit', (e)->
    # ga('set', 'dimension12', 'logged in with otp')
    $(".overlay").show()
    $(".progress_img1").show()
    e.preventDefault()
    $('#user_phone').val($('#phone').val())
    $('#one_time_password').val(getOtpValue())
    $.ajax(verifyOtp($('#phone').val(), getOtpValue(), $(this).attr('action')))

  $('#new_account').on 'submit', (e)->
    $(".overlay").show()
    $(".progress_img1").show()
    # ga('set', 'dimension12', 'logged in with email')

  $('#resend_otp').on 'click', ->
    $.ajax(generateOtp($('#phone').val(), '/accounts/send_otp', true))
    getTimer()
    $('.resend-tag').hide()
    $('.timer').show()
    # $(this).prop('disabled', true)

  $('#email-login-link').on 'click', ->
    $('.mobile-number-login').hide()
    $('.email-login').show()
    # ga('set', 'dimension12', 'email login form view')

  $('#number-login-link').on 'click', ->
    $('.email-login').hide()
    $('.mobile-number-login').show()
    # ga('set', 'dimension12', 'otp login form view')

verifyOtp = (phone, otp, url) ->
  type: 'POST'
  data:
    user_phone:phone
    one_time_password: otp
  url: url
  dataType: 'JSON'
  success: (data, status) ->
    if data['error']
      $(".overlay").hide()
      $(".progress_img1").hide()
      $('#otp-error-message').show()
      clearOtpFields()
    else
      window.location.href = data['location'];

generateOtp = (phone, url, resent) ->
  type: 'POST'
  data:
    phone:phone
  url: url
  dataType: 'JSON'
  success: (data, status, jqxhr) ->
    if (data['error'] == true)
      $('#error-message').show()
    else if (data['sms_sent'] == true && resent == false)
      $('.new-login-form').hide()
      $('.verify-otp-form').show()
      $('.otp-sent-phone').text(phone)
      getTimer()

getOtpValue = () ->
  otp = $('#otpBox1').val() + $('#otpBox2').val() + $('#otpBox3').val() + $('#otpBox4').val()
  return otp

getTimer = () ->
  seconds = document.getElementById('countdown-timer').textContent
  countdown = setInterval((-> 
    seconds--
    if seconds < 10
      seconds = '0' + seconds
    document.getElementById('countdown-timer').textContent = seconds
    $('.resend-tag').hide()
    $('.timer').show()
    if seconds <= 0
      clearInterval countdown
      $('.resend-tag').show()
      $('.timer').hide()
      document.getElementById('countdown-timer').textContent = 15
    return
  ), 1000)

clearOtpFields = () ->
  $('#otpBox1').val('')
  $('#otpBox2').val('')
  $('#otpBox3').val('')
  $('#otpBox4').val('')
  $('#otpBox1').focus()
