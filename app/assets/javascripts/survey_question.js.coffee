$ ->
  $('.submit_question_group .submit').on('click', (e) ->
    e.preventDefault();
    terminator_flag = false
    selected_issue = {}
    self = $(this)
    question_group = $(this).closest('.question_group')
    show_class_name = question_group.data('next-classes').split(',')
    question_group.find("input:checked, .grey-stars-design[selected='selected']").each ->
      selected_issue[$(this).attr('name').split('_')[0]] = $(this).attr('value')
      if survey_terminator(this)
        terminator_flag = true
    association_id = $(this).data('association-id');
    association_type = $(this).data('association-type');
    issue_type = $(this).data('issue-type');
    #$('#survey_answer input:checked').each ->
      #selected_issue[$(this).attr('name')] = $(this).attr('value')
    order_id = $(this).data('order-id');
    $.ajax
      type: 'POST'
      data:
        answers: selected_issue
        association_id: association_id
        type: association_type
        issue_type: issue_type
        sample_form: location.href.includes('sample_form')
      url: '/surveys/survey_answers'
      datatype: 'JSON'
      success: (data) ->
        $('#done-box').hide()
        if data.status == 200
          if terminator_flag
            terminate_survey(self)
          else
            show_class(show_class_name)
            next_question(question_group)
            question_group.slideUp 500, ->
              question_group.remove()
            question_group.find('.submit_question_group .submit').remove();
          #$('#survey_answer, #audience-text').hide();
          #$('#submit_nps_survey_questions .submit').remove();
          #$('#review_designs').show();
        else
          $('.alert-danger').text(data.notice)
          $('.alert-danger').slideDown 500, ->
            setTimeout (->
              $('.alert-danger').slideUp 500
            ), 3000
        $("html, body").animate({ scrollTop: 0 }, "slow");
      error: (data) ->
        $('.alert-danger').text(data.notice)
        $('.alert-danger').slideDown 500, ->
          setTimeout (->
            $('.alert-danger').slideUp 500
          ), 3000
  )


condition_parser = (condition, value) ->
  if condition != undefined
    condition = condition.split('_')
    switch condition[0]
      when 'gte'
        return value >= +condition[1]
      when 'lte'
        return value <= +condition[1]
  return false

survey_terminator = (object)->
  terminator = +$(object).data('terminator')
  condition = $(object).closest('.nps_questions').data('condition')
  condition_parser(condition, terminator)

show_class = (classes) ->
  classes.forEach (class_name) ->
    $(class_name).first().show()

remove_element = (classes) ->
  classes.forEach (class_name) ->
    class_name = $(class_name).first()
    class_name.slideUp 500, ->
      class_name.remove()

next_question = (object) ->
  panel = object.closest('.accordion-navigation')
  if panel.find('.question_group').length == 1
    panel.slideUp 500, ->
      review_table = panel.closest('.review_table')
      if review_table.find('.question_group').length == 1
        review_table.find('.review_text_box').slideDown 500
      panel.remove()
      $(".review_text .accordion a:first").trigger("click");
      $('.review_text .accordion .accordion-navigation:first-child .question_group:first-child').show()
  else
    object.slideUp 500, ->
      object.remove()
      $('.review_text .accordion .accordion-navigation:first-child .question_group:first-child').show()

CheckAllDone = ->
  if $('.review_table').length == 0
    renderThankYouMessage()

terminate_survey= (object) ->
  review_table = object.closest('.accordion-navigation')
  if review_table.length <= 0
    review_table = object.closest('.review_table')
  text_box = object.closest('.review_table')
  if text_box.find('.accordion-navigation').length == 1
    text_box.find('.review_text_box').slideDown 50
  review_table.html('Thank you')
  review_table.slideUp 800, ->
    review_table.remove()
    CheckAllDone()
    $(".review_text .accordion a:first").trigger("click");
    $('.review_text .accordion .accordion-navigation:first-child .question_group:first-child').show()


$ ->
  $("input[type = 'radio']").prop 'checked', false
  $('.accordion .accordion-navigation').each ->
    if $(this).find('.question_group').length == 0
      $(this).remove()
  $('.review_text .accordion .accordion-navigation:first-child .question_group:first-child').show()
  $(".review_text .accordion a:first").trigger("click");

$ ->
  $('body').on('click', '.question_group input[type="radio"],.rating-tab-design .grey-stars-design',(e) ->
    $(this).closest('.question_group').find('.submit_question_group').show()
  )


$ ->
  $('.rating-tab-design .grey-stars-design').hover (->
    star_value_design = $(this).data('star-value-design');
    row_number = $(this).data('row-number');
    $(this).closest('.answer').find('.grey-stars-design').each ->
      if $(this).data('star-value-design') <= star_value_design && $(this).css('color') == 'rgb(53, 53, 53)'
        $(this).css 'color', '#FFC315'
  ), ->
    $(this).closest('.answer').find('.grey-stars-design').each ->
      if $(this).css('color') == 'rgb(255, 195, 21)'
        $(this).css 'color', '#353535'
$ ->
  $('.rating-tab-design .grey-stars-design').on 'click', (e) ->
    star_value_design = $(this).data('star-value-design');
    row_number = $(this).data('row-number');
    $(this).closest('.answer').find('.grey-stars-design').each ->
      $(this).removeAttr('selected')
      if $(this).data('star-value-design') <= star_value_design
        $(this).css 'color', '#FFD316'
      else
        $(this).css 'color', '#353535'
    $(this).attr('selected', 'true')


$ ->
  $('.submit_review_text_done').on 'click', (e) ->
    e.preventDefault()
    row_number_text = $(this).data('row-number-text')
    product_id = $(this).data('product-id')
    order_id = $(this).data('order-id')
    comment_data = $('#customer_review_' + row_number_text).val()
    $.ajax
      type: 'POST'
      url: '/surveys/save_review_text'
      data:
        comment: comment_data
        order_id: order_id
        product_id: product_id
        sample_form: location.href.includes('sample_form')
      success: (result) ->
        $('.review_table_' + row_number_text).remove();
        CheckAllDone()
        return
      error: (result) ->
        $('#survey_alert').text(data.notice)
        return
    return

renderThankYouMessage = ->
  $('#review_designs').html('\
    <div class="submission-message">\
      <div class="feedback-logo"><img src="/assets/tick_white.svg"></div>\
      <div class="thank-you-text">Thank You!</div>\
      <div class="feedback-subtext">Your feedback has been successfully submitted</div>\
      <a href="/" class="return-to-home">Return to Homepage</a>\
    </div>').css('margin', '11rem 0')
  
  $('.review-the-products').remove()

  if $('#last-box').data('type') == 'promoters'
    $('#last-box').show()

$ ->
  $('.submit_review_text_cancel').on 'click', (e) ->
    e.preventDefault()
    row_number_text = $(this).data('row-number-text')
    $('.review_table_' + row_number_text).remove()
    CheckAllDone()
