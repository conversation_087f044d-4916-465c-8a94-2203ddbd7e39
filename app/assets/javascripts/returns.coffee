checkBoxCheckEvent = (id) ->
  check_box = $('input[value='+id+']')
  if check_box.is(':checked')
    check_box[0].checked = false
    $('#item_'+id).addClass 'hide'
  else
    check_box[0].checked = true
    $('#item_'+id).removeClass 'hide'
  $('#return_type_of_refund').trigger('change')
  if ($("input[type=checkbox]:checked").length != 0)
    $('.submit_reason').removeClass 'hide'
    $('#reason_'+id).attr 'required', true
  else
    $('.submit_reason').addClass 'hide'
    $('#reason_'+id).removeAttr 'required'

$ ->
  $(document).on 'click', '.img-check', ->
    id = this.id.substring(6)
    msg = $(this).attr('data-notice')
    if msg == ''
      $('.error-alert').remove()
      $(this).toggleClass 'check'
      $('#done_step_'+id).toggleClass 'done'
      checkBoxCheckEvent(id)
    else
      $('.error-alert').remove()
      text_msg = "<div data-alert class='alert-box row warning error-alert radius'>"+msg+"<a href='#' class='close'></a></div>"
      $('#new_return').prepend(text_msg)

	$(document).on 'change', '#new_return .quantity', ->
    item_id = $(this).attr('data-item-id')
    $('#total_'+item_id).text((Number($('#quantity_'+item_id+' option:selected').text()) * Number($('#item_price_'+item_id).text())).toFixed(2))
    $('#return_type_of_refund').trigger('change')

$ ->
  $(document).on 'change', '#new_return .item_reason', ->
    id = this.id.substring(7)
    return_reason_hash = $(this).data('return-reason-hash')
    $('#upload_panel_'+id).addClass 'hide'
    $('#uploadimage_'+id+"_1").removeAttr 'required'
    $('#uploadimage_'+id+"_2").removeAttr 'required'
    $('#reason_details_panel_'+id).addClass 'hide'
    $('#reason_details_'+id).find('option').remove()
    $('#uploadimage_'+id+"_1").val('')
    $('#uploadimage_'+id+"_2").val('')
    if $.inArray($(this).val(), [
        'Print /Design /Color Mismatch'
        'Damaged Product'
        'Wrong Product'
      ]) != -1
      $('#upload_panel_'+id).removeClass 'hide'
      $('#uploadimage_'+id+"_1").attr 'required', true
      $('#uploadimage_'+id+"_2").attr 'required', true
    else
      for reason in return_reason_hash[$(this).val()]
        return_reason = $('<option>')
        return_reason.attr('value', reason).text(reason)
        $('#reason_details_'+id).append(return_reason)
      $('#reason_details_panel_'+id).removeClass 'hide'
    return

CheckAccountNumber = () ->
  type: 'GET'
  data:
      bank_name: $('#return_bank_name').val()
  url: '/returns/get_account_no_length'
  datatype: 'json'
  success: (data) ->
    error_label = $('#return_error_label_for_refund')
    if data.length != $('#return_account_number').val().length
      error_label.text("Account number should be of "+ data.length + " digits")
      error_label.addClass('label alert')
    else
      if error_label.text().match(/Account number/) != null
        error_label.text('')
        error_label.removeClass('label alert')

verifyIFSC = () ->
  ifsc_regex = /^[a-zA-Z]{4}[a-zA-Z0-9]{7}$/
  error_label = $('#return_error_label_for_refund')
  if ifsc_regex.test($('#return_ifsc_code').val())
    if error_label.text().match(/IFSC Code/) != null
      error_label.text('')
      error_label.removeClass('label alert')
    $.ajax(CheckAccountNumber())
  else
    error_label.addClass('label alert')
    error_label.text("IFSC Code should be of 11 charachters only with first four letters.")

$ ->
  $(document).on 'click', '.edit_buttons', (e) ->
    verifyIFSC()
    if $('#return_error_label_for_refund').text() != ''
      e.preventDefault()
      return false

  $(document).on 'change', '.refund_type_dropdown', (e) ->
    value = $('.refund_type_dropdown option:selected').text()
    is_bank_detail_require = $(this).data('bank-require')
    return_total = getTotal()
    is_otp_required = $(this).data('otp-required') && return_total > 0 && return_total <= $(this).data('max-automated-cod-amount')
    $('#return_account_holder_name, #return_ifsc_code, #return_branch, #return_account_number, #return_user_paypal_email, #verified_phone_number, #otp').removeAttr('required')
    $('.bank_details_form').addClass('hide')
    $('#otp_verification_form').addClass('hide')
    if value == 'Refund' && is_otp_required
      $('#otp_verification_form').removeClass('hide')
      $('.bank_details_not_needed').addClass('hide')
      $('#verified_phone_number, #otp').attr('required', true)
    else if value == 'Refund' && is_bank_detail_require && !is_otp_required
      $('#otp_verification_form').addClass('hide')
      $('.bank_details_form').removeClass('hide')
      $('#return_account_holder_name, #return_ifsc_code, #return_branch, #return_account_number').attr('required', true)
      $('.bank_details_not_needed').addClass('hide')
    else
      $('#otp_verification_form').addClass('hide')
      $('.bank_details_form').find('input:text').val('')
      $('.bank_details_not_needed').removeClass('hide')
      $('.bank_details_form').addClass('hide')

  $(document).on 'click', '.account_details', (e) ->
    if $(this).text() == 'Paypal Account'
      $('#paypal_detail').removeClass('hide')
      $('#bank_detail').addClass('hide')
      $('#return_account_holder_name, #return_ifsc_code, #return_branch, #return_account_number').removeAttr('required').val('')
      $('#return_user_paypal_email').attr('required',true)
    else
      $('#paypal_detail').addClass('hide')
      $('#bank_detail').removeClass('hide')
      $('#return_account_holder_name, #return_ifsc_code, #return_branch, #return_account_number').attr('required',true)
      $('#return_user_paypal_email').removeAttr('required').val('')

  $(document).on 'keyup', '#return_ifsc_code', ->
    verifyIFSC()

  $(document).on 'keyup', '#return_account_number', ->
    $.ajax(CheckAccountNumber())

$ ->
  $(document).on 'click', '.user_return_panel .no_button', ->
    id = $(this).data('id')
    $('body').removeClass('modal-open')
    $("#cancel_order_#{id}").foundation('reveal', 'close')

checkTrackingNumber = (rdoid,tracking_number) ->
  type: 'GET'
  data:
      rdo_id: rdoid
      tracking_number: tracking_number
  url: '/returns/check_tracking_number'
  datatype: 'json'
  success: (data) ->
    if data.new_track != 'correct'
      $('.same_track_notice').removeClass('hidden')
      $('.same_track_notice').text("* Please enter tracking number of package you will return to us.")
      return false
    else
      $('.same_track_notice').addClass('hidden')
      $('.same_track_notice').text('')
      $(".return_designer_order_event").submit();
      return true
$ ->
  $(document).on 'click', '.designer_well .track_submit', (e) ->
    rdoid = $(this).attr('data-rdo-id')
    tracking_number =  $('input[id="tracking_number_'+rdoid+'"]').val()
    if tracking_number != ''
      e.preventDefault();
      $.ajax(checkTrackingNumber(rdoid,tracking_number))

  $(document).on 'click','.policy_button', ->
    return $('body').addClass 'modal-open'

  $(document).on 'click', '.close-reveal-modal', (e) ->
    return $('body').removeClass 'modal-open'

  $(document).on 'change', '.item_image', ->
    inputFile = $(this)
    maxExceededMessage = 'This file exceeds the maximum allowed file size (6 MB)'
    extErrorMessage = 'Only image file with extension: .jpg, .jpeg, .gif or .png is allowed'
    allowedExtension = [
      'jpg'
      'jpeg'
      'gif'
      'png'
    ]
    maxFileSize = inputFile.data('max-file-size')
    sizeExceeded = false
    extError = false
    $.each this.files, ->
      if @size and maxFileSize and @size > parseInt(maxFileSize)
        sizeExceeded = true
      extName = @name.split('.').pop()
      if $.inArray(extName, allowedExtension) == -1
        extError = true
      return

    if sizeExceeded
      window.alert maxExceededMessage
      $(inputFile).val ''
    if extError
      window.alert extErrorMessage
      $(inputFile).val ''
    return

  $('#get-otp-btn').on 'click', (e) ->
    e.preventDefault();
    $('#incorrect-phone-error').addClass('hide')
    if is_valid_phone($('#verified_phone_number').val())
      $.ajax(generateOtp($('#verified_phone_number').val(), $('#order_number').val()))
    else
      $('#incorrect-phone-error').removeClass('hide')

generateOtp = (phone, order_number) ->
  type: 'POST'
  datatype: 'json'
  data:
    phone: phone
    order_number: order_number
  url: '/returns/generate_otp'
  success: (data) ->
    if data.sms_sent == true
      $('#otp-input-field').removeClass('hide')
      $('#submit-otp-btn').removeClass('hide')
      $('#get-otp').addClass('hide')
      $('#phone-field').addClass('hide')

is_valid_phone = (phone) ->
  regex = /^[6-9][0-9]{9}$/
  if regex.test(phone.trim())
    return true
  else
    return false

getTotal = () ->
  id_arr = [] 
  $("input[name='return_items[]']:checked").each ->
    id_arr.push($(this).val()) 
  total = 0
  for t in id_arr
    total = total + parseInt($("#total_"+t).text() || 0)
  return total

$ ->
  $(document).ready ->
    if $('.returns_heading').length > 0
      $('.return_btn').addClass('active')
      $('.order_btn').removeClass('active')