var ACCORDIAN = ACCORDIAN || {};
 
ACCORDIAN = (function(window, document, Accordian){

  Accordian.methods ={
    onChangeHandler: function(){
      accordionNavigation = $('.accordion-navigation')
      accordionNavigation.find(".inline-options").on('change', function(){
        accordionNavigation.removeClass("active")
        accordionNavigation.find(".content").removeClass("active")
        INT_PAYMENT_OPTIONS.methods.enableFormSubmit()
        WALLET.methods.checkWalletDetails()
      })
      
      $(".payment-text-block").on('click', function(){
        $('[name="order[pay_type]"').removeAttr("checked");
        INT_PAYMENT_OPTIONS.methods.disableFormSubmit()
        WALLET.methods.checkWalletDetails()
      })

    },
    animateError: function(element, error_element , message){
      element =  $(element)
      $('html, body').animate({
        scrollTop: element.offset().top - 60
      }, 500, shake())
      
      function shake(){
        element.find(error_element).slideDown('slow').text(message)
        element.find('a').addClass('shake-effect')
        setTimeout(function(){
          element.find('a').removeClass('shake-effect')
        }, 750)
      }
    },

    activateAccordion: function(class_name){
      setCookie('selected_payment_option')
      element = $('.accordion-navigation' + class_name)
      element.addClass("active")
      element.find('.payment-text-block').attr('aria-expanded', 'true')
      element.find('.content').addClass("active")
      $('[name="order[pay_type]"').removeAttr("checked");
      WALLET.methods.unCheckWallet()
      INT_PAYMENT_OPTIONS.methods.disableFormSubmit()
    },

    deactivateAccordion: function(){
      element = $('.accordion-navigation')
      element.removeClass("active")
      element.find('.payment-text-block').attr('aria-expanded', 'false')
      element.find('.content').removeClass("active")
      $('[name="order[pay_type]"').removeAttr("checked");
    }
  }
  return Accordian; 
})(this, this.document,ACCORDIAN );
