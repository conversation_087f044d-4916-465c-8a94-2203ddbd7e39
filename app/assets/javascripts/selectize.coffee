class window.Selectize
  bindings = ->
    @$options.on 'change', 'input[type=radio]', (->
      if isOtherOptionSelected.call(this)
        startEditingOtherOption.call(this)
      else
        cancelEditingOtherOption.call(this)
    ).bind(this)

    @$otherOptionTextBox.on 'keyup', ((event) ->
      if event.key == 'Enter'
        doneEditingOtherOption.call(this)
      else if event.key == 'Escape'
        cancelEditingOtherOption.call(this)
    ).bind(this)

  isOtherOptionSelected = ->
    @$otherOptionRadioButton.is(':checked')

  startEditingOtherOption = ->
    @$otherOptionTextBox.val(@$otherOptionRadioButton.val())
    showOtherOptionTextBoxInstead.call(this)

  doneEditingOtherOption = ->
    userEnteredValue = @$otherOptionTextBox.val()
    if userEnteredValue.length != 0
      @$otherOptionLabel.text(userEnteredValue)
      @$otherOptionRadioButton.val(userEnteredValue)
    showOtherOptionLabelInstead.call(this)

  cancelEditingOtherOption = ->
    showOtherOptionLabelInstead.call(this)

  showOtherOptionTextBoxInstead = ->
    @$otherOptionLabel.addClass('is-hidden')
    @$otherOptionTextBox.removeClass('is-hidden')
    @$otherOptionTextBox.focus()

  showOtherOptionLabelInstead = ->
    @$otherOptionTextBox.addClass('is-hidden')
    @$otherOptionLabel.removeClass('is-hidden')

  constructor: ($selectize, name) ->
    @$container = $selectize
    @$options = @$container.children('.selectize-option')
    @$otherOption = @$options.last()
    @$otherOptionLabel = @$otherOption.find('label')
    @$otherOptionRadioButton = @$otherOption.find('input[type=radio]')
    @$otherOptionTextBox = @$otherOption.find('input[type=text]')

    bindings.call(this)

  value: ->
    @$options.find('input[type=radio]:checked').val()

  isValid: ->
    @value() != undefined
