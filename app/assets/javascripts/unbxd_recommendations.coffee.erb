root = exports ? this

root.loadRecommendation = (data) ->
  currency_code = data.symbol || 'INR'
  $.ajax
    url: "/unbxd_recommendations/#{data.unbxdContainer}"
    dataType: 'script'
    cache: true
    data:
      url_params:
        currency_code: currency_code
        uid: data.user
        pid: $('#unbxd-data-div').data('pid')
        category: $('#unbxd-data-div').data('category') || data.category
        brand: $('#unbxd-data-div').data('brand')
        product_count: data.productCount
        box_name: data.boxName
    success: (response) ->
      response
      $('#box-'+data.unbxdContainer).fadeIn('slow');
      # detectScroll();
      gaImpressions();
      <%# ga('send', 'event', 'UX', 'load', 'Unbxd-Widget-Loaded'); %>
      if ($('#no-'+data.boxName.toLowerCase()).length > 0)
        setTimeout (->
          $('#'+data.boxName.toLowerCase()).fadeOut('slow')
        ), 3000
    error : (xhr) ->
      $("div[data-unbxd-container ='#{data.unbxdContainer}']").replaceWith("<div class='container recommended-design-box' id='#{data.boxName}'><div class='title-block'><h1>" + data.boxName.toLowerCase().replace(/_/g, ' ') + "</h1></div><div class='row'><div class='no-design-box' id='no-#{data.boxName}'>Sorry Not Able to Find Recommendations</div></div></div>");
      setTimeout (->
        $('#'+data.boxName).fadeOut('slow')
      ), 3000
  return

if !root.bindView || root.bindView instanceof Array
  oldQueue = root.bindView || []

isCurrentlyVisible = ($element) ->
  top_of_element = $element.offset().top
  bottom_of_element = $element.offset().top + $element.outerHeight()
  bottom_of_screen = $(window).scrollTop() + window.innerHeight
  top_of_screen = $(window).scrollTop()
  return (bottom_of_screen > top_of_element) && (top_of_screen < bottom_of_element)

root.bindView = push: (data)->
  $element = $('div[data-unbxd-container="'+data+'"]')
  if $element.length > 0
    if isCurrentlyVisible($element)
      loadRecommendation $element.data()
    else
      $(document).one 'scroll', ->
        loadRecommendation $element.data()

for data in oldQueue
  bindView.push(data)

oldQueue = []

$ ->
  # not needed add if scroll buttons are there 
  # detectScroll = () ->
  #   $('.recommended-products').on 'scroll', ->
  #     elem=$(this);
  #     newScrollLeft = elem.scrollLeft();
  #     width = elem.width();
  #     scrollWidth = elem.get(0).scrollWidth;
  #     if (newScrollLeft >= width)
  #       $(this).siblings('.scroll-btn-right').fadeIn();
  #     if (scrollWidth - newScrollLeft - width == 0)
  #       $(this).siblings('.scroll-btn-left').fadeOut();
  #     else
  #       $(this).siblings('.scroll-btn-left').fadeIn();
  #     if (newScrollLeft == 0)
  #       $(this).siblings('.scroll-btn-right').fadeOut();