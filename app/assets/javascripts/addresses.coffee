# Place all the behaviors and hooks related to the matching controller here.
# All this logic will automatically be available in application.js.
# You can use CoffeeScript in this file: http://coffeescript.org/
//= require 'fuse.min.js'

$ ->
  # ga('send', 'event', {
    # eventCategory: 'Checkout',
    # eventAction: 'address-collect',
    # nonInteraction: true
  # })
  validatePhoneNumber("#address_country", "#address_phone")
  validatePhoneNumber("#address_shipping_address_country", "shipping_address_phone")

$ ->

  zipCountryCode = undefined
  zipRegex = undefined
  stateListFuse = undefined
  
  toogleDialCode = (dial_code, dial_code_block, dial_code_text) ->
    if !(dial_code?) || dial_code == ''
      $(dial_code_block).hide()
      $(dial_code_text).attr('value', '')
    else
      $(dial_code_block).show()
      $(dial_code_text).val('+' + dial_code)

  country_code = $('#country_code').val()
  if (country_code != undefined)
    $('#address_country').val(country_code).prop('selected', true);
    validatePhoneNumber("#address_country", "#address_phone")
    $('#address_shipping_address_country').val(country_code).prop('selected', true);
    validatePhoneNumber("#address_shipping_address_country", "#shipping_address_phone")

  
  $(document).on 'change', '.mark_as_default', (e) -> 
    $(this).parent().submit()
  if $('#address_country').length > 0
    createStateDropDown = (state_list) ->
      options = ''
      if state_list.length > 0
        $.each state_list, (index, state) ->
          options += "<option value = '" + state + "'>" + state + "</option>"
      options
      
  if $('#address_shipping_address_country').length > 0
    createStateDropDownShipping = (state_list) ->
      shipping_options = ''
      if state_list.length > 0
        $.each state_list, (index, state) ->
          shipping_options += "<option value = '" + state + "'>" + state + "</option>"
      shipping_options

  paramsGetStates = (country_code, id, selected_value, dial_code_block, dial_code_text) ->
    type: 'GET'
    url: '/country/get_states_and_dial_code'
    data:
      country: country_code
    success: (data, status, jqhxr) ->
      options = createStateDropDown(data.state_list)
      if options == ''
        html_data = '<input id="address_state" name="address[state]" size="30" required=true type="text" class="form-control">'
      else
        options = "<option value=''>Select State</option>" + options
        html_data = "<select id='address_state' class='form-control' name='address[state]' required=true>" + options + "</select>"
      $('#address_state').replaceWith(html_data)
      if data.state_list.length > 0
        stateOptions = $.map $('#address_state option').slice(1), (option) -> { value: $(option).val() }
        fuseOptions = { shouldSort: true, threshold: 0.85, location: 0, distance: 100, maxPatternLength: 32, minMatchCharLength: 2, keys: ["value"] }
        stateListFuse = new Fuse(stateOptions, fuseOptions)
      if selected_value? && $.inArray(selected_value, data.state_list)
        $('#address_state').val(selected_value)
      if $(dial_code_block).length > 0
        toogleDialCode(data.dial_code, dial_code_block, dial_code_text)
  
  paramsGetStatesShipping = (country_code, id, selected_value, dial_code_block, dial_code_text) ->
    type: 'GET'
    url: '/country/get_states_and_dial_code'
    data:
      country: country_code
    success: (data, status, jqhxr) ->
      shipping_options = createStateDropDownShipping(data.state_list)
      if shipping_options == ''
        html_data_shipping = '<input id="address_shipping_address_state" name="address[shipping_address][state]" size="30" type="text" class="form-control">'
      else
        shipping_options = "<option value=''>Select State</option>" + shipping_options
        html_data_shipping = "<select id='address_shipping_address_state' class='form-control' name='address[shipping_address][state]'>" + shipping_options + "</select>"
      $('#address_shipping_address_state').replaceWith(html_data_shipping)
      if data.state_list.length > 0
        stateOptions = $.map $('#address_shipping_address_state option').slice(1), (option) -> { value: $(option).val() }
        fuseOptions = { shouldSort: true, threshold: 0.85, location: 0, distance: 100, maxPatternLength: 32, minMatchCharLength: 2, keys: ["value"] }
        stateListFuse = new Fuse(stateOptions, fuseOptions)
      if selected_value? && $.inArray(selected_value, data.state_list)
        $('#address_shipping_address_state').val(selected_value)
      if $(dial_code_block).length > 0
        toogleDialCode(data.dial_code, dial_code_block, dial_code_text)
        
  showPincodeFormat = (country_code, id, pincode_format_notice, pincode)  ->
    type: 'GET'
    url: '/country/' + country_code + '/get_pincode_format'
    success: (data, status, jqhxr) ->
      pincode_format = data[0][0]
      zipCountryCode = data[1][0]
      zipRegex = new RegExp(data[1][1],'i')
      if pincode_format != "" && pincode_format != null
        $('#showpincodefields').show()
        $(pincode_format_notice).text "Example : " +pincode_format
      else
        $(pincode).val("None")
        $('#showpincodefields').hide()
  $(document).on 'change', '#address_country', (e) ->
    if $(this).val() != ''
      $('#address_city').val("")
      $('#address_pincode').val("")
      $.ajax(paramsGetStates($(this).val(), $(this)[0].id, $('#address_state').val(), '.dial_code_block', '#dial_code_text'))
      $.ajax(showPincodeFormat($(this).val(), $(this)[0].id, '#pincode_format_notice', '#address_pincode'))
  if $('#address_country').val() != undefined
    $.ajax(showPincodeFormat($('#address_country').val(), $('#address_country')[0].id, '#pincode_format_notice', '#address_pincode'))
    $.ajax(paramsGetStates($('#address_country').val(), $('#address_country')[0].id, $('#address_state').val(), '.dial_code_block', '#dial_code_text'))
  $(document).on 'change', '#address_shipping_address_country', (e) ->
    if $(this).val() != ''
      $('#address_shipping_address_city').val("")
      $('#address_shipping_address_pincode').val("")
      $.ajax(paramsGetStatesShipping($(this).val(), $(this)[0].id, $('#address_shipping_address_state').val(),'.dial_code_block_shipping', '#dial_code_text_shipping'))
      $.ajax(showPincodeFormat($(this).val(), $(this)[0].id, '#shipping_pincode_format_notice', '#address_shipping_address_pincode'))
  
  if $('#address_shipping_address_country').val() != undefined
    $.ajax(showPincodeFormat($('#address_shipping_address_country').val(), $('#address_shipping_address_country')[0].id, '#shipping_pincode_format_notice', '#address_shipping_address_pincode'))
    $.ajax(paramsGetStatesShipping($('#address_shipping_address_country').val(), $('#address_shipping_address_country')[0].id, $('#address_shipping_address_state').val(), '.dial_code_block_shipping', '#dial_code_text_shipping'))

  $(document).on 'click', '#shipping_address', (e) ->
    ship_value = 0
    if $('#shipping_address:checked').length > 0
      ship_value = 1
    $('#ship_to_same_address').attr('value', ship_value)

  paramInternationalFetchCityStateOptions = (country_code, pincode, address_city, address_state) ->
    type: 'GET'
    url: '//api.zippopotam.us/'+country_code+'/'+pincode
    datatype: 'JSON'
    success: (data, status, jqhxr) -> 
      if data['places'] != undefined && data['places'][0] !=undefined
        $(address_city).val(data['places'][0]['place name'])
        state_name = data['places'][0]['state']
        if state_name == '' || state_name == undefined
          state_code = data['places'][0]['state abbreviation']
          if state_code == '' || state_code == undefined
            state_name = data['places'][0]['place name']
          else
            state_name = state_code
        if stateListFuse != undefined && (state_option = stateListFuse.search(state_name)[0]) != undefined
          state_name = state_option.value
        if state_name != undefined
          $(address_state).val(state_name).prop('selected', true)

  paramsGetCityState = (pincode, address_city, address_state) ->
    type: 'GET'
    data:
      pincode: pincode
    url: '/api/v1/addresses/pincode_info'
    datatype: 'JSON'
    success: (data, status, jqhxr) ->
      if data
        $(address_city).val(data.city_name)
        $(address_state).val(data.state).prop('selected', true);

  CityStateAutoFill = (address_pincode, address_country, address_city, address_state) ->
    pincode = $(address_pincode).val()
    country = $(address_country).val()
    if country == 'India'
      if pincode.length == 6
        $.ajax(paramsGetCityState(pincode, address_city, address_state))
    else if (zipCountryCode?) && (zipRegex?) && zipRegex.test(pincode)
      $.ajax(paramInternationalFetchCityStateOptions(zipCountryCode, pincode, address_city, address_state))

  CityStateAutoFill('#address_pincode', '#address_country', '#address_city', '#address_state')
  CityStateAutoFill('#address_shipping_address_pincode', '#address_shipping_address_country', '#address_shipping_address_city', '#address_shipping_address_state')

  $(document).on 'keyup paste', '#address_pincode', (e) ->
    if e.target.id == 'address_pincode'
      CityStateAutoFill('#address_pincode', '#address_country', '#address_city', '#address_state')
        
  $(document).on 'keyup paste', '#address_shipping_address_pincode', (e) ->
    if e.target.id == 'address_shipping_address_pincode'
      CityStateAutoFill('#address_shipping_address_pincode', '#address_shipping_address_country', '#address_shipping_address_city', '#address_shipping_address_state')

  $(document).on 'change', '#address_country', (e) ->
    if $(this).val() == 'India'
      $('#address_pincode').attr({type:'tel', maxlength:'6' , pattern:"^[1-9][0-9]{5}$", placeholder: 'Pincode', title: 'Please enter 6 digit pincode'})
    else
      $('#address_pincode').removeAttr('pattern title')
      $('#address_pincode').attr({type:'text', maxlength:'15', placeholder: 'Zipcode'}) 
    validatePhoneNumber("#address_country", "#address_phone")
    
  $(document).on 'change', '#address_shipping_address_country', (e) ->
    if $(this).val() == 'India'
      $('#address_shipping_address_pincode').attr({type:'tel', maxlength:'6' , pattern:"^[1-9][0-9]{5}$", placeholder: 'Pincode', title: 'Please enter 6 digit pincode'})
    else
      $('#address_shipping_address_pincode').removeAttr('pattern title')
      $('#address_shipping_address_pincode').attr({type:'text', maxlength:'15', placeholder: 'Zipcode'}) 
    validatePhoneNumber("#address_shipping_address_country", "#shipping_address_phone")
$ ->
  $(document).scroll ->
    if $('#address_collect_submit').length > 0
      stickyButton(address_collect_submit,grand_total_price_cart,2.8)

validatePhoneNumber = (country, phoneNumber) ->
  phoneNumberInput = $(phoneNumber)
  pattern = switch $(country).val()
    when 'United States'
      "^[0-9]{10}$"
    when 'India'
      "^[6-9][0-9]{9}$"
    when 'UAE'
      "^[0-9]{9}$"
    else
      "^[0-9]{6,20}$"
  phoneNumberInput.attr({pattern: pattern, title: "Please enter a valid Mobile Number" })
  phoneNumberInput.on 'input', ->
    customValidityMessage = switch $(country).val()
      when 'United States'
        if @validity.patternMismatch
          "Please enter a 10 digit Mobile Number"
        else
          ""
      when 'India'
        if @validity.patternMismatch
          "Please enter a valid 10 digit Mobile Number starting with 6-9"
        else
          ""
      when 'UAE'
        if @validity.patternMismatch
          "Please enter a 9 digit Mobile Number"
        else
          ""
      else
        if @validity.patternMismatch
          "Please enter a valid Mobile Number"
        else
          ""
    @setCustomValidity(customValidityMessage)
    
$ ->
  $addresses = $('.billing_address')
  $defaultAddress = $addresses.filter('.default-address')
  $addresses = $addresses.not($defaultAddress)
  $('.top-address').prepend($defaultAddress)

$(document).ready ->
  shippingAddressFields = $ '.shipping_name, .shipping_lname, .shipping_email, .shipping_phone, .shipping_code, .shipping_city, .shipping_state, .shipping_address_1, .shipping_address_2'
  $('#shipping_address').change ->
    is_checked = $(@).is ':checked'
    if is_checked
      $('#shipping-address').css 'display', 'none'
      shippingAddressFields.each ->
        $(@).prop 'required', false
        $(@).closest('.form-group').hide()
    else
      $('#shipping-address').css 'display', 'block'
      shippingAddressFields.each ->
        $(@).prop 'required', true
        $(@).closest('.form-group').show()