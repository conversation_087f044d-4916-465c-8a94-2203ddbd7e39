# Place all the behaviors and hooks related to the matching controller here.
# All this logic will automatically be available in application.js.
# You can use CoffeeScript in this file: http://coffeescript.org/
$ ->
  if $('#login_modal').length > 0
    $('#login_modal').foundation('reveal', 'open');

  $('#close_modal').click ->
    $('#login_modal').foundation('reveal', 'close');

  $('#inches,#feet').change ->
    inches = parseInt $('#inches').val()
    feet = parseInt $('#feet').val()
    if !isNaN(inches)
      in_inches = feet * 12 + inches
      in_cms = Math.round(in_inches * 2.54 * 100) / 100
      $('#height').val("#{feet}.#{inches}")
      $('#conversion_height').html("In Inches: #{in_inches} inches  In Cms: #{in_cms} cms")
    else
      $('#conversion_height').html('')

  $('#weight').keyup ->
    weight = parseInt($(this).val())
    $(this).val(0) if weight < 0
    $(this).val(200) if weight > 200
    weight = parseInt($(this).val())
    in_pounds = Math.round(weight * 2.20 * 100) / 100
    $('#conversion_weight').html("In Pounds: #{in_pounds}")  unless isNaN(weight)

  $('#measurement_data_1').on('submit',(e) -> 
    e.preventDefault()
    if $('#weight').val() == ''
      alert 'Weight is required'
    else if $('#weight').val() < 20
      alert 'Weight Should be more than 20'
    else
      form_data = $(this).serialize()
      $.ajax(paramGetMeasurmentForm(form_data))
    )
  $('#measurement_select').change ->
    inches = parseInt $('#inches').val()
    feet = parseInt $('#feet').val()
    $('#height').val(parseFloat("#{feet}.#{inches}"))
    form_data = $('#measurement_data_1').serialize()
    $.ajax(paramGetMeasurmentForm(form_data))

  paramGetMeasurmentForm =(form_data) ->
    url: 'stitching_measurement/measurement_data'
    type: 'GET'
    data:
      form_data
    datatype: 'script'
    beforeSend: ->
      $('#loadingImage').show()
    complete: ->
      $('#loadingImage').hide()
    success: (data,success,jqhxr) ->
      $(document).foundation()
      $('.example-orbit').css('height','200px')


  $(document).on 'click', '.similar-img-check', ->
    id = $(this).data('id')
    checkBoxCheckEvent(id)

  checkBoxCheckEvent = (id) ->
    check_box = $("#item_#{id}")
    if check_box.is(':checked')
      check_box[0].checked = false
      $("#check_#{id}").removeClass('selected_style')
      $('.item_checkbox:checked').each ->
        item_id = $(this).val()
        $("#check_#{item_id}").addClass('selected_style')
    else
      check_box[0].checked = true
      $('.item_checkbox:checked').each ->
        id = $(this).val()
        $("#check_#{id}").addClass('selected_style')

# ------ Separation between the bright and the dark side ------

class MeasurementExperienceForm
  bindings = ->
    @$form.on 'submit', ((event) ->
      event.preventDefault()

      disable.call(this)
      @callback(experienceIndex.call(this))
    ).bind(this)

    @$form.find('.measurement-experience-options').on 'change', 'input[type=radio]', (->
      enableSubmit.call(this)
    ).bind(this)

  experienceIndex = ->
    @$form.find('.measurement-experience-options input[type=radio]:checked').val()

  disable = ->
    @$form.hide()

  enableSubmit = ->
    @$form.find('input[type=submit]').removeAttr('disabled')

  constructor: ($form) ->
    @$form = $form

    bindings.call(this)

  afterSubmit: (callback) ->
    @callback = callback

class StitchingForm
  bindings = ->
    @measurementExperienceForm.afterSubmit ((experienceIndex) ->
      setMeasurementExperience.call(this, experienceIndex)
      enable.call(this)
    ).bind(this)

  enable = ->
    @$container.show()
    scrollToForm.call(this)

  disable = ->
    @$container.hide()

  scrollToForm = ->
    $('html, body').animate({
      scrollTop: 0
    }, 300)

  setMeasurementExperience = (experience) ->
    @$form.find('input[type=hidden][name=measurement_experience]').val(experience)

  housekeeping = ->
    disable.call(this)

    bindings.call(this)

  constructor: ($container, measurementExperienceForm) ->
    @$container = $container
    @$form = @$container.find('#measurement_data_1')
    @measurementExperienceForm = measurementExperienceForm

    housekeeping.call(this)

$ ->
  new StitchingForm($('ul.accordion'), new MeasurementExperienceForm($('.measurement-experience')))
