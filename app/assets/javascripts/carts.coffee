# Place all the behaviors and hooks related to the matching controller here.
# All this logic will automatically be available in application.js.
# You can use CoffeeScript in this file: http://coffeescript.org/
$ ->
  $(document).on 'change', '.quantity_list', (e) ->
    id = this.id.split('_').pop()
    url = '/line_items/' + id
    $.ajax
      url: url
      type: 'PUT'
      dataType: 'json'
      data:
        line_items:
          id: id
          quantity: $(this).val()
      success: (response) ->
        if response.ga_hash
          sendUpdateCartInfoToGa(response.ga_hash, response.country_code,response.ads_data)
        if Turbolinks.supported
          Turbolinks.visit response.redirect_url
        else
          window.location.href = response.redirect_url

  $(document).on "ajax:success", "a.remove_from_cart , a.move_to_wishlist",  (evt, data, status, xhr) ->
    if data.ga_hash
      sendRemoveFromCartInfoToGa(data.ga_hash)
    if Turbolinks.supported
      Turbolinks.visit data.redirect_url
    else
      window.location.href = data.redirect_url

  sendUpdateCartInfoToGa = (ga_hash, country_code,ads_data) ->
    if ga_hash.quantity
      ga_action = if ga_hash.quantity > 0 then 'add_to' else 'remove_from'
      ga_hash.quantity = Math.abs(ga_hash.quantity)
      window.dataLayer = window.dataLayer || [];
      
      dataLayer.push({ ecommerce: null });
      dataLayer.push({
        event: 'ga4_'+ga_action+'_cart',
        ecommerce:{
          currency: 'INR',
          value: (ga_hash.price +  ga_hash.item_customization) * ga_hash.quantity,
          country_code: country_code,
          items: [ga_hash],
          contents: ads_data[0].contents,
          content_ids: ads_data[0].content_ids,
          item_ids: ads_data[0].item_ids
        }
      });

  sendRemoveFromCartInfoToGa = (ga_hash) ->
    # ga 'ec:addProduct', ga_hash
    # ga 'ec:setAction', 'remove'
    # ga 'send', 'event', 'UX', 'click', 'remove from cart'

  $(document).on 'click', 'a.giftwrapped', (e) ->
    $.ajax
      url: '/carts/add_gift_wrap_price'
      type: 'POST'
      dataType: 'json'
      success: (response) ->
        if Turbolinks.supported
          Turbolinks.visit response.redirect_url
        else
          window.location.href = response.redirect_url


$ ->
  $(window).scroll ->
    stickyButton("#fixed_checkout_button",action_buttons,1.6)

$ ->
  $(document).on 'click', '.view_details_button', (event) ->
    $('html, body').animate { scrollTop: $('#totals_block').offset().top - 50 }, 500
    

  # $footer = $('#secondary_action_buttons')
  # $win = $('#main-section')
  # $container = $('#container')
  # ipos = $container.height()

  $(document).on 'click', '.coupon', (event) ->
    if $('.wallet_error').is(':visible')
      $('.wallet_error').html ''
    $('.coupon-button').removeClass 'enableLink'
    $('.wallet-button').addClass 'enableLink'
    $('.apply_coupon .coupon, .apply_wallet .wallet-box, #apply_coupon_or_wallet').hide()
    $('.apply_coupon .coupon-box').fadeIn 400

  $(document).on 'click', '.wallet', (event) ->
    $('.wallet-button').removeClass 'enableLink'
    $('.coupon-button').addClass 'enableLink'
    $('.apply_coupon .coupon-box, #apply_coupon_or_wallet').hide()
    $('.apply_wallet .wallet-box').fadeIn 400

  emailTrim = $('.panel_heading .email_display').text()
  if $.trim(emailTrim).length
    $('.panel_heading .email_form').hide()
  else
    $('.panel_heading .email_form, .email_display').hide()
    
  emailTrimDesktop = $('.panel_heading .email_display_desktop').text()
  if $.trim(emailTrimDesktop).length
    $('.panel_heading .email_form').hide()
  else
    $('.panel_heading .email_form, .email_display_desktop').hide()
  
  $(document).on 'click', '.panel_heading .save_email', (event) ->
    $('.panel_heading .save_email, .email_display').hide()
    $('.panel_heading .save_email, .email_display_desktop').hide()
    $('.panel_heading .email_form').fadeIn 400

  paramEmailSave = (email) ->
    $.post '/cart/save_email', {
      cart_id: $('#cart_id').val()
      email: email
    }, ((response) ->
      if response.success == '1'
        $('.cart-alert').remove()
        $('.panel_heading .email_form').hide()
        $('.panel_heading .save_email, .email_display').fadeIn 400
        if $('#email_box').val() != ''
          $('.panel_heading .email_display').html($('#email_box').val())
      else if response.success == '0'
        text_msg = "<div data-alert class='alert-box warning radius cart-alert'>Oops! Something went wrong.</div>"
        $('#carts_block').prepend(text_msg)
      else
        $('#email_box').val ''
    ), 'json'
    
  paramEmailSaveDesktop = (email) ->
    $.post '/cart/save_email', {
      cart_id: $('#cart_id').val()
      email: email
    }, ((response) ->
      if response.success == '1'
        $('.cart-alert').remove()
        $('.panel_heading .email_form').hide()
        $('.panel_heading .save_email, .email_display_desktop').fadeIn 400
        if $('#email_box_desktop').val() != ''
          $('.panel_heading .email_display_desktop').html($('#email_box_desktop').val())
      else if response.success == '0'
        text_msg = "<div data-alert class='alert-box warning radius cart-alert'>Oops! Something went wrong.</div>"
        $('#carts_block').prepend(text_msg)
      else
        $('#email_box_desktop').val ''
    ), 'json'
       
  $(document).on 'submit', '#save-cart-email', (event) ->
    event.preventDefault()
    email = $(this).find('#email_box').val()
    email1 = $(this).find('#email_box_desktop').val()
    reEmail = /^([\w-\.]+)@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.)|(([\w-]+\.)+))([a-zA-Z]{2,4}|[0-9]{1,3})(\]?)$/
    if reEmail.test(email)
      paramEmailSave email
      true
    else if reEmail.test(email1)
      paramEmailSaveDesktop email1
      true
    else
      alert 'Please enter valid Email'
      false

$ ->
  $('#checkout_button').click (e) ->
    e.preventDefault()
    if $('.out_of_stock').length > 0
      if $('.oos_error').length == 0
        $('.columns.panel_content').prepend '<div class="minimum_cart_value_message oos_error">Cart contains out of stock product please remove them before checkout</div>'
    else
      window.location.href = $(this).attr('href')

#  scrollHeight = (e) ->
#    wpos = $win.scrollTop()
#    winh = $win.height()
#    space = $win.height() - $footer.height()/2
#    if (ipos - wpos < winh)
#      $footer.slideDown()
#    else
#      $footer.slideUp()
#
#  $(window).ready(scrollHeight).resize(scrollHeight)
#  $win.scroll(scrollHeight)

  $(document).on 'click', '.add_cart_addon', ->
    design_id = $(this).data('id')
    if $(this).is(":checked")
      quantity = +$('#cart_addon_quantity_'+design_id).val()
      addCartAddon(design_id, quantity)
    else
      removeCartAddon(design_id)

addCartAddon = (design_id, quantity) ->
  item = [{design_id: design_id, quantity: quantity}]
  $.ajax
    url: '/line_items'
    type: 'POST',
    dataType: 'JSON',
    data:
      line_items: item
      design_page: true
    success: (response, status, jqxhr) ->
      window.location.assign(response.redirect_url)

removeCartAddon = (item_id) ->
  $.ajax
    url: '/line_items/'+item_id,
    type: 'POST',
    dataType: 'script',
    data:
      _method: "delete"