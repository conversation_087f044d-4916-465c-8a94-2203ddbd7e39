class @SavedCard extends OtherPayment
  initialize = ->
    @$IDInput.each ->
      if $(this).data("type") == "AMEX"
        new Cleave(this, {blocks: [4]})
      else
        new Cleave(this, {blocks: [3]})

  bindings = ->
    @$typeDeciderElements.on 'change', (event) =>
      $selector = $(event.target)

      if $selector.context.nodeName == "SELECT"
        $selector = $selector.find(':selected')

      updateInputType.call(this, $selector.data("token"))

  updateInputType = (token) ->
    @cleave.destroy() if @cleave != undefined
    @$IDInput.val('')
    if token != undefined
      message = "CVV"
      @$IDInput.attr("disabled", true)
      @$IDInput.attr("type", "hidden")
      $('.'+token).attr("disabled", false)
      $('.'+token).attr("type", "password")
    else
      @$IDInput.attr("disabled", true)
      @$IDInput.attr("type", "hidden")

    @$IDInput.attr("placeholder", message)

  constructor: (container) ->
    super(container)

    @$IDInput = $('.cvv-input')
    @$typeDeciderElements = $('.radio-container').find('input[type=radio], select')

    initialize.call(this)
    updateInputType.call(this)
    bindings.call(this)