//= require addons/cleave-phone.in
class @CardPayment
  initialize = ->
    @cvvCleave = new Cleave(@cvvSelector, {
      numericOnly: true,
      blocks: [3]})
    @cardCleave = new Cleave(@cardSelector, {
      creditCard: true,
      creditCardStrictMode: false,
      onCreditCardTypeChanged: onCardChange,
      blocks: [4]
    })
    @expiryCleave = new Cleave(@expirySelector, {date: true, datePattern: ['m', 'y']})
    
  onCardChange = (type) ->
    @cvvCleave.destroy()
    if type == "amex"
      @cvvCleave = new Cleave(@cvvSelector, {
        numericOnly: true,
        blocks: [4]})
    else
      @cvvCleave = new Cleave(@cvvSelector, {
        numericOnly: true,
        blocks: [3]})
      mastercardDiscount(type)

    changeCardIcon(type)

  mastercardDiscount = (type) ->
    selected_option = $(".accordion-navigation.active")
    mastercard_discount = selected_option.data('mastercardDiscount')
    if mastercard_discount == 0
      return
    else
      symbol = selected_option.data('symbol')
      grandtotal = selected_option.data('grandtotal')
      prepaid_discount = selected_option.data('prepaidDiscount')
      shipping_discount = selected_option.data('shipping')
      if type == "mastercard"
        $('.card_message').show()
        $('.card_message').html('Mastercard Discount is applied!')
        $('.prepaid_discount').hide()
        $('#prepaid_discount').val(0)
        $('.mastercard_discount').show()
        $('#mastercard_discount').val(gon.mastercard_discount)
        if selected_option.data('prepaidShippingPromo') == 'available'
          $('#shipping_charge').html('Shipping : ' + ('FREE').fontcolor('green').bold())
          computeGrandTotal(symbol, grandtotal, 0, mastercard_discount, shipping_discount)
        else
          $('#shipping_charge').html('Shipping : ' + symbol + ' ' + shipping_discount)
          computeGrandTotal(symbol, grandtotal, 0, mastercard_discount, 0)
      else
        $('.card_message').hide()
        $('.mastercard_discount').hide()
        if $('.prepaid_discount').length > 0
          $('.prepaid_discount').show()
          $('#prepaid_discount').val(gon.prepaid_discount)
        $('#mastercard_discount').val("0")
        if selected_option.data('prepaidShippingPromo') == 'available'
          $('#shipping_charge').html('Shipping : ' + ('FREE').fontcolor('green').bold())
          computeGrandTotal(symbol, grandtotal, prepaid_discount, 0, shipping_discount)
        else
          $('#shipping_charge').html('Shipping : ' + symbol + ' ' + shipping_discount)
          computeGrandTotal(symbol, grandtotal, prepaid_discount, 0, 0)

  computeGrandTotal = (symbol, grandtotal, prepaid_discount, mastercard_discount, shipping_discount) ->
    total = grandtotal - prepaid_discount - mastercard_discount - shipping_discount
    total_with_symbol = symbol + ' ' + total
    $('.grand_total').html('Grand Total : ' + total_with_symbol)
    $('.grand_total.top').html(total_with_symbol)

  changeCardIcon = (type) ->
    $(@cardIconSelector).removeClass (index, className) ->
      (className.match(/pf-.+/) || []).join('')

    $(@cardIconSelector).addClass("pf-#{type}")

  getCardNumber = ->
    @cardCleave.getRawValue()

  getCvv = ->
    @cvvCleave.getRawValue()

  getMonth = ->
    @expiryCleave.getFormattedValue().split('/')[0]

  getYear = ->
    @expiryCleave.getFormattedValue().split('/')[1]

  getMerchantID = ->
    $('#merchant-id').val()

  getApiURL = ->
    $('#apiURL').val()

  getEncodedBody = ->
    cardDetails =
      "card_number": getCardNumber(),
      "card_exp_year": getYear(),
      "card_exp_month": getMonth(),
      "card_security_code": getCvv(),
      "merchant_id": getMerchantID()

    formBody = []

    for property of cardDetails
      encodedKey = encodeURIComponent(property)
      encodedValue = encodeURIComponent(cardDetails[property])
      formBody.push encodedKey + '=' + encodedValue
    formBody = formBody.join('&')

  isSelected: ->
    @accordionNavigation.hasClass('active')

  clear: ->
    @cardCleave.setRawValue('')
    @cvvCleave.setRawValue('')
    @expiryCleave.setRawValue('')

  isValid: ->
    getCardNumber().length >= 14 && getCvv().length >= 3 && getMonth().length > 0 && getYear().length > 0


  constructor: (cardSelector, expirySelector, cvvSelector, cardIconSelector, accordionNavigation) ->
    initialize = initialize.bind(this)
    onCardChange = onCardChange.bind(this)
    changeCardIcon = changeCardIcon.bind(this)
    getCardNumber = getCardNumber.bind(this)
    getCvv = getCvv.bind(this)
    getMonth = getMonth.bind(this)
    getYear = getYear.bind(this)
    getMerchantID = getMerchantID.bind(this)
    getEncodedBody = getEncodedBody.bind(this)
    getApiURL = getApiURL.bind(this)

    @apiURL = getApiURL() + "/card/tokenize"

    @cardSelector = cardSelector
    @expirySelector = expirySelector
    @cvvSelector = cvvSelector
    @cardIconSelector = cardIconSelector
    @accordionNavigation = accordionNavigation


    initialize()

  makePayment: ->
    fetch(@apiURL, {
      method: 'POST',
      body: getEncodedBody(),
      headers: {
        "Content-Type": "application/x-www-form-urlencoded"
      }
    }).then((res) ->
      res.json()
    ).then((data) ->
      if data['status'] == "error"
        if data['error_message'].includes('[card_security_code]')
         $('#card-error').val("CVV is not present")
        else
         $('#card-error').val(data['error_message'])
      else
        $('#card-token').val(data['token'])
      $("#new_order").trigger('submit')
    ).catch (error) ->
      console.log error