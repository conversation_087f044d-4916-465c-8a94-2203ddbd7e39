class @UPIPayment extends OtherPayment
  bindings = ->
    @$typeDeciderElements.on 'change', (event) =>
      $selector = $(event.target)

      if $selector.context.nodeName == "SELECT"
        $selector = $selector.find(':selected')

      updateInputType.call(this, $selector.data("type"))

  updateInputType = (type) ->
    @cleave.destroy() if @cleave != undefined
    @$IDInput.val('')
    switch type
      when "vpa"
        message = "Enter VPA"
        @$IDInput.attr("disabled", false)
        @$IDInput.attr("type", "text")
        break
      when "mobile"
        message = "Enter phone number"
        @cleave = new Cleave(@$IDInput.get()[0], {
          phone: true,
          phoneRegionCode: 'IN'
        })
        @$IDInput.attr("disabled", false)
        @$IDInput.attr("type", "text")
        break
      else
        message = "Please select an option"
        @$IDInput.attr("disabled", true)

    @$IDInput.attr("placeholder", message)

  constructor: (container) ->
    super(container)

    @$IDInput = $('.payment-upi-id input')
    @$typeDeciderElements = @$container.find('input[type=radio], select')

    updateInputType.call(this)
    bindings.call(this)