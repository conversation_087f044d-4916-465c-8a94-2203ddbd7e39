var PRODUCT_CART_INFO = PRODUCT_CART_INFO || {};

PRODUCT_CART_INFO = (function(window, document, PRODUCT_CART_INFO){
    PRODUCT_CART_INFO.events = {
        init: function(ga4_checkout_params) {
            PRODUCT_CART_INFO.events.triggerBeginCheckout(ga4_checkout_params)
        },

        triggerBeginCheckout: function(ga4_checkout_params){
            $('#checkout_button,#cart_checkout_button').click(function(e) {
                if ($('.out_of_stock').length ==  0) {
                    dataLayer.push({ ecommerce: null });
                    dataLayer.push({
                        event: "ga4_begin_checkout",
                        ecommerce: ga4_checkout_params
                    });
                };
            });
        }

    };
    return PRODUCT_CART_INFO;
})(this, this.document, PRODUCT_CART_INFO);