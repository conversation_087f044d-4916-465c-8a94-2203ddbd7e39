root = exports ? @
root.loadAsyncReactComponent = (component, args) ->
  $.getJSON args.url, (data) -> 
    props = data
    if args.extraDataOnLoad
      $.extend(props, args.extraDataOnLoad)
    $("[data-react-async-class='#{component}']").each ->
      React.render(React.createElement(eval.call(window, component), props), @)

root.defaultLoadImg = (img, event)->
  $(img.target).attr 'src', "<%= asset_path('default_image.jpg') %>"