//= require handlebars
//= require unbxdAutosuggest
(unbxdAutoSuggestFunction($, Handlebars));

$(window).on("orientationchange",function(){ $('.unbxd-as-maincontent').hide() });

bindUnbxdSearch = function() {
  $('.search_margin input.search-trending').unbxdautocomplete({
    siteName : "<%=MirrawMobile::Application.config.unbxd[:UNBXD_SITE_KEY]%>",
    APIKey : "<%=MirrawMobile::Application.config.unbxd[:UNBXD_API_KEY]%>" ,
    minChars : 1,
    maxSuggestions: 0,
    delay : 10,
    loadingClass : 'unbxd-as-loading',
    mainWidth : 0,
    sideWidth : 180,
    zIndex : 100,
    position : 'fixed',
    template : "1column" ,
    mainTpl: ["inFields","keywordSuggestions", "topQueries", "popularProducts"] ,
    sideTpl: [],
    sideContentOn : "right",
    showCarts : false,
    cartType : "separate",
    // onSimpleEnter : function(){   this.input.form.submit();  },
    onSimpleEnter : function(){
      var value = $('#search_input').val();
      if (value) {
        this.input.form.submit();
      }
    },
    onItemSelect: function(data, original) {
      this.input.form.submit()
    },
    inFields: {
      count: 0,
      fields: {
        designer: 3,
        catelevel2name: 3,
        catelevel3name:3
      }
    },
    topQueries: {
      count: 2,
      header: 'Top searches'
    },
    keywordSuggestions: {
      count: 3,
      header: 'By Keyword'
    },
    popularProducts: {
      count: 2,
      price: false,
      header: 'By Popularity'
    }
  });
}

bindUnbxdSearch();

$(document).on('turbolinks:render', function(){
  bindUnbxdSearch();
});