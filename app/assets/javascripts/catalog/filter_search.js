
var MR = MR || {};
MR = (function(window, document, Mirraw) {
  Mirraw.accordionSearch = {
    init: function() {
      this.bindSearchEvent();
    },

    bindSearchEvent: function() {
      $('.accordion-item .search-field').on('input', function() {
        var $input = $(this);
        var query = $input.val().toLowerCase();
        var filterName = $input.closest('.accordion-item');
        MR.accordionSearch.filterItems(filterName, query);
      });
    },
    
    filterItems: function(parentClass, query) {
      var $listItems = parentClass.find('.single_line_div');
      $listItems.each(function() {
        var $listItem = $(this);
        var listItemText = $listItem.text().toLowerCase();
        $listItem.toggle(listItemText.includes(query));
      })
    }
  };
  return Mirraw;
})(this, this.document, MR);
