var MR = MR || {};
MR = (function(window, document, Mirraw) {
  Mirraw.store = {
    init: function() {
      this.keepSelectedPropertiesActive();
      this.bindClearAllEvents();
    },

    bindClearAllEvents: function() {
      // Ensure event bindings have proper scope references
      $(document).ready(function() {
        MR.store.initializeClearAllButtons();
      });
    
      $(document).on('turbolinks:load', function() {
        MR.store.initializeClearAllButtons();
      });
    
      $(document).on('change', 'input:checkbox, input:radio', function() {
        MR.store.toggleClearAllButton($(this).closest('.accordion-item'));
      });
    
      $(document).on('click', '.clear-all-btn', function() {
        var accordionBody = $(this).closest('.accordion-item');
        MR.store.clearFilters(accordionBody);
        MR.store.updateUrl();
      });
    },

    keepSelectedPropertiesActive: function() {
      var selected_property = window.sessionStorage.getItem('last-filter');
      if (!$('#' + selected_property).length > 0) {
        selected_property = 'price';
      }
      var selected_tab = $('#' + selected_property).attr('href');
      $('nav > .tab-filter-fix').removeClass('active-tab');
      $('#' + selected_property).addClass('active-tab');
      $('.tabs-content > .content').removeClass('active').addClass('content');
      $(selected_tab).addClass('content active');
    },

    toggleClearAllButton: function(accordionBody) {
      var clearAllBtn = accordionBody.find('.clear-all-btn');
      clearAllBtn.toggle(accordionBody.find('input:checked').length > 0);
    },

    initializeClearAllButtons: function() {
      $('.accordion-item').each(function() {
        MR.store.toggleClearAllButton($(this));
      });
    },

    clearFilters: function(accordionBody) {
      var checkboxes = accordionBody.find('input:checked');
      checkboxes.prop('checked', false);
      window.remove_hidden_range_filter_input(checkboxes.attr('data-max'))
      window.remove_hidden_range_filter_input(checkboxes.attr('data-min'))
      window.updateSelectedFiltersDesktop();
      checkboxes.each(function() {
        $(".chip[data-remove='" + $(this).attr('id') + "']").remove();
      });
      MR.store.toggleClearAllButton(accordionBody);
    },

    updateUrl: function() {
      var url = '';
      var valueString = window.appendFiltersDesktop(url)
      if (valueString && !valueString.includes('?')) {
        valueString = valueString.replace('&', '?');
      }      
      var correct_url = window.checkForColourInUrl($('#facet-data').attr('data-url')) + valueString;
      
      if (Turbolinks.supported) {
        Turbolinks.visit(correct_url);
      } else {
        window.location.assign(correct_url);
      }
    }
  }; 
  return Mirraw;
})(this, this.document, MR);

afterWindowOrTrubolinksLoad(MR.store.keepSelectedPropertiesActive())