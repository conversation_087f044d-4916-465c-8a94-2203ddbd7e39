# ajax login + address form not required now, because it is now merge into one page
# $ ->
#    $('.toggle_loggin_forms').click -> $('.existing_user_login_form, .guest_login_form').toggle()
#
# jQuery(document).ready ($) ->
#   $(window).on 'popstate', ->
#     toggleAddressCollectForm()
#     return
#   return
# toggleGuestLoginForm = undefined
# 
# toggleGuestLoginForm = ->
#   $('.email_field_div').toggle()
#   $('.shipping_step .stepNumber').removeClass('inactiveStep').addClass 'activeStep'
#   $('.address_field_div').toggle()
#   return
# 
# toggleAddressCollectForm = ->
#   $('.shipping_step .stepNumber').removeClass('activeStep').addClass 'inactiveStep'
#   $('.login_step .stepNumber').removeClass 'doneStep'
#   $('.email_field_div').css 'opacity', 1
#   $('.address_field_div').toggle()
#   $('.email_field_div').toggle()
#   return
# 
# $(document).on 'submit', '#guest_login_form', (e) ->
#   e.preventDefault()
#   $.ajax
#     type: 'POST'
#     data: $('#guest_login_form').serialize()
#     url: $('#guest_login_form').attr('action')
#     beforeSend: ->
#       $('.progress_img').show()
#       $('.email_field_div').css 'opacity', '0.4'
#       $('.error_message_box').hide()
#     success: (data, status, jqXHR) ->
#       if data.guest_acc_created
#         toggleGuestLoginForm()
#         if window.history and window.history.pushState
#           window.history.pushState null, 'address collect', '#'
#         $('.login_step .stepNumber').addClass 'doneStep'
#       else
#         $('.error_message_box').html(data.reason or 'Incorrect email').show()
#     complete: ->
#       $('.email_field_div').css 'opacity', 1
#       $('.progress_img').hide()
#       return