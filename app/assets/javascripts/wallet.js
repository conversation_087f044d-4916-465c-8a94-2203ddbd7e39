var WALLET = WALLET || {};

WALLET = (function(window, document, Wallet){

  Wallet.methods ={

    enablePlaceOrderButton: function(){
      ACCORDIAN.methods.deactivateAccordion()
      INT_PAYMENT_OPTIONS.methods.enableFormSubmit()
    },

    walletApiCall: function(){
      setWalletDiscount()
    },

    unCheckWallet: function(){
      element = $('#wallet_return');
      if(element.prop('checked')){
        element.prop('checked', false);
        WALLET.methods.walletApiCall()
      }
    },

    useCompleteWallet: function(){
      total = parseFloat($("#grand_total_without_cod").attr('value'))
      if (total <= 0){
        $('#wallet_return').val('fully_used_wallet')
        return true 
      }
      else {
        $('#wallet_return').val('use_return')
        return false
      }
    },

    checkWalletDetails: function(){
      if(WALLET.methods.useCompleteWallet()){
        WALLET.methods.unCheckWallet()
      }
    },
    isValidWallet: function(){
      return $('#wallet_return').prop('checked') && WALLET.methods.useCompleteWallet() 
    },

    updateAccordianDomestic: function(grandtotal){
      $(".accordion-navigation").each(function(index){
        $(this).data('grandtotal', grandtotal)
      })
    },
    
    updateGrandTotal: function(grandtotal){
      $(".accordion-navigation").each(function(index){
        $(this).data('grandtotal', grandtotal)
      })
      $('[name=payment_option]').data('grandtotal', grandtotal)
    },
    
    UncheckAccordian: function(){
      element = $('.accordion-navigation')
      element.removeClass("active")
      element.find('.payment-text-block').attr('aria-expanded', 'false')
      element.find('.content').removeClass("active")
      $('[name="order[pay_type]"').removeAttr("checked");
    },

    UncheckRadioButtons: function(){
      $('.accordion').find("input:checked").prop('checked', false)
      
    },

    togglePrepaidPromotion: function() {
      var grandtotal, prepaid_discount, selected_option, shipping_discount, symbol;
      selected_option = $('[name=payment_option]').filter(":checked");
      if (!(selected_option.length > 0)){
        selected_option = $(".accordion-navigation.active")
      }
      symbol = selected_option.data('symbol');
      grandtotal = selected_option.data('grandtotal');
      prepaid_discount = selected_option.data('prepaidDiscount');
      shipping_discount = selected_option.data('shipping');
      $('.dom-payment-error').hide();
      $('.payment-error').hide();
      if (!$('#cashondelivery').is(":checked") && selected_option.length > 0 ) {
        $('#wallet_discount_order_page').show();
        if (selected_option.data('prepaidShippingPromo') === 'available') {
          $('#shipping_charge').html('Shipping : ' + 'FREE'.fontcolor('green').bold());
          WALLET.methods.computeGrandTotal(symbol, grandtotal, prepaid_discount, 0, shipping_discount);
        } else {
          $('#shipping_charge').html('Shipping : ' + symbol + ' ' + shipping_discount);
          WALLET.methods.computeGrandTotal(symbol, grandtotal, prepaid_discount, 0, 0);
        }
        $('.prepaid_discount').show();
        $('#prepaid_discount').val(gon.prepaid_discount);
        $('.grand_total').show();
        $('.grand_total_with_cod.hide').hide();
        $('.cod_charges').hide();
      }
      else {
        $('.prepaid_discount').hide();
        if ($('.prepaid_discount').is(":hidden")){
          
        }
        selected_option = $('#wallet_payment_option')
        shipping_discount = selected_option.data('shipping');
        symbol = selected_option.data('symbol');
        $('#shipping_charge').html('Shipping : ' + symbol + ' ' + shipping_discount);
      }
      $('#mastercard_discount').val('0');
      $('.mastercard_discount').hide();
      return $('.saved_card_message').hide();
    },

    computeGrandTotal: function(symbol, grandtotal, prepaid_discount, mastercard_discount, shipping_discount){
      total = grandtotal - prepaid_discount - mastercard_discount - shipping_discount
      total_with_symbol = symbol + ' ' + total
      $('.grand_total').html('Grand Total : ' + total_with_symbol)
      $('.grand_total.top').html(total_with_symbol)
    }
    
  } 
  return Wallet; 
})(this, this.document, WALLET);

