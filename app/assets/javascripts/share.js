var MR = MR || {};
MR = (function (window, document, Mirraw) {
  Mirraw.share = {
    initSocialShare: function () {
      const shareButton = $(".share");
      const socialDropdown = $(".social");
   
      const url = encodeURIComponent(window.location.href);
      const text = encodeURIComponent("Check out this amazing product!");
      const designImage = $(".design_image")[0]; 
      const media = designImage ? encodeURIComponent(designImage.src) : "";
      const isMobile = /iPhone|iPad|iPod|Android/i.test(navigator.userAgent);
      if (navigator.share && isMobile) {
        shareButton.on("click", async function (event) {
          event.preventDefault();
          try {
            await navigator.share({
              title: "Product Share",
              text: "Check out this amazing product!",
              url: window.location.href,
            });
          } catch (error) {
        }
        });
        return;
      }
      const shareLinks = {
        ".share-whatsapp": `https://api.whatsapp.com/send?text=${text} ${url}`,
        ".share-facebook":   isMobile
        ? `fb-messenger://share?link=${encodeURIComponent(url)}`
        : () => copyLink(url, "Messenger", "https://www.messenger.com"),
        ".share-twitter": () => copyLink(url, "Twitter", "https://twitter.com/messages/compose"),
        ".share-mail": `mailto:?subject=${text}&body=${text}%20${url}`,
        ".share-pinterest": `https://www.pinterest.com/pin/create/button/?url=${url}&media=${media}&description=${text}`
      };
  
      function copyLink(url, platform, redirectUrl) {
        if (!window.isCopying) { 
          window.isCopying = true; 
          navigator.clipboard.writeText(decodeURIComponent(url)).then(() => {
            alert(`Product link copied! Paste it in a ${platform} DM.`);
            window.open(redirectUrl, "_blank");
            setTimeout(() => { window.isCopying = false; }, 500);
          });
        }
      }
      
      Object.entries(shareLinks).forEach(([selector, link]) => {
        const element = $(selector);
        if (element.length) {
          if (typeof link === "function") {
            element.on("click", function (event) {
              event.preventDefault();
              link();
            });
          } else {
            element.attr("href", link);
          }
        }
      });
      if (shareButton && socialDropdown) {
        $(document).on("click", ".share", function () {
          $(".share").toggleClass("active");
          $(".social").toggleClass("active");
        });
        $(document).on("click", function (event) {
          if (!$(event.target).closest(".share-wrapper").length) {
            socialDropdown.removeClass("active");
            shareButton.removeClass("active");
          }
        });
      }
    },
  };
  return Mirraw;
})(this, this.document, MR);
$(function () {
  MR.share.initSocialShare();
  if (typeof Turbolinks !== "undefined") {
    MR.share.initSocialShare();
  }
  $(document).on("turbolinks:load", function () {
    MR.share.initSocialShare();
  });
});