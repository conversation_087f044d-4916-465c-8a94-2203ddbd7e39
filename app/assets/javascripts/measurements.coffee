$ ->
  $(document).foundation()
  $('.other_number_field').on('keydown keyup',(e) ->
    $(this).val(0) if $(this).val() < 0
    $(this).val(100) if $(this).val() > 100
    if e.which == 13
      e.preventDefault()
    )
  $('.help_link').click ->
    $('body').css('overflow','hidden')

  if !$('#top_form').hasClass('active')
    $('#top_form').addClass('active')

  $(document).on 'close.fndtn.reveal', '.reveal-modal', ->
    $('body').css('overflow','scroll')
    $('.example-orbit').css('height','200px')

  $('.other_button').click ->
    key = $(this).data('key')
    min = $(this).data('min')
    max = $(this).data('max')
    value = $("#other_field_#{key}").val()
    is_other_text_field = (key == 'accuracy_info' || key == 'source_info')
    if (!is_other_text_field && isNaN(value)) || value.trim() == ''
      #show error
    else if (!isNaN(max) && parseFloat(value) > max) || (!isNaN(min) && parseFloat(value) < min)
      $('#mapping_error_'+key).removeClass('hide')
      setTimeout (->
        $('#mapping_error_'+key).addClass('hide')
        return
        ),2000
    else
      $("#measurement_other_#{key}").show()
      $("#measurement_text_field_#{key}").hide()
      $("#other_field_#{key}").val('')
      $("#stitching_measurement_#{key}").val(value)
      if !is_other_text_field
        value = "#{value} Inch "      
      $("#measurement_other_#{key}").html(value)
    updateFittingNotice(key, parseFloat(value))

  $('.measurement_li').click ->
    key = $(this).data('key')
    $("#measurement_list_#{key}").children().removeClass('active_li')
    $(this).addClass('active_li')
    if $(this).data('dependent-mapping') == true
      changeAsPerBustSize($(this).data('value'))

  changeAsPerBustSize=(bust_size) ->  
    mapping = $('#product_mapping').data($('#stitching_measurement_design_id').val() + '-' + bust_size)
    index = 1
    for key in ['length', 'sleeves_length']
      $('.'+key+'_size_li').each ->
        $(this).show()
        if (!isNaN(parseInt($(this).data('value'))) && (parseInt($(this).data('value')) < mapping[index][0] || parseInt($(this).data('value')) > mapping[index][1])) || (isNaN(parseInt($(this).data('value'))) && mapping[index][0] != 0)
          $(this).hide().removeClass('active_li')
      if $('.'+key+'_size_li.active_li').length == 0
        $("#stitching_measurement_#{key}").val('')
      index++

  updateFittingNotice=(key, value)->    
    key_name = titleize(key).replace('Size','').replace('Length','').replace('Chest','Bust')
    key_name = if key_name == '' then 'Blouse' else key_name
    if !isNaN(value)
      if ($('#measurement_type_garments_body_measurement').is(':checked') && $('#measurement_type_garments_garment_measurement').is(':visible') && jQuery.inArray(key, ['chest_size', 'hip_size', 'waist_size']) != -1)
        message = "Final garment that will be made will have #{key_name} length of #{value + 2} to #{value + 3} inches"
      else
        if key == 'chest_size' && !$('#mapping_error_'+key).hasClass('hide')
          message = ''
        else
          message = "Final garment that will be made will have #{key_name} length of #{value} inches"        
      $('#fitting_notice_'+key).html(message)
    else
      $('#fitting_notice_'+key).html('')

  $('.measurement_type_radio').change ->    
    for key in ['chest_size', 'waist_size', 'hip_size']
      updateFittingNotice(key, parseFloat($('#stitching_measurement_'+key).val()))    

  $('.other_field').click ->
    key = $(this).data('key')
    $("#stitching_measurement_#{key}").val('')
    $("#measurement_text_field_#{key}").show()
    $("#measurement_other_#{key}").hide()
    value = $("#measurement_other_#{key}").text()
    if value.toLowerCase() != 'other'
      $("#other_field_#{key}").val(value)

  $('.measurement_options').click ->
    count = $(this).data('count')
    key = $(this).data('key')
    value = $(this).data('value')
    $("#stitching_measurement_#{key}").val(value)
    $("#measurement_text_field_#{key}").hide()
    $("#measurement_other_#{key}").show()
    if $(".data_entry_#{count}").length == 0
      if count.split('_')[1] == 'top'
        if $('#bottom_form').length > 0 
          $('#bottom_form').addClass('active')
        else
          $('#addition_question_form').addClass('active')
      else if count.split('_')[1] == 'bottom'
        $('#addition_question_form').addClass('active')
    updateFittingNotice(key, value)
    goToELement(".data_entry_#{count}")

  goToELement=(element) ->
    if $(element).length > 0 && $(element).is(':visible')
      $('html, body').animate({
        scrollTop: $(element).offset().top - 200
      }, 300);

  goToELement('#update_form_stitching')

  $('.product_type_btn').click ->
    $('.product_type_btn').removeClass('selected_type')
    selected_elem = $(this)
    selected_elem.addClass('selected_type')
    if selected_elem.data('product') == 'anarkali'
      $('#chudidar_bottom').show()
      $('#salwar_bottom').hide()
      $('#as_per_image_bottom').parent().removeClass('small-6').addClass('small-12')
    else
      $('#chudidar_bottom').show()
      $('#salwar_bottom').show()
      $('#as_per_image_bottom').parent().removeClass('small-12').addClass('small-6')
    $('#product_type').val($(this).data('product'))
    $('.product_type_btn_bottom').removeClass('selected_type')
    $('#product_bottom_type').val('')


  $('.product_type_btn_bottom').click ->
    $('.product_type_btn_bottom').removeClass('selected_type')
    selected_elem = $(this)
    selected_elem.addClass('selected_type')
    $('#product_bottom_type').val(selected_elem.data('product'))
    if $('#product_type').val() == 'anarkali'
      if $('#product_bottom_type').val() == 'chudidar'
        $('#stitching_measurement_product_designable_type').val('anarkali')
      else if $('#product_bottom_type').val() == 'as_per_image'
        $('#stitching_measurement_product_designable_type').val('anarkali_with_bottom_as_per_image')
      else
        $('#stitching_measurement_product_designable_type').val('anarkali_pant')
    else
      if $('#product_bottom_type').val() != 'as_per_image'
        $('#stitching_measurement_product_designable_type').val("kameez_#{$('#product_bottom_type').val()}")
      else
        $('#stitching_measurement_product_designable_type').val('kameez_with_bottom_as_per_image')


  $('.img-check').click ->
    value = $(this).data('id')
    is_value_changed = "#{value}" != $('#stitching_measurement_style_no').val()
    if is_value_changed
      $('#stitching_measurement_style_no').val(value)
      $('#style_no_value').html(value)
      $('.img-check').removeClass('selected_style')
      $(this).addClass('selected_style')
      style_no_validations(value)
      $('#stitching_measurement_back_style_no').val('')
      $.ajax(get_valid_back_styles(value, $('#styles_mapping').data("#{value}")[0]))      

  get_valid_back_styles=(data, back_styles) ->
    url: 'stitching_measurement/get_back_styles'
    type: 'GET'
    data:
      front_style_no: data
      back_styles: back_styles
    datatype: 'script'
    beforeSend: ->
      $('#loadingImage').show()
    complete: ->
      $('#loadingImage').hide()
    success: (data,success,jqhxr) ->
      $(document).foundation()
      $('.example-orbit').css('height','200px')

  style_no_validations=(data) ->
    if data == 6 || data == 13
      $('#measurement_list_sleeves_length ,#title_sleeves_length ,#measurement_list_shoulder_size ,#title_shoulder_size').hide()
      $('#stitching_measurement_sleeves_length').val('NA')
      $('#stitching_measurement_shoulder_size').val('NA')
    else
      $('#measurement_list_sleeves_length').show()
      $('#title_sleeves_length ,#measurement_list_shoulder_size ,#title_shoulder_size').show()
      if $('#stitching_measurement_sleeves_length').val() == 'NA'
        $('#stitching_measurement_sleeves_length').val('')
      if $('#stitching_measurement_shoulder_size').val() == 'NA'
        $('#stitching_measurement_shoulder_size').val('')
    if data == 12 || data == 16 || data == 14
      $('#measurement_list_front_neck').hide()
      $('#title_front_neck').hide()
      if data == 14
        $('#stitching_measurement_front_neck').val(0)
      else
        $('#stitching_measurement_front_neck').val(3)
    else
      $('#title_front_neck ,#measurement_list_front_neck').show()
      $('#stitching_measurement_front_neck').val('') if $('#stitching_measurement_front_neck').val()== 'NA'
    apply_hook_validations_as_per_styles(data)
    apply_sleeves_validations_as_per_styles(data)

  apply_hook_validations_as_per_styles=(data)->
    valid_hook_styles = Array.from(new Set($('#styles_mapping').data("#{data}")[1].split(',')))
    $('.hook_size_li').removeClass('active_li').hide()
    $('#stitching_measurement_hook').val('')
    if valid_hook_styles.length == 1 && valid_hook_styles[0] != '' && valid_hook_styles[0].toLowerCase().trim() != 'all'
      found_element = $('#' + valid_hook_styles[0].toLowerCase().trim() + '_link')
      found_element.show().addClass('active_li')
      $('#stitching_measurement_hook').val(found_element.data('value'))
    else if valid_hook_styles[0] != ''
      for hook_name in valid_hook_styles          
        lowered_hook_name = hook_name.toLowerCase().trim()
        if lowered_hook_name == 'all'
          $('.hook_size_li').show()
        else
          $('#' + lowered_hook_name + '_link').show()
    previous_hook_style = $('#previous_measurements').data('hook')
    if $("li[data-value='#{previous_hook_style}']").is(':visible')
      $('#stitching_measurement_hook').val(previous_hook_style)
      $("li[data-value='#{previous_hook_style}']").addClass('active_li')

  apply_sleeves_validations_as_per_styles=(data)->
    valid_sleeves_options = Array.from(new Set($('#styles_mapping').data("#{data}")[2].split(',')))
    lowered_sleeve_value = valid_sleeves_options[0].toLowerCase().trim()
    $('.sleeves_length_size_li').hide().removeClass('active_li')
    $('#stitching_measurement_sleeves_length').val('')
    if valid_sleeves_options.length > 1
      $('.sleeves_length_size_li').show()
    else if valid_sleeves_options.length == 1 && lowered_sleeve_value != ''
      if lowered_sleeve_value != 'all'
        $('.sleeves_length_size_li').hide().removeClass('active_li')        
        if lowered_sleeve_value == 'sleeveless'
          $("li[data-value='Sleeveless']").show().addClass('active_li')
          $('#stitching_measurement_sleeves_length').val('Sleeveless')
        else
          $("#measurement_list_sleeves_length li[data-value!='Sleeveless']").show()
      else
        $('.sleeves_length_size_li').show()
    previous_sleeve_length = $('#previous_measurements').attr('data-sleeves-length')
    if $("#measurement_list_sleeves_length li[data-value='#{previous_sleeve_length}']").is(':visible')
      $('#stitching_measurement_sleeves_length').val(previous_sleeve_length)
      $("#measurement_list_sleeves_length li[data-value='#{previous_sleeve_length}']").addClass('active_li')

  if $('#stitching_measurement_style_no').val() != "" && $('#stitching_measurement_style_no').val() != undefined
    style_no_validations(parseInt($('#stitching_measurement_style_no').val()))
  
  if $('#previous_measurements').data('back-style-no') == 15
    $('.hook_size_li').removeClass('active_li').hide()
    $('#stitching_measurement_hook').val('String At The Back')
    $('#string_link').show().addClass('active_li')

  validate_form=()->
    error_values = []
    for key in $('#top_keys').data('keys')
      if $("##{key}").val() == ""
        error_values.push(key)
    for key in $('#bottom_keys').data('keys')
      if $("##{key}").val() == ""
        error_values.push(key)
    for key in $('#additional_keys').data('keys')
      if $("##{key}").val() == ""
        error_values.push(key)
    return error_values

  $('#validation_button').click ->
    if $('#stitching_measurement_chest_size').val() != "" && $('#stitching_measurement_chest_size').val() != undefined && $('#stitching_measurement_product_mapping_present').val() == 'true' && jQuery.inArray($('#stitching_measurement_product_designable_type').val(), ['lehenga_choli', 'blouse']) != -1
      changeAsPerBustSize($('#stitching_measurement_chest_size').val())    
    error_values = validate_form()
    normalized_keys = []
    $('.field_label').removeClass('unfilled_measurements')
    for error in error_values
      error_key = error.replace('stitching_measurement_','')
      $("#title_#{error_key}").addClass('unfilled_measurements')
      normalized_keys.push(titleize(error_key))
      $('#top_form ,#bottom_form').addClass('active')
    if error_values.length > 0
      goToELement("#measurement_list_#{error_values[0].replace('stitching_measurement_','')}")
      new PNotify({
          title: 'Oh No!',
          text: "Some Fields Are Empty:\n#{titleize(error_values.join('\n').replace(/stitching_measurement_/g,' '))}",
          type: 'error',
          delay: 4000
      })
    else
      new PNotify({
          title: 'Review Measurements',
          text: "Please Review Your Measurements Before Submitting",
          type: 'success'
      })
      $('.hide_on_review').hide()
      create_table()

  create_table=()->
    str = '<table class="columns small-12">'
    top_name = $('#form_names').data('top')
    bottom_name =  $('#form_names').data('bottom')
    str += "<tr><td colspan=2 class='table_head'> #{top_name} Measurements </td></tr>"
    for key in $('#top_keys').data('keys')
      value =$("##{key}").val()
      if key == 'stitching_measurement_style_no'
        str += "<tr><td>#{titleize(key.replace('stitching_measurement_',''))}</td><td><div><img src='/assets/stitching/blousefront/#{value}.jpg'></div></td></tr>"
      else if key == 'stitching_measurement_back_style_no'
        str += "<tr><td>#{titleize(key.replace('stitching_measurement_',''))}</td><td><div><img src='/assets/stitching/blouseback/#{value}.jpg'></div></td></tr>"
      else if key == 'product_type'|| key == 'stitching_measurement_hook' || key == 'stitching_measurement_padded'
        str +="<tr><td>#{titleize(key.replace('stitching_measurement_',''))}</td><td>"+titleize($("##{key}").val())+"</td></tr>"
      else
        str += "<tr><td>#{titleize(key.replace('stitching_measurement_',''))}</td><td>"+titleize($("##{key}").val())+" Inch</td></tr>"
    if $('#bottom_keys').data('keys').length > 0
      str += "<tr><td colspan=2 class='table_head'> #{bottom_name} Measurements</td></tr>"
      for key in $('#bottom_keys').data('keys')
        if key == 'product_bottom_type'
          str +="<tr><td>#{titleize(key.replace('stitching_measurement_',''))}</td><td>"+titleize($("##{key}").val())+"</td></tr>"
        else
          str +="<tr><td>#{titleize(key.replace('stitching_measurement_',''))}</td><td>"+titleize($("##{key}").val())+" Inch</td></tr>"
    str += "</table>"
    $('#review_table ,.show_on_review').show()
    $('#review_table').empty()
    $('#review_table').html(str)
    if (window.history && window.history.pushState)
      window.history.pushState(null, 'measurement collect', '')
    goToELement('#final_submit')

  $(window).on('popstate',(e) ->
    $('.show_on_review').hide()
    $('.hide_on_review').show()
  )

  $('#main_stitching_form').on('submit',(e) ->
    e.preventDefault()
    $('#final_submit').prop('disabled',true)
    $('#final_submit').val('Wait..')
    if measurement_name_uniq()
      similar_products = []
      $('.item_checkbox:checked').each ->
        similar_products.push($(this).val())
      form_data = $(this).add('input[type=hidden][name=measurement_experience]').serialize() + "&similar_products=#{similar_products}"
      $.ajax(paramSubmitForm(form_data))
    else
      $('#final_submit').prop('disabled',false)
      $('#final_submit').val('Submit')
    )

  measurement_name_uniq=()->
    value = $('#stitching_measurement_measurement_name').val()
    if value == '' 
      alert('Name Cannot be Blank')
      return false
    if value == 'Create New'
      alert('Name Cannot be "Create New"')
      return false
    for x in [0...($("#measurement_select>option").length)]
      if $("#measurement_select>option")[x].innerHTML == value
        $('#stitching_measurement_measurement_name').addClass('border-red')
        $('#error_measurement_name').show()
        return false
    return true


  paramSubmitForm=(form_data)->
    url: 'stitching_measurement/create'
    type: 'POST'
    data:
      form_data
    datatype: 'json'
    beforeSend: ->
      $('#loadingImage').show()
    complete: ->
      $('#loadingImage').hide()
    success: (data,success,jqhxr) ->
      new PNotify({
          title: 'Success!',
          text: "Measurements Have Been Saved Successfully",
          type: 'success'
      })
      $('.show_on_review').hide()
      $('#review_table').show()
      order_number = $('#order_number').val()
      item_id = $('#item_id').val()
      user_id = $('#user_id').val()
      source = $('#source').val()
      app_source = $('#app_source').val()
      # ga('send', 'event', 'StitchingForm', 'Submit', "order_#{order_number}/item_#{item_id}/user_#{user_id}/source_#{source}/app_source_#{app_source}");
      setTimeout (->
        location.reload(true)
        return
      ), 600
    error: ->
      new PNotify({
          title: 'Error!',
          text: "Some Error Occured",
          type: 'error'
      })
      $('#final_submit').prop('disabled',false)
      $('#final_submit').val('Submit')


  $('.example-orbit').css('height','200px')

  $('.repeat_radio_btn').click ->
    if $(this).val() == 'Yes'
      $('.similar_products').show()
    else
      $('.item_checkbox:checked').removeAttr('checked')
      $('.similar-img-check').removeClass('selected_style')
      $('.similar_products').hide()

  $('#go_back_button').click ->
    $('.hide_on_review').show()
    $('.show_on_review').hide()

  if $('#selected_categories').length > 0
    top = $('#selected_categories').data('top')
    bottom =  $('#selected_categories').data('bottom')
    $("##{top}").click()
    $("##{bottom}_bottom").click()

  if $('#stitching_measurement_height').val() != ''
    height = $('#stitching_measurement_height').val()
    weight = $('#stitching_measurement_weight').val()
    inche = height.split(".")[1] || '0'
    feets = parseInt(height)
    $('#inches').val(inche)
    $('#feet').val(feets)
    $('#height').val("#{feets}.#{inche}")
    $('#weight').val(weight)
    $('#age').val($('#stitching_measurement_age').val())
    $('#addition_question_form').addClass('active')

  titleize = (sentence) ->
    if sentence == undefined
      return ''
    if !sentence.split
      return sentence

    _titleizeWord = (string) ->
      string.charAt(0).toUpperCase() + string.slice(1).toLowerCase()

    result = []
    sentence.split('_').forEach (w) ->
      result.push _titleizeWord(w)
      return
    result.join ' '