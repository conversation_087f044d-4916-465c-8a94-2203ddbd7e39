window.SocialShareButton =
  openUrl : (url, width = 640, height = 480) ->
    left = (screen.width / 2) - (width / 2)
    top = (screen.height * 0.3) - (height / 2)
    opt = "width=#{width},height=#{height},left=#{left},top=#{top},menubar=no,status=no,location=no"
    window.open(url, opt)
    false

  share : (el) ->
    site = $(el).data('site')
    appkey = $(el).data('appkey') || ''
    $parent = $(el).parent()
    title = encodeURIComponent($(el).data(site + '-title') || $parent.data('title') || '')
    img = encodeURIComponent($parent.data("img") || '')
    url = encodeURIComponent($parent.data("url") || '')
    via = encodeURIComponent($parent.data("via") || '')
    desc = encodeURIComponent($parent.data("desc") || ' ')

    if url.length == 0
      url = encodeURIComponent(location.href)
    switch site
      when "email"
        location.href = "mailto:?to=&subject=#{title}&body=#{url}"
      when "twitter"
        via_str = ''
        via_str = "&via=#{via}" if via.length > 0
        SocialShareButton.openUrl("https://twitter.com/intent/tweet?url=#{url}&text=#{title}#{via_str}", 650, 300)
      when "facebook"
        SocialShareButton.openUrl("https://www.facebook.com/sharer.php?u=#{url}", 555, 400)
      when "pinterest"
        SocialShareButton.openUrl("https://www.pinterest.com/pin/create/button/?url=#{url}&media=#{img}&description=#{title}")
      when "linkedin"
        SocialShareButton.openUrl("https://www.linkedin.com/shareArticle?mini=true&url=#{url}&title=#{title}&summary=#{desc}")
    false