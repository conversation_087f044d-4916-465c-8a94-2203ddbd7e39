if Object::toString.call(window.operamini) is "[object OperaMini]"
  $(document).ready ->
    # This method handle specification and related tabs functionality
    $("li.tabs > .tab-title").click ->
      active_content_selector = $(".tabs-content > .content.active").attr("id")
      $("#" + active_content_selector).removeClass "active"
      activate_content_selector = $($(this)[0].children[0]).attr("href")
      $(activate_content_selector).addClass "active"
      $("li.tabs > .tab-title.active").removeClass "active"
      $(this).addClass "active"

    # Opera stitching fix
    $('.addon_types').on "change", ->
      addon_type_value_id = $(this).attr('value')
      $(this).find("option").each ->
        current_val = $(this).val()
        if current_val != addon_type_value_id
          $('#atv_' + current_val).hide()  
        $('#atv_' + current_val).show()

    # Opera checkout button fix
    if (/cart/i.test(window.location.href)) && $(".item_block").length > 0
      order_parent = $('.cart_checkout')
      href = $('.add_place_order').attr('href')
      order_parent.hide()
      add_cont = $('.add_cont_shop')
      add_cont.css
        'background': 'none'
        'margin-top': '-7px'
        'font-weight': 'bold'
      add_cont.before('<a class="opera_checkout_position_fix button small success" href="'+href+'">CHECKOUT</a>')  

    # Opera sort filter
    if $(".sort_filter").length > 0
     $(".sort_filter").removeClass "fixed"
     $(".sort_filter").addClass  "opera_footer_fix"
