MR = (function (window, document, Mirraw) {
  Mirraw.order = {
    validateDocument: function () {
      var allowedExtensions = ['pdf', 'jpg', 'jpeg', 'png'];
      var allowedMimes = ['application/pdf', 'image/jpeg', 'image/png'];
      var maxSize = 5 * 1024 * 1024;

      var fileInput = $('#file_input');
      var errorMsg = $('#file-error');
      var uploadTitle = $('.upload-title');
      var uploadSub = $('.upload-sub');
      var form = fileInput.closest('form');
      var clearBtn = $('<span class="file-clear">✖</span>').hide();
      $('.upload-box').append(clearBtn);

      function clearSelection(keepError) {
        fileInput.val('');
        uploadTitle.text('Upload File');
        uploadSub.text('Supports JPG, PNG, PDF (Max 5MB)');
        if (!keepError) errorMsg.text('').hide();
        clearBtn.hide();
      }

      function showError(msg) {
        errorMsg.text(msg).show();
      }

      fileInput.on('change', function () {
        var file = this.files[0];
        if (file) {
          uploadTitle.text(file.name);
          errorMsg.hide();
          clearBtn.show();
        }
      });

      form.on('submit', function (e) {
        var file = fileInput[0].files[0];
        if (!file) {
          e.preventDefault();
          showError('Please select a file to upload.');
          return false;
        }

        var ext = file.name.split('.').pop().toLowerCase();
        if ($.inArray(ext, allowedExtensions) === -1 || $.inArray(file.type, allowedMimes) === -1) {
          e.preventDefault();
          showError('Only PDF, JPG & PNG files are allowed.');
          clearSelection(true);
          return false;
        }

        if (file.size > maxSize) {
          e.preventDefault();
          showError('File size must be less than 5 MB.');
          clearSelection(true);
          return false;
        }
        $(this).find('.submit-btn').prop('disabled', true);
        errorMsg.hide();
      });

      clearBtn.on('click', function (e) {
        e.preventDefault();
        clearSelection();
      });
    }
  };

  return Mirraw;
})(this, this.document, MR || {});
