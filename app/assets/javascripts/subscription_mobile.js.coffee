$ ->
  checkUpScrollPercent = (scrollStart) ->
    interval = setInterval((->
      upScrollPercent = getScrollPercent(scrollStart)
      if upScrollPercent > parseFloat(10) / 100
        clearInterval interval
        setTimeout show_subscription_banner, 1000
        return
    ), 1000)

  getScrollPercent = (scrollStart) ->
    scrollAmount = $(window).scrollTop() - scrollStart
    if scrollAmount < 0
      scrollAmount = 0
      scrollStart = $(window).scrollTop()
    return parseFloat(scrollAmount) / parseFloat($(document).height())

  checkExitIntent = do ->
    scrollStart = $(window).scrollTop()
    pageHeight = $(document).height()
    () ->
      if pageHeight > 0
        interval = setInterval((->
          downScrollPercent = getScrollPercent(scrollStart)
          if downScrollPercent > parseFloat(60) / 100 && $('.pages_home').length > 0
            clearInterval interval
            checkUpScrollPercent(scrollStart)
            return
        ), 1000)

  checkExitIntent()

  subscribe_email = (user_email) ->
    url = '/subscriptions'
    data = subscriptions:
      source_url: window.location.href
      email: user_email
      appsource: 'mobile'
    $.post url, data, ((response) ->
      if response.error
        $('.sub-msg').text response.error_message
      else
        if response.data.coupon_code
          $('.sub-msg').text response.data.message + response.data.coupon_code
          $('#mobile-subscribe-window').foundation 'reveal', 'close'
          # if $('#branch-banner-iframe').length > 0
          #   $('body').css 'margin-top', '0px'
          #   $('#branch-banner-iframe').remove()
          $('.coupon_code').html response.data.coupon_code
          $('.offer_msg').html response.data.message
          $('#notice_banner').slideDown 500, ->
            setTimeout (->
              $('#notice_banner').slideUp 500, ->
                $('#notice_banner').remove()
                return
              return
            ), 5000
            return
        else
          $('.sub-msg').text response.data
        $('.subscribe_text_message').hide()
        if !response.already_subscribed.length
          setTimeout (->
            $('#mobile-subscribe-window').foundation 'reveal', 'close'
            return
          ), 5000
          $('.sub-msg').addClass 'subscribe_success'
          $('#mobile-subscribe-window input').hide()
          # ga('send', 'event', 'Subscription', 'subscription_successfull', {
            # 'nonInteraction': 1,
            # 'metric1': 1
          # });
          # ga 'send', 'event', 'UX', 'click', 'metric1': 1
        else
          $('.sub-msg').text 'We noticed.!!!!You have already subscribed!'
          setTimeout (->
            $('#mobile-subscribe-window').foundation 'reveal', 'close'
            return
          ), 5000
      return
    ), 'json'
    return

  show_subscription_banner = ->
    if !getCookie('subscribe').length and !/cart*|order*|account*/i.test(window.location.href)
      $('#mobile-subscribe-window').foundation 'reveal', 'open'
      if !sub_view
        sub_view = true
        #send page view only once.
        # ga('send', 'event', 'Subscription', 'subscription_form_view', {
            # 'nonInteraction': 1,
            # 'metric2': 1
          # });
        # ga 'send', 'pageview', 'metric2': 1
    return

  $(document).on 'click', '#email-subscribe-button', (e) ->
    e.preventDefault()
    if typeof $('#subscribe-input').val() != 'undefined'
      user_email = $('#subscribe-input').val()
      regex = /^([a-zA-Z0-9_.+-])+\@(([a-zA-Z0-9-])+\.)+([a-zA-Z0-9]{2,4})+$/
      if user_email.trim() and !user_email.endsWith('.co') and regex.test(user_email)
        subscribe_email user_email
      else
        $('.sub-msg').text 'Please enter a valid Email address.'

  $(document).on 'click', '#subscribe-input', (e) ->
    $('#modal-subscribe-box').animate { scrollTop: $(this).offset().top }, 'slow'

  setTimeout show_subscription_banner, 9000
  sub_view = false
  $(document).on 'click', '#notice_close_btn', ->
    $('#notice_banner').slideUp 500, ->
      $('#notice_banner').remove()

  $(document).on 'click', '.close_subscribe, #email-cancel-button', ->
    $('#mobile-subscribe-window').foundation 'reveal', 'close'
    setCookie 'subscribe', 'closed', 7
    # ga('send', 'event', 'Subscription', 'subscription_window_close', {
      # 'nonInteraction': 1,
      # 'metric3': 1
      # });
    return