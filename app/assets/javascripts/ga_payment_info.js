var GA_PAYMENT_INFO = GA_PAYMENT_INFO || {};

GA_PAYMENT_INFO = (function(window, document, GA_PAYMENT_INFO){
    GA_PAYMENT_INFO.events ={
        init: function(payment_type){
            var value = (gon.totalvalue + gon.addons_charges - gon.coupon_amount + gon.gift_wrap).toFixed(2)
            var customizationTotal = 0
            ga4_payment_params = gon.ga_hash_new
            ga4_payment_params.value = parseFloat(value) 
            ga4_payment_params.payment_type = payment_type
            ga4_payment_params.offers_discount = gon.b1g1_offers
            ga4_payment_params.customization = gon.addons_charges
            ga4_payment_params.coupon_discount = gon.coupon_amount
            ga4_payment_params.gift_wrap = gon.gift_wrap
            ga4_payment_params.items = [];
            for (var i = 0; i < ga4_checkout_params.items.length; i++) {
                var item = ga4_checkout_params.items[i];
                var newItem = Object.assign({}, item);
                ga4_payment_params.items.push(newItem);
            }
            ga4_payment_params.tax = getTax()
            ga4_payment_params.shipping = getShipping()
            dataLayer.push({ ecommerce: null });
            dataLayer.push({
                event: "ga4_add_payment_info",
                ecommerce: ga4_payment_params
            });
        }
    }
    return GA_PAYMENT_INFO; 
  })(this, this.document,GA_PAYMENT_INFO);