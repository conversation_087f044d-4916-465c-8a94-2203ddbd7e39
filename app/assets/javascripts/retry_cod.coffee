# Place all the behaviors and hooks related to the matching controller here.
# All this logic will automatically be available in application.js.
# You can use CoffeeScript in this file: http://coffeescript.org/

# $ ->
	# ga('send', 'event', {
	#   eventCategory: 'Checkout',
	#   eventAction: 'prepaid-popup-view',
	#   nonInteraction: true
	# })

$ ->
	document.getElementById('retry').addEventListener 'submit', (evt) ->
	  $('input[type=\'submit\']').attr 'disabled', true
	  $('#retry_button').val 'Please wait...'
	#   ga('send', 'event', {
	    # eventCategory: 'Checkout',
	    # eventAction: 'prepaid-popup-retry',
	    # nonInteraction: true
	#   })
	  true

	document.getElementById('cod').addEventListener 'submit', (evt) ->
	  $('#cod_button').val 'Please wait...'
	  $('input[type=\'submit\']').attr 'disabled', true
	#   ga('send', 'event', {
	    # eventCategory: 'Checkout',
	    # eventAction: 'prepaid-popup-cod',
	    # nonInteraction: true
	#   })
	  true