.selectize {
  list-style-type: none;
  margin: 0;

  .selectize-option {
    font-size: 1.2em;

    &:first-child label {
      border-radius: 0.5em 0.5em 0 0;
    }

    &:last-child label, &:last-child input[type=text] {
      border-radius: 0 0 0.5em 0.5em;
    }

    input[type=radio] {
      display: none;

      &:checked + label {
        background: #670e19;
        color: white;
      }
    }

    label, input[type=text] {
      width: 100%;
      padding: 1em 1.2em;
      background-color: #f4f4f4;
    }

    label {
      cursor: pointer;
      margin: 0;
    }

    input[type=text] {
      border: none;
    }

    .selectize-other-option-toggle {
      &.is-hidden {
        display: none;
      }
    }
  }
}