//importing variables
@import 'variables';
@import 'white_theme';

/*Home_page specific css */

body {
    font-family: "Inter", sans-serif !important;
  overflow: scroll !important;
  background: $body_white;
  color: $text_black;
  cursor: auto;
  // font-family: helvetica, arial;
  font-style: normal;
  font-weight: normal;
  line-height: 1.5;
  margin: 0;
  padding: 0;
  position: relative;

  #see-offers-box{
    .columns{
      padding: 0px;
    }
    a#offer-button{
      display: block;
      background-color: transparent;
      border: $border_black;
      padding: 10px 5px;
      color: $text_black;
      font-size: 1em;
      line-height: 27px;
      font-weight: 600;
      margin: 0.3rem 0.2rem 0.6rem 0.2rem;
      &:after{
        content: '»';
        margin-left: 12px;
        color: $text_black;
        font-size: 28px;
        vertical-align: inherit;
      }
    }
  }
  .heading_underline {
    text-decoration: underline;
  }
  .line_through_text{
    text-decoration: line-through;
  }
  .footer-info {
    /*border:1px dotted grey;*/
    padding:8px;
    background-color: #fff9fa;
    margin-bottom:20px;
    margin-top:20px;
    box-sizing:border-box;
    #seo_post {
      .seo-list-anchor {
        font-size: 14px;
        padding: 5px;
        line-height: 17px;
      }
      .seo-list-table {
        width:70%;
        border: 1px solid black;
      }
      .seo-list-line-height {
        line-height:30px;
      }
      .seo-list-font {
        font-size:14px;
      }
      h2 {
        font-size: 1.5em;
        color: $text_black;
      }
      p {
        font-size: 1em;
        color: $text_black;
        text-align: justify;
      }
      &.read-more{
        height:7.8em;
        overflow:hidden;
      }
    }
    #popular_search{
      width: 100%;
      height: 170px;
      color: $text_black;
      overflow-y: scroll;
      overflow-x: hidden;
      font-size: 12px !important;
      font-weight: bold;
      position: relative;
      scroll-behavior: smooth;
      table{
        width: 100% !important;
        height: auto !important;
        table-layout: fixed;
      }
      tr{
        td{
          font-size: 10px !important;
          font-weight: bold;
          padding: 5px 5px;
          text-align: center;
        }
      }
      h2{
        font-size: 18px !important;
        font-weight: bold;
      }
    }
    ::-webkit-scrollbar {
      -webkit-appearance: none;
      width: 3px;
    }
    ::-webkit-scrollbar-thumb {
      border-radius: 5px;
      background-color: rgba(0,0,0,.5);
      -webkit-box-shadow: 0 0 1px rgba(255,255,255,.5);
    }
    #popular_search_title{
      h2{
        font-size: 18px !important;
        font-weight: bold;
      }
    }
  }
  .truncate {
    width: 100%;
    color: $text_black;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    a{
      color: $text_black;
    }
  }
  .no-bullet.shipping_desc {
    li {
      color: $text_black;
    }
  }
  .close-icon{
    background-image: image-url('close-32.png');
    background-repeat: no-repeat;
    display: inline-block;
    width: 1em;
    height: 1em;
    vertical-align: middle;
    background-size: 1em;
    background-color: black;
    border-radius: 5em;
  }
  .bordered_block {
    padding: 1em;
    margin-bottom: 1em;
    /*box-shadow: $card_box_shadow;*/
    border: 0.1em solid $gray;
  }

  .opera_footer_fix {position: relative;bottom: 0;width: 100%;z-index: 9999;margin-top: 12px;}
  .opera_footer_fix a {margin-bottom: 0em;width: 100%;left: 0px;font-weight: bold;line-height: inherit;}
  .opera_checkout_position_fix{width: 100%;margin-top: 18px;background-color: #0E9A7D !important;}
  
  .small_msg{
    font-size: 12px;
  }

  .wrap_total{
    white-space: nowrap;
  }

  .home_page_designs {
    margin: 0.1em;
    >li {
      img{
        width:100%;
      }
      .fr_page {
        box-shadow: $card_box_shadow;
        .panel {
          height: 6.6em;
          position: relative;
          padding:0.6em;
          background: $snow_white;
          border: 1px solid $snow_white;

          &.design_desc {
            margin-bottom: 0em;
            a {
              color: $text_black;
              font-size: 14px;
            }
          }
        }
      }
    }
    li {
      &.original_price {
        font-size: 0.8em;
        color: $text_black;
      }
      &.discount_price {
        font-weight: bold;
        color: $text_black;
      }
      &.percent_off {
        color: red;
        margin-top: -1.7em;
        font-size: 0.9em;
      }
    }
    .design_desc {
      padding: 0.5em;
      margin-bottom: 0rem;
      li {
        padding-bottom: 0em;
      }
    }  

    #design_details {
      width:100%;
    }

    .details_block {
      color: $text_black;
    }

    .rating_div{
      position: absolute;
      width: 40px !important;
      height: 40px;
      margin-top:-25px;
      .small_rating{
        font-size: 12px;
        background-color: #16be48;
        color: $text_white;
        padding: 5px;
        border-radius: 5%;
        font-weight: bold;
      }
      .green-rating{
        background-color: #16be48;
      }
      .red-rating{
        background-color: #FF5722;
      }
      .orange-rating{
        background-color: #FFA000;
      }
    }

    .design-col1 {
    float:left;
    width:100%;
    font-size: 15px !important;
    /*font-weight: bold;*/
    text-align: left;
    }

    .design-col2 {
      float:right;
      border-radius: 5%;
      font-size: 12px;
      background-color: #e0356f;
      padding: 6px 6px;
      line-height: 12px;
      text-align: center;
      position: relative;
      margin-top: -24px;
      color: $text_white;
    }

    .add-to-cart-bst{
      padding: 10px 2px;
      width: 100%;
      color: $text_white;
      background: $green_btn;
      font-weight: 700;
      box-shadow: $green_btn_box;
    }

    .discount_new_wrap {
      text-align:center;
      word-wrap: break-word;
      padding-left: 0.5px;
    }

    .add_new_pos {
      position: relative !important;
      width: 100% !important;
    }

    .discount_font {
      font-size: 0.8em;
    }

    .design_price {
      font-weight: bold;
    }
  }

  .home_page {
    margin: 0.1em;
    >li {
      .fr_page {
        box-shadow: $card_box_shadow;
        .panel {
          margin-bottom:0;
          position: relative;
          padding:0.6em;
          background: $snow_white;
          color: $text_black;
          border: 1px solid $snow_white;
        }
      }
    }
  }
}

/* Search_bar css */

#search_desktop{
  display: none;
}

@media only screen and (min-width: 64.063em) { 
  .search_margin{
    display: none;
  }
  #search_desktop{
    display: block;
  }
}

@import 'unbxd_recommendations';