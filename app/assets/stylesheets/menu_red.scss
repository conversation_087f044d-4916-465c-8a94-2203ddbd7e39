html {
  -webkit-tap-highlight-color: transparent;
}

::selection {
  background-color: $dark_red;
  color: $text_white;
}

section.main-section {
  display: block;
}

body {
  font-family: "Inter", sans-serif !important;
}

sup.menu-tag {
  color: #ffffff;
  padding: 3px 4px;
  font-size: 10px;
  background: #bf445e;
  background-size: 100% 100%;
  border-radius: 2px;
}

#menu-side-nav {
  height: 100%;
  width: 0px;
  position: fixed;
  z-index: 9999;
  top: 0;
  left: 0;
  background-color: #fff9fa;
  overflow-x: hidden;
  overflow-y: scroll;
  display: block;
  color: #3C4345;
  max-width: 300px;

  .menu_text_box {
    img {
      width: 100%;
      max-width: 120px;
      padding: 0 15px;
      margin: 10px 0;
    }
  }

  .close-menu {
    float: right;
    width: 30px;
    height: 30px;
    position: absolute;
    right: 0;
    top: 10px;

    &:before,
    &:after {
      position: absolute;
      content: ' ';
      height: 20px;
      width: 2px;
      background-color: #3C4345;
      right: 20px;
      top: 5px;
    }

    &:before {
      transform: rotate(45deg);
    }

    &:after {
      transform: rotate(-45deg);
    }
  }

  .menu-accordion {
    margin-left: 0px;
    display: none;
  }

  .has-submenu {
    padding: 0.6rem 1rem;
    border-bottom: 1px solid #f1f1f1;
    background-color: #fff9fa;
  }

  a {
    color: #3C4345;
    letter-spacing: 1px;
    font-size: 16px;

    &:hover {
      background: $dark_red !important;
      color: white !important;
    }
  }

  .menu-content {
    padding: 0px;
  }

  .all-menu {
    display: block;
    padding: 0.6rem 1rem;
    font-size: 16px;
    font-weight: bold;
  }

  .menus_static {
    font-size: 15px;
    padding: 10px 0;
    list-style: none;

    li {
      border-bottom: 1px solid #f1f1f1;
      padding: 4px 0px;
      width: 85%;
      font-size: $menu_font_size;
    }
  }

  .submenu {
    font-size: 16px;
    margin-left: 5px;
    position: absolute;
    right: 14px;
    font-weight: bold;
    color: #670b19;

    &:hover {
      background: $dark_red !important;
      color: white !important;
    }
  }
}

.move-right>.inner-wrap {
  -webkit-transform: unset !important;
  -moz-transform: unset !important;
  -ms-transform: unset !important;
  -ms-transform: unset !important;
  -o-transform: unset !important;
  transform: unset !important;
}

.off-canvas-wrap.move-right {
  position: relative;

  &:after {
    position: absolute;
    content: "";
    width: 100%;
    height: 100%;
    top: 0;
    background: #000;
    z-index: 99;
    opacity: 0.7;
  }
}

#branch-banner-iframe {
  top: 84px !important;
  z-index: 1 !important;
}
@media screen and (max-width:991px){
  .accordion-navigation.active > a, dd.active > a{
    background:  $dark_red;
    color: #fff;
  }

  #create_orders_block ul.addons-notes .accordion-navigation.active>a,
  dd.active>a,
  #carts_block ul.addons-notes.accordion .accordion-navigation.active>a,
  dd.active>a {
    background: #F9F9F9 !important;
    color: #000 !important;
  }

  .small-12.columns.title-column.offer-message-frame {
    display: flex;
    flex-wrap: wrap;
    width: 100%;
    align-items: center;

    span {
      margin-bottom: 5px;
    }
  }
}

.fi-arrow-down:before {
  content: "\f109";
}

.fi-arrow-up:before {
  content: "\f10c";
}

.fi-filter:before {
  content: "\f14b";
}

.fi-list:before {
  content: "\f169";
}

.fi-magnifying-glass:before {
  content: "\f16c";
  transform: scaleX(-1);
}

.fi-thumbnails:before {
  content: "\f1fa";
}

.fi-download:before {
  content: "\f143";
}

.fi-heart:before {
  content: "\f159";
}
