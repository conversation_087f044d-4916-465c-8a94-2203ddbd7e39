body{
  ul, li{
    padding: 0px;
    margin: 0px;
  }
  a{
    text-decoration: none;
  }
  .text-center{
    text-align: center;
  }
  .truncate{
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: initial;
  }
  amp-sidebar{
    background-color: #670b19;
    width: 60%;
    button#menu-close{
      display: block;
      margin: 0 auto;
      background: transparent;
      border: none;
      color: #dad9d9;
      font-size: 18px;
      padding: 8px;
      float: right;
    }
    amp-accordion.menu-list{
      section{
        .nested-accordion{
          margin-left: 1.15rem;
          border-left: 1px dashed #b11f2d;
        }
        h4{
          padding: 0.5rem 1rem;
          border: none;
          background-color: transparent;
          color: #dad9d9;
          outline: none;
          font-weight: normal;
        }
        h4.last{
          border-bottom: 1px solid gray;
          margin: 0 0.7rem 0 0.7rem;
          padding: 0.5rem 0;
        }
        a{
          padding: 0.5rem 1rem 0.5rem 0.1rem;
          color: #dad9d9;
          font-size: 15px;
        }
      }
    }
  }
  .amp-btn{
    font-weight: inherit;
    font-size: 1rem;
    line-height: 1.125rem;
    padding: .7em .8em;
    text-decoration: none;
    word-wrap: normal;
    vertical-align: middle;
    cursor: pointer;
    background-color: transparent;
  }
  .header{
    box-shadow: 0 -1px 15px 0 rgba(0,0,0,0.2);
    #menu-bar{
      color: white;
      background-color: #670b19;
      font-size: 24px;
      border: none;
      border-right: 1px solid gray;
      padding: 2px 5px;
      margin: 8px;
    }
    #logo-box{
      amp-img{
        display: inline-block;
        width: 96px;
        top: 8px;
      }
    }
  }
  .home-page-block{
    background: #eee;
    .banner-container{
      margin: 0.4rem 0.2rem;
    }
    .static-banner{
      margin: 5px 0;
      padding: 5px 0;
      background: white;
    }
    .front-block{
      position: relative;
      width: 49%;
      display: inline-block;
    }
    #offer-button{
      display: block;
      border: 2px solid #303030;
      padding: 10px 5px;
      color: #303030;
      line-height: 27px;
      font-weight: 600;
      margin: 0.3rem 0.4rem 0.6rem 0.4rem;
    }
    #design-home-block{
      margin: 0.2rem;
      .design-desc{
        .name {
          color: #303030;
          font-size: 0.750rem;;
          font-weight: bold;
        }
      }
    }
  }
  .store-page-block{
    .store-breadcrumb {
      display: flex;
      padding: 0.2rem 0.5rem 0 0.5rem;
      @media only screen
      and (min-width: 500px)
      and (max-width: 1040px) { 
        margin-top: 15px;
      }
      ul{
        margin-bottom: 0px;
        margin-left: 0px;
      }
      li:not(:first-child):before {
        content: '/';
        margin-left: 0px;
        margin-right: 0px;
      }
      a {
        font-size: 12px;
        color: #303030;
      }
      li {
        font-size: 12px;
        display:inline-block;
      }
      .final{
        font-weight: bold;
      }
    }
    #heading-product-title{
      padding: 0rem 0.5rem 0 0.5rem;
      .product-count{
        font-size: 0.8rem;
      }
      h1{
        font-size: 1.2rem;
        font-weight: 500;
        margin: 6px 0px;
      }
    }
    #category-links{
      overflow-x: auto;
      overflow-y: hidden;
      white-space: nowrap;
      padding: 0.3rem 0.5rem;
      max-width: 100%;
      line-height: 2;
      a.button{
        background-color: white;
        border: 1px solid #303030;
        color: #303030;
        font-size: 0.725rem;
        padding: 0.7em;
        margin-bottom: 0.3rem;
      }
    }
    #action-buttons{
      padding: 0.2rem 0.5rem 0 0.5rem;
      span{
        display: inline-block;
        color: #333;
        border: 1px solid #333;
        width: 40%;
        text-align: center;
        font-weight: 600;
      }
    }
    .navigate-page{
      margin: 1rem;
      .prev, .next{
        color: #333;
        border: 1px solid #333;
        padding: .2rem .8rem;
      }
    }
  }
  .store-page-design{
    margin-top: 0.875em;
    .design-block{
      display: inline-block;
      padding: 0 0.15rem 0.35rem;
      .offer-msg{
        position: absolute;
        font-weight: 500;
        font-size: 0.75em;
        padding: 0.15em;
        background-color: #b11f2d;
        color: #ffffff;
        z-index: 1;
      }
      .fr-page{
        border: 0.75px solid #d8d8d8;
        .discount_new_block{
          position: relative;
          background: #b01f2b;
          z-index: 1;
          color: white;
          font-size: 0.75em;
          padding: 0.15em;
          float: right;
          top: 40px;
          margin-top: -40px;
        }
        .design-desc {
          position: relative;
          padding: 0em 0.3em 0.3em 0.3em;
          margin-bottom: 0em;
          .name {
            color: #303030;
            font-size: 0.750rem;;
            font-weight: bold;
          }
          .design-details{
            display: flex;
            line-height: 2;
            .price{
              color: #8f1b1d;
              font-weight: bold;
              text-align: left;
              margin: 0;
              font-size: 0.875rem;
            }
            .actual-price{
              margin: 1px 0px 0px 5px;
              text-decoration: line-through;
              color: #a2a1a1;
              text-align: left;
              font-size: 0.850rem;
            }
          }
        }
      }
    }
  }
  ul.store-page-design > div{
    display: inline;
  }
  #mobile-footer{
    margin: 0.2rem;
    border-top: 1px solid #c3c3c3;
    padding-top: 1rem;
    font-size: 0.8rem;
    span.info{
      width: 32%;
      display: inline-block;
      box-shadow: 0 1px 1px 0 rgba(51, 51, 51, 0.51);
      padding: 0.4rem 0;
      font-weight: 600;
    }
    div.info{
      padding: 0.4rem 0;
    }
  }
}

.footer-info {
  padding: 15px;
  margin: 8px;
  margin-bottom: 20px;
  margin-top: 20px;
  #seo_post {
    color: #303030;
    line-height: 1.6;
    .seo-list-anchor {
      font-size: 14px;
      padding: 5px;
      line-height: 17px;
    }
    .seo-list-table {
      width:70%;
      border: 1px solid black;
    }
    .seo-list-line-height {
      line-height:30px;
    }
    .seo-list-font {
      font-size:14px;
    }
    h1, h2, h3, h4, h5, h6{
      color: #303030;
      font-weight: bold;
      margin-bottom: 0.5rem;
      margin-top: 0.2rem;
    }
    h1{
      font-size: 1.1875rem;
    }
    h2{
      font-size: 1.125rem;
    }
    h3 {
      font-size: 1rem;
    }
    h4{
      font-size: 0.9375rem;
    }
    h5{
      font-size: 0.8125rem;
    }
    h6{
      font-size: 0.6875rem;
    }
    p {
      font-size: 14px;
      color: #303030;
      text-align: justify;
      margin-bottom: 0.9rem;
      a{
        font-weight: bold;
      }
      padding: 0px;
    }
    ul, ol{
      font-size: 14px;
      color: #303030;
      text-align: justify;
      margin-bottom: 0.6rem;
      margin-left: 1.1rem;
      padding: 0px;
    }
    &.read-more{
      height:7.8em;
      overflow:hidden;
    }
  }
  #popular_search{
    width: 100%;
    height: 170px;
    color: #303030;
    overflow-y: scroll;
    overflow-x: hidden;
    font-size: 12px;
    font-weight: bold;
    position: relative;
    scroll-behavior: smooth;
    table{
      width: 100%;
      height: 100%;
      table-layout: fixed;
      border-collapse: separate;
      border-spacing: 0em 0.7em;
    }
    tr{
      &:nth-child(even) {
        background-color: #decace;
      }
      td{
        font-size: 10px;
        font-weight: bold;
        padding: 5px 5px;
        text-align: center;
      }
    }
    a{
      color: #670b19;
    }
    h2{
      font-size: 18px;
      font-weight: bold;
    }
  }
  .seo-text-box{
    ul,ol{
      padding-left: 15px;
    }
    h2{
      font-size: 19px;
    }
    h3{
      font-size: 18px;
    }
    p,li{
      line-height: 1.3;
    }
  }
  #price_box{
    width: 100%;
    height: 100%;
    color: #303030;
    overflow-y: scroll;
    overflow-x: hidden;
    font-size: 12px;
    font-weight: bold;
    position: relative;
    scroll-behavior: smooth;
    table{
      width: 100%;
      height: 100%;
      table-layout: fixed;
      border-collapse: separate;
      border-spacing: 0em 0.7em;
    }
    tr{
      &:nth-child(even) {
        background-color: #decace;
      }
      td{
        font-size: 10px;
        font-weight: bold;
        padding: 5px 5px;
        text-align: center;
      }
    }
    a{
      color: #670b19;
      font-weight: normal;
      font-size: 13px;
    }
    h2{
      font-size: 18px;
      font-weight: bold;
    }
    th:first-child{
      width: 50px;
    }
    th:last-child{
      width: 85px;
    }
  }
  ::-webkit-scrollbar {
    -webkit-appearance: none;
    width: 3px;
  }
  ::-webkit-scrollbar-thumb {
    border-radius: 5px;
    background-color: rgba(0,0,0,.5);
    -webkit-box-shadow: 0 0 1px rgba(255,255,255,.5);
  }
  #popular_search_title{
    h2{
      font-size: 18px;
      font-weight: bold;
    }
  }
  #FAQs {
    margin-top: 1em;
    border: 0px;
    color: #303030;
    font-size: 14px;
    line-height: 25px;
    border-bottom: 1px solid #eee;
    h3{
      font-size: 16px;
    }
    ol, ul{
      margin: 5px 0px 5px 0px;
      padding-left: 20px;
    }
    a{
      display: inline;
      color: #670b19;
    }
    section {
      margin-bottom: 8px;
      h4 {
        border: 0px;
        font-size: 15px;
        padding-left: 5px;
      }
      .content {
        font-size: 14px;
        padding-left: 5px;
      }
    }
  }
}

@media only screen and (max-width: 480px){
  .home-page-block{
    #frontpage{
      .frontpage-caption{
        width: 48%;
      }
    }
  }
  li.design-block{
    display: inline-block;
    width: 48%;
  }
}

@media only screen and (min-width: 480px) and (max-width: 1024px){
  .home-page-block{
    #frontpage{
      .frontpage-caption{
        width: 24%;
      }
    }
  }
  li.design-block{
    display: inline-block;
    width: 32%;  
  }
}

@media only screen and (min-width: 1024px){
  .home-page-block{
    #frontpage{
      .frontpage-caption{
        width: 24%;
      }
    }
  }
  li.design-block{
    display: inline-block;
    width: 24%;
  }
}

.amp-product-slider {
  margin: 8px;
  .slider-design-block {
    padding: 4px;
  }
}
.about{
  .txt_color {
    color: #303030;
  }
  .img_top{
    margin-top: 10px;
    margin-left: auto;
    margin-right: auto;
    text-align: center;
  }
  .txt_slogan {
    font-size: 16px;
    margin-top: 5px;
    color: #303030;
  }
  .link_color{color: #670b19;font-weight: bold;}
  .txt_content{font-size: 16px;margin: 0 0 20px;line-height: 24px;}
}
details {
  position: relative;
  margin-top: 0.2rem;
  margin-bottom: 0.5rem;
  border-bottom: 0.1px solid #dddddd;
  > :last-child {
    margin-bottom: 1rem;
  }
  &::before {
    width: 100%;
    height: 100%;
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    border-radius: inherit;
    opacity: .15;
    pointer-events: none;
    transition: opacity .2s;
    z-index: -1;
  }
  &[open] {
    background-color: #FFF;
    &::before {
      opacity: .6;
    }
  }
  *:focus {
    outline: 0;
  }
}
summary {
  padding: 1rem 2em 1rem 0;
  display: block;
  position: relative;
  font-size: 1.33em;
  font-weight: bold;
  cursor: pointer;
  &::before,
  &::after {
    width: .75em;
    height: 2px;
    position: absolute;
    top: 50%;
    right: 0;
    content: '';
    background-color: currentColor;
    text-align: right;
    transform: translateY(-50%);
    transition: transform .2s ease-in-out;
  }
  &::after {
    transform: translateY(-50%) rotate(90deg);   
    [open] & {
      transform: translateY(-50%) rotate(180deg);
    }
  }
  &::-webkit-details-marker {
    display: none;
  }
  *:focus {
    outline: 0;
  }
}