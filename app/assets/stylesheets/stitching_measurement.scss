// Place all the styles related to the StitchingMeasurement controller here.
// They will automatically be included in application.css.
// You can use Sass (SCSS) here: http://sass-lang.com/
body {
  font-family: "Inter", sans-serif !important;
}
#main-section{
  overflow-x: hidden !important;
}
.accordion{
  margin-left: 0px;
  .content{
    color: black;
  }
}

.ui-pnotify-title{
  color: black;
}
.ui-pnotify-text{
  font-size: 0.7em;
}
button.round, .button.round {
    border-radius: 10px;
}
.img-check, .back-img-check{
  cursor:pointer;
}
.label{
  display: inline-block;
  font-family: "Helvetica Neue", Helvetica, Roboto, Arial, sans-serif;
  font-weight: normal;
  line-height: 1;
  margin-bottom: auto;
  position: relative;
  text-align: center;
  text-decoration: none;
  text-align: left;
  white-space: nowrap;
  padding: 0.25rem 0.5rem 0.25rem;
  font-size: 0.6875rem;
  background-color: #ffffff;
  color: #000000;
}

input[type="text"]:focus, input[type="text"]:hover, input[type="password"]:focus, input[type="password"]:hover, input[type="email"]:focus, input[type="email"]:hover, input[type="number"]:focus, input[type="number"]:hover, input[type="tel"]:focus, input[type="tel"]:hover, input[type="time"]:focus, input[type="time"]:hover, textarea:focus, textarea:hover, select:focus, select:hover{
  box-shadow: 0 1px 0 0 #5d8da5;
  border-bottom: 1px solid #5d8da5;
  background-color: white;
  color: black;
}
input[type="text"], input[type="password"], input[type="email"], input[type="number"], input[type="tel"], input[type="time"], textarea, select{
  margin: 0 0 0.5em 0;
  border: 0;
  border-bottom: 1px solid #9e9e9e;
  background-color: white;
  color: black;
  box-shadow: none;
  outline: none;
  &::-webkit-input-placeholder { /* Chrome/Opera/Safari */
    color: grey;
  }
  &::-moz-placeholder { /* Firefox 19+ */
    color: grey;
  }
  &:-ms-input-placeholder { /* IE 10+ */
    color: grey;
  }
  &:-moz-placeholder { /* Firefox 18- */
    color: grey;
  }
}
.off-canvas-wrap #container{
  width: 96vw;
  overflow-y: hidden;
  padding: 1em 0em;
  margin: 2%;
  margin-top: 2em;
  overflow-x: hidden;
}

.conversion{
  color: #c13e7f;
}

.mapping_error{
  color: red; 
}

.measurement_type{
  font-size: 0.8em;
}
.disable_accordion{
  color: white;
  display: block;
  font-family: "Helvetica Neue", Helvetica, Roboto, Arial, sans-serif;
  font-size: 1rem;
  padding: 1rem;
  background-color: black;
}
.measurement_list{
  list-style: none;
}
.measurement_li{
  color: black;
  background-color: #d8d8d8;
  padding: 6px;
  margin-bottom: 4px;
  border-radius: 0.1em;
  text-align: center;
}
.other_button{
  webkit-appearance: none;
  -moz-appearance: none;
  border-radius: 0;
  border-style: solid;
  border-width: 0;
  cursor: pointer;
  font-family: "Helvetica Neue", Helvetica, Roboto, Arial, sans-serif;
  font-weight: normal;
  line-height: normal;
  margin: 0 0 1.25rem;
  position: relative;
  text-align: center;
  text-decoration: none;
  display: inline-block;
  padding: 0.6em !important;
  font-size: 1rem;
  background-color: #008CBA;
  border-color: #007095;
  color: #FFFFFF;
  margin-left: -6.5em !important;
  transition: background-color 300ms ease-out;
}

.login_hr:after {
  content: "OR"; /* section sign */
  color: #999;
  display: inline; /* for vertical centering and background knockout */
  background-color: white; /* same as background color */
  padding: 0 0.5em; /* size of background color knockout */
}
.login_hr {
  font-family: Arial, sans-serif; /* choose the font you like */
  text-align: center; /* horizontal centering */
  line-height: 1px; /* vertical centering */
  height: 1px; /* gap between the lines */
  font-size: 1em; /* choose font size you like */
  border-width: 1px 0; /* top and bottom borders */
  border-style: solid;
  border-color: #676767;
  margin: 20px 10px; /* 20px space above/below, 10px left/right */

  /* ensure 1px gap between borders */
  -webkit-box-sizing: content-box;
  -moz-box-sizing: content-box;
  -ms-box-sizing: content-box;
  -o-box-sizing: content-box;
  box-sizing: content-box;
}

.active_li{
  background-color: #36a070;
}
.info{
  color: #298bb3;
}

#loadingImage{
  position: fixed;
  left: 45%;
  top: 30%;
}

.btn-primary{
  border: antiquewhite;
  border-style: groove;
  }
.accordion .accordion-navigation > a:hover, .accordion dd > a:hover {
    background: black;
    color: white;
}
.accordion .accordion-navigation > a, .accordion dd > a {
  background: black;
  color: white;
  display: block;
  font-family: "Helvetica Neue", Helvetica, Roboto, Arial, sans-serif;
  font-size: 1rem;
  padding: 1rem;

}

.accordion .accordion-navigation.active > a, .accordion dd.active > a{
  background-color: black;
}
.product_type_btn{
  height: 12em;
  color: black !important;
  background-color: white;
  &:hover{
    background-color: #8c8c8c;
  }
}

.product_type_btn_bottom{
  height: 12em;
  color: black !important;
  background-color: white;
  &:hover{
    background-color: #8c8c8c;
  }
}

.selected_type{
  background-color: #8c8c8c;
}

@media only screen and (max-width: 40em) {
  .orbit-container{
    .orbit-next, .orbit-prev {
      display: block !important; 
    }
  }
}

.unfilled_measurements{
  color: red;
}
.table_head{
  background-color: black;
  color: white;
  padding: 15px;
}
.selected_style{
  border-color: #389a43;
  border-style: double;
  border-width: 5px;
}

.touch .orbit-container .orbit-prev, .touch .orbit-container .orbit-next{
  background-color: rgba(97, 0, 0, 0.22) !important
}

@media only screen and (orientation: landscape) {
  .reveal-modal{  
    max-height: 70%;
    overflow-y: scroll;
  }
}

.view-measurements{
  .view-btn{
    margin-top: 10px;
  }
}
// .mearsurement-page {
//   padding: 35px 0 100px;
// }