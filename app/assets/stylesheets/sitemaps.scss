@import 'variables';
$royal_blue: #3585dc;
body {
  font-family: "Inter", sans-serif !important;
}
.sitemaps_index, .sitemaps_show_category {
  #container {
    margin-top: 3.8em;
    padding: 0px;
  }
}

.sitemap_block {
  padding: 0px 10px 0px 10px;
  .accordion {
    border: 1px solid $gray;
  }

  .sitemap_item_title {
    a { color: $royal_blue; }
    color: $gray;
  }

  .sitemap_item {
    border-bottom: 1px solid $gray;
    margin-left: 0px !important;
    .sitemap_header_title {
      color: $gray;
      font-size: 17px;
    }

    .accordion-navigation {
      .content {
        font-size: 14px !important;
        :after {
          content: '';
        }
      }
      a { line-height: 0.4; }
      a:after {
        margin-left: 10px;
        content: '  »';
      }
    }
  }

  .sitemap_category_header {
    font-size:20px;
  }

  .breadcrumb_stuff {
    font-size: 15px;
    color: $gray;
    li { display:inline-block; }
    li:not(:last-child):after {
      content: '>';
      margin-left:5px;
      margin-right:5px;
    }
  }
}

#back-top {
  display: none;
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 99;
  a {
    i:before {
      background-color: $royal_blue;
      width: 35px;
      height: 35px;
      line-height: 35px;
      border-radius: 50%;
      color: white;
      text-align: center;
    }
  }
}