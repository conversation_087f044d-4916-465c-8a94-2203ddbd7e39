@import 'variables_red';
@import 'red_theme';
body {
    font-family: "Inter", sans-serif !important;
}
.board-item{
    background: white;
    padding: 5px 0px;
    margin: 5px 0px;
    .front_home_page{
      display: inline-flex;
    }
    .banner-timer{
      .countdown{
        position: absolute;
        top: -60px;
        text-align: center;
        left: 0;
        right: 0;
        font-weight: 500;
        letter-spacing: 2px;
        font-size: 22px;
        .deal_timer{
          margin: 0px 8px;
          padding: 0px 4px;
          height: 30px;
          display: inline-block;
        }
        .deal_text{
          display: none;
        }
        .clock{
          width: 100% !important;
        }
      }
    }
    #homepage_cart{
      .homepage-cart-title{
        font-weight: bold;
        margin-left: 5px;
        line-height: 30px;
        margin: 10px;
        border-bottom: 1px solid #eee;
      }
      .blaze-slider{
        .quantity, .item-price-font{
          font-weight: bold;
        }
        .item_block{
          .image-box{
            padding-left: 0.9375em;
            padding-right: 0.9375em;
          }
        }
      }
      .view-cart{
        text-align: center;
        background: $dark_red;
        color: white;
        padding: 10px;
        margin: 0px 10px;
      }
    }
    .homepage_design_blocks, .homepage-blocks{
      .design-col2 {
        font-size: 12px !important;
        padding: 0;
      }
      .design-details, .block-headers{
        display: flex;
        font-weight: bold;
        margin: 0 10px;
        width: auto;
        .view-more-designs{
          font-size: 14px;
          text-align: right;
        }
      }
      .design-slider, .flash_deals{
        .design-slides{
          img{
            margin: auto;
            width: 100%;
          }
        }
      }
      .flash_deals, .design-slider{
        overflow-x: scroll;
        overflow-y: hidden;
        white-space: nowrap;
        scroll-behavior: smooth;
        .design-slider-wrapper{
          display: inline-block;
          width: 47%;
          overflow: hidden;
          text-align: center;
          border: 1px solid #eee;
        .design-col2{
            white-space: initial;
          }
          .design_desc {
            padding: 0.5em;
            margin-bottom: 0rem;
            .truncate {
              color: #670b19;
            }
            .wishlist-forms {
              .wishlist-heart-button {
                font-size: 1.5rem;
                margin: 0;
                padding: 0;
                float: right;
                background: transparent;
                color: $red_background;
                position: absolute;
                right: 0;
                &:focus {
                  outline: none;
                }
              }
              .wishlist-heart-button.empty-heart {
                color: gray;
              }
            }
            .details_block {
              float: left;
              .actual_price{
                display: inline-block;
                color: #595959;
                text-decoration: line-through;
                margin-left: 3px;
                font-weight: 600 !important;
              }
            }
          }
        }
      }
    }
  }

@media only screen and (min-width: 768px){
  .flash_deals, .design-slider{
    .design-slider-wrapper{
      width: 20%;
    }
  }
}


.widget-title{
  .design_blocks{
    .row.widget-title-recs{
      max-width: 100%;
      width: 100%;
      font-size: 1em;
      font-weight: 600;
      padding-top: 1em;
      margin-left: 0.58rem;
      }
    .blaze-slider{
      .blaze-container{
        .blaze-track{
          display: flex;
        }
        .blaze-slide{
          .product-container{
            padding: 0;
            box-shadow: 0 3px 9px 0 rgba(0,0,0,0.1);
            overflow: hidden;
            margin: 10px 11px 5px 0px;
            width: 100%;
            height: auto;
            align-self: center;
            }
            a img {
              width: 100%;
            }
            .p-detail-box{
              color: #636466;
              display: block;
              font-size: 0.875rem;
              overflow: hidden;
              font-weight: 400;
              font-style: normal;
              line-height: 2em;
              height: 2em;
              display: -webkit-box;
              -webkit-box-orient: vertical;
              -webkit-line-clamp: 1;
              overflow: hidden;
              text-wrap: wrap;
              text-transform: capitalize;
            }
            .price-flex.d-flex.price-container{
              display: flex;
              font-weight: 400;
              line-height: 1.25rem;
              letter-spacing: -0.02rem;
              text-align: left;
              flex-wrap: wrap;
              justify-content: flex-start;
              font-family: 'Inter' sans-serif;
              height: 40px;
              }
              .discount_price.title_wrap{
                color: #303030;
                font-size: 0.8rem;
                font-weight: 600;
                font-family: 'Inter', sans-serif;
                @media only screen and (max-width: 767px){
                  font-size: 0.7rem;
                }
              }
              .actual_price.discount-price{
                color: #303030;
                text-decoration: line-through;
                padding-left: 2%;
                font-size: 0.8rem;
                padding: 0 0.5rem;
                font-family: 'Inter', sans-serif;
                @media only screen and (max-width: 767px){
                  padding: 0 0.2rem;
                  font-size: 0.7rem;
                }
              }
              .design-col2.details_block.percent_discount{
                font-family: 'Inter' sans-serif !important;
                display: inline-flex;
                width: 100%;
                font-weight: 600;
                line-height: 1.25rem;
                letter-spacing: -0.02rem;
                text-align: left;
                @media only screen and (max-width: 767px){
                  font-size: 0.7rem;
                }
              }
              }
            }       
        }
      }
    }
    .unbxd_row{
      width: 95%;
      margin: auto;
    }