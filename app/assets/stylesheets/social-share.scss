$size: 30px;

.social-share-button {
  .ssb-icon {
    background-position: center center;
    background-repeat: no-repeat;
    background-size: $size $size;
    display: inline-block;
    height: $size;
    width: $size;
  }

  @each $site in twitter, facebook, google_plus,linkedin, pinterest, email {
    .ssb-#{$site} {
      background-image: image-url('social-share-button/#{$site}.svg');
    }
  }
}