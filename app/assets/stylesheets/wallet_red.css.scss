body {
  background-color: #fff;
  font-family: "Inter", sans-serif !important;
}
.wallet-container{
  font-size: 0.875rem;
  .panel{
   padding: 10px 0;
    background: #fff;
    border: none;
    h5 {
      padding: 10px;
      font-size: 18px;
      color: #03a685;
      text-align: center;
      margin: 0;
      font-weight: 500;
  }
  .panel_content {
    font-size: 18px;
    text-align: center;
    margin: 0;
}
    .row{
      text-align: center;
      .wallet_tnc{
        text-align: left;
      }
    }
  }
  .panel_text{
    background-color: #f8f2f2;
    color: #000;
    padding: 0.5rem;
    h4 {
      font-size: 16px;
      text-align: center;
      color: #670b19;
  }
  }
  .panel_title {
    background-color: #fff;
    margin: 0 0 30px 0 !important;
    color: #3c4345;
    font-weight: 500;
    font-size: 2.5rem;
    border-bottom: 1px solid #f1f1f1;
  }
  .panel-box {
    display: flex;
    justify-content: space-between;
    width: 100%;
    margin: 0 0 30px 0;
    flex-wrap: wrap;
    .panel-container {
      width: 30%;
      border: 1px solid #f1f1f1;
      background: #fff;
  }  
}
}
.wallet-container {
  .panel_text.term_heading h4 {
    text-align: left;
  }
  .terms_and_conditions {
    background: #fff;
    padding: 1rem 2rem;
    border: 1px solid #f1f1f1;
    h5 {
      font-size: 20px;
      text-align: left;
      color: #000;
  }
   .disc_style {
    padding-left: 2rem;
    li {
      font-size: 16px;
      padding: 5px 0 0;
  }
}
  }
}

#wallet_payment_option{
  #wallet_return {
    width: 15px;
    height: 15px;
    float:left;
  }
}
.wallet_message{
  float:left;
  margin:-2px 15px;
  width: 100%;
  .message{
    margin: -3px 7%;
  }
}
.order_wallet{
  position: relative;
  top: 9px;
}
@media only screen and (min-width: 768px){
  #container{
    margin-top: 2.5rem !important;
  }
}
@media only screen and (max-width: 767px){
  .wallet-container {
   .panel_title {
      font-size: 2rem;
  }
    .panel-box {
      .panel-container {
        width: 100%;
    } 
    }
    .terms_and_conditions {
      .disc_style {
        padding-left: 0;
        margin: 0;
        li {
          font-size: 16px;
          padding: 5px 0 0;
      }
    }
    }
  }
}