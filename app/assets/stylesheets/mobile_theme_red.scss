@import 'variables_red';
@import 'red_theme';

.turbolinks-progress-bar {
  background-color: #ccc5c5;
  z-index: 99999;
}

[class*="block-grid-"]{
  li{
    padding: 0 0.15rem 0.35rem;
  }
}
body {
    font-family: "Inter", sans-serif !important;
  overflow: scroll;
  a{
    color: $dark_red;
  }
  h1,h2,h3,h4,h5,h6{
    color: $text_black;
    font-family: "Inter", sans-serif !important;
  }
  .progress_img{
    display: none;
    position: fixed;
    z-index: 99;
    width: 100%;
    top: auto;
    bottom: 50%;
    .image_container{
      text-align: center;
    }
  }
  .order_flow_submit_button{
    color: white !important;
    background: $text_red !important;
    border: 0;
    font-weight: bold;
  }
  #action_buttons {
    text-align: center;
    .add_checkout {
      background: $text_red;
      /*background: linear-gradient(to bottom,#0E9A7D,#267363);*/
      font-size: 1rem;
      font-weight: 700;
    }
    .improved_link{
      color: $link_blue;
      font-size: 1em;
    }
    .padding-0{
      padding: 0;
    }
    .action_button_btn {
      width: 100%;
      .add_to_buy_bow {
        background-color: $dark_green;
        width: 96%;
      }
    }
    .add_cont_shop {
      background: none;
      color: $dark_red;
      font-weight: bold;
      font-size: $cart_font;
      padding: 0.75em;
    }
  }
  .empty-cart-action-buttons{
    margin-bottom: 1em;
    .add_cont_shop {
      line-height: 4;
      &:nth-child(2){
        border-left: 1px solid;
      }
    }
  }
  #container{
    .paymentSteps{
      padding: 10px 5px 8px 5px;
      position: relative;
      margin-bottom: 10px;
      .login_step, .shipping_step, .payment_step{
        width: 50%;
        float: left;
        text-align: center;
        font-size: $font-size;
        position: relative;
      }
      .checkoutLine{
        position: absolute;
        height: 3px;
        background-color: grey;
        top: 35%;
        z-index: 0;
        left: 25%;
        right: 25%;
      }
      .stepNumber{
        width: 40px;
        height: 40px;
        display: inline-block;
        border-radius: 50%;
        -moz-border-radius: 50%;
        -webkit-border-radius: 50%;
        text-align: center;
      }
      .payment_Steps_Title{
        margin-top: 8px;
      }
      .stepImage{
        margin: 8px;
      }
      .paymentImage{
        margin: 7px;
      }
      .stepImage:hover, .paymentImage:hover{
        background: none !important;
      }
      .inactiveStep{
        background-color: darkgrey;
      }
      .activeStep{
        background-color: grey;
      }
      .doneStep{
        background-color: $dark_red;
      }
      .linearCheckLine{
        background: #text_black; /* For browsers that do not support gradients */    
        background: -webkit-linear-gradient(90deg, #30924A 50%, #text_black 50%);
        background: -o-linear-gradient(90deg, #30924A 50%, #text_black 50%);
        background: -moz-linear-gradient(90deg, #30924A 50%, #text_black 50%);
        background: linear-gradient(90deg, #30924A 50%, #text_black 50%);
      }
      .checkComplete{
        background-color: $dark_red;
      }
    }
  }
  .heading_underline {
    text-decoration: underline;
  }
  .fixed_cart_buttons{
    position: fixed;
    bottom: 0;
    z-index: 10;
    right: 0;
    left: 0;
    top: auto;
    box-shadow: 0 -1px 15px 0 rgba(0,0,0,.2);
    a{
      margin: 0 !important;
    }
  }
  #action_buttons, #secondary_action_buttons{
    &.fixed {
      bottom: 0;
      top: auto;
      .add_place_order {
        background-color: $dark_green;
      }
      .add_to_cart {
        background-color: black;
        color: $dark_green;
      }
      margin-bottom: 0;
      a, input[type='submit'] {
        margin-bottom: 0em;
        width: 100%;
        font-weight: bold;
        line-height: inherit;
      }
      li {
        padding-bottom: 0em;
        padding: 0em;
      }
    }
  }
  #secondary_action_buttons{
    margin: 3.7em 0;
  }
  .panel_block {
    margin-top: 1em;
    .panel_content {
      // background-color: $snow_white;
      /*border: 1px solid black;*/
      /*border-radius: 6px;*/
    }

    .panel_heading {
      padding: 0em .2em;
      ul {
        margin: .2em auto;
      }
    }
  }
  .line_through_text{
    text-decoration: line-through;
  }
  .truncate {
    width: 100%;
    color: $text_black;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .no-bullet.shipping_desc {
    li {
      color: white;
    }
  }
  .close-icon{
    background-image: image-url('close-32.png');
    background-repeat: no-repeat;
    display: inline-block;
    width: 1em;
    height: 1em;
    vertical-align: middle;
    background-size: 1em;
    background-color: black;
    border-radius: 5em;
  }
  .bordered_block {
    padding: 1em;
    margin-bottom: 1em;
    background-color: #eee;
  }

  .opera_footer_fix {position: relative;bottom: 0;width: 100%;z-index: 9999;margin-top: 12px;}
  .opera_footer_fix a {margin-bottom: 0em;width: 100%;left: 0px;font-weight: bold;line-height: inherit;}
  .opera_checkout_position_fix{width: 100%;margin-top: 18px;background-color: #0E9A7D !important;}
  
  .small_msg{
    font-size: 12px;
  }

  .cart_checkout a{
    width: 100%;
    margin-top: 18px;
    background-color: #0E9A7D !important;
    box-shadow: $green_btn_box;
    border-radius: 2px;
    font-size: 1rem !important;
  }

  .wrap_total{
    white-space: nowrap;
  }
  a.b1g1{
    color: $text_red !important;
    font-size: 14px;
    font-weight: bold;
  }

  #pushengage_confirm{
    left: 0px !important;
    width: auto !important;
    #pushengage_close_btn{
      color: #333232;
    }
  }
  .b1g1_text{
    border-radius: 50%;
    background-color: $text_red;
    padding: 0px 6px;
    font-size: 14px;
    font-style: italic;
    font-weight: 700;
    font-family: "Inter", sans-serif !important;
    cursor: pointer;
    color: #fff;
  }
  .b1g1_info{
    border-radius: 2px;
    background-color: #d44f67;
    padding: 0px 6px;
    margin: 0 5px;
    width: auto;
    font-size: 14px;
    font-style: italic;
    font-weight: 700;
    font-family: "Inter", sans-serif !important;
    cursor: pointer;
    a{
      color: #fff;
    }
  }
  .b1g1_colored {
    background-color: #d44f67;
    color: #fff;
    float:left;
    padding: 2px;
    margin-bottom: 5px;
  }
  .discounts-text {
    border: 1px solid #c73567 !important;
    color: $text_black !important;
  }
  .discount-percent {
    padding: 3px;
  }
  .sold-out-btn{
    width: 100% !important;
    font-weight: 600;
    display: block;
    text-transform: uppercase;
    padding: 0.6rem 2rem 0.6rem 2rem;
  }
}

:-webkit-autofill,
:-webkit-autofill:hover,
:-webkit-autofill:focus,
:-webkit-autofill:active, {
  transition: background-color 0s ease-in-out 5000s;
}

input[type="text"], input[type="password"], input[type="email"], input[type="number"], input[type="tel"], input[type="time"],textarea, select{
  margin: 0 0 0.5em 0;
  border: 0;
  border-bottom: 1px solid #9e9e9e;
  color: black;
  box-shadow: none;
  outline: none;
  &:focus, &:hover{
    box-shadow: 0 1px 0 0 $input_focus_color;
    border-bottom: 1px solid $input_focus_color;
  }
  &::-webkit-input-placeholder { /* Chrome/Opera/Safari */
    color: $placeholder_color;
  }
  &::-moz-placeholder { /* Firefox 19+ */
    color: $placeholder_color;
  }
  &:-ms-input-placeholder { /* IE 10+ */
    color: $placeholder_color;
  }
  &:-moz-placeholder { /* Firefox 18- */
    color: $placeholder_color;
  }
}

img[data-original] {
  opacity: 1;
  transition: opacity 0.5s;
}

img.js-lazy {
  opacity: 0;
}

.sticky-button{
  margin-bottom: 0;
  height:45px;
  position: fixed;
  bottom: 0;
  z-index: 10;
  right: 0;
  left: 0;
  top: auto;
  box-shadow: 0 -1px 15px 0 rgba(0,0,0,.2);
  animation: smoothScroll 0.3s forwards;
  -webkit-transition: -webkit-transform 0.3s ease-out;
  transition: -webkit-transform 0.3s ease-out;
  transition: transform 0.3s ease-out;
  transition: transform 0.3s ease-out,
  -webkit-transform 0.3s ease-out;
  -webkit-transform: translateY(63px);
  transform: translateY(63px);
  background: $white;
  background-image: linear-gradient(transparent, white);
}
@keyframes smoothScroll {
  0% {
    transform: translateY(20px);
  }
  100% {
    transform: translateY(0px);
  }
}
.off-canvas-wrap .submit_btn:focus, .off-canvas-wrap .submit_btn:hover {
  background-color: transparent;
  border-color: transparent;
}
// change password new css
body{
  .change_password_page {
    padding: 40px 0;
    h1.change_password {
      font-size: 24px;
      font-weight: 600;
      color: #3C4345;
      margin-bottom: 30px;
      font-family: "Inter", sans-serif !important;
  }
  .change_password_input {
    background-color: #fff9fa;
    border-radius: 20px;
    @media screen and (min-width: 991px){
      width: 600px;
    }
    max-width: 700px;
    margin: auto;
    padding: 50px 0;
    border: 1px solid #f1f1f1;
}
input[type="text"], 
input[type="password"], 
input[type="email"],
 input[type="number"], 
 input[type="tel"], 
 input[type="time"], 
 textarea, select {
  margin: 0 0 1.5em 0;
  border: 0;
  border: 1px solid #ccc;
  color: #000;
  box-shadow: none;
  outline: none;
  padding: 20px;
  border-radius: 6px;
}
.reset-password {
  border: none;
  color: #fff;
  padding: 10px 50px;
  font-size: 16px;
  border-radius: 6px !important;
  font-weight: 600;
  margin-top: 20px;
}
  }
}
