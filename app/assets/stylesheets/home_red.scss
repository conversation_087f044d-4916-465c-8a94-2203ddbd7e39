
//importing variables
@import 'variables_red';
@import 'red_theme';
/*Home_page specific css */

body {
  overflow-x: hidden !important;
  overflow: scroll !important;
  background: $body_white;
  color: $text_black;
  font-family: "Inter", sans-serif !important;
  cursor: auto;
  font-style: normal;
  font-weight: normal;
  line-height: 1.5;
  margin: 0;
  padding: 0;
  position: relative;
  overflow-x: hidden !important;
  // dynamic home page css
  .pages_home {
    #container{
      div, a{
        &:focus{
            outline: none;
        }
      }
      .js-lazy{
        display: inline-block;
      }
      .user_scrollable_banner{
        scroll-behavior: smooth;
        .banner-slider-box{
          overflow-x: scroll;
          overflow-y: hidden;
          white-space: nowrap;
          scroll-behavior: smooth;
          .banner-wrapper{
            display: inline-block;
            width: 310px;
            overflow: hidden;
            text-align: center;
            .user_scrollable_banner_slide{
              margin: 0px 2px;
              display: flex;
              img{
                border: $border_black;
              }
            }
          }
        }
      }
      .tag_slider{
        .tag-slider{
          overflow-x: scroll;
          overflow-y: hidden;
          white-space: nowrap;
          scroll-behavior: smooth;
          .tag-slide{
            margin: 0px 15px;
            display: inline-block;
            text-align: center;
            .tag-slider-title{
              font-size: 12px;
              font-weight: bold;
              text-align: center;
            }
          }
        }
      }
      .board-item{
        background: white;
        padding: 5px 0px;
        margin: 5px 0px;
        .front_home_page{
          display: inline-flex;
        }
        @media only screen and (min-width: 768px){
          .front_home_page{
            display: inline;
          }
        }
        .banner-timer{
          .countdown{
            position: absolute;
            top: -60px;
            text-align: center;
            left: 0;
            right: 0;
            font-weight: 500;
            letter-spacing: 2px;
            font-size: 22px;
            .deal_timer{
              margin: 0px 8px;
              padding: 0px 4px;
              height: 30px;
              display: inline-block;
            }
            .deal_text{
              display: none;
            }
            .clock{
              width: 100% !important;
            }
          }
        }
        #homepage_cart{
          .homepage-cart-title{
            font-weight: bold;
            margin-left: 5px;
            line-height: 30px;
            margin: 10px;
            border-bottom: 1px solid #eee;
          }
          .blaze-slider{
            .quantity, .item-price-font{
              font-weight: bold;
            }
            .item_block{
              .image-box{
                padding-left: 0.9375em;
                padding-right: 0.9375em;
              }
            }
          }
          .view-cart{
            text-align: center;
            background: $dark_red;
            color: white;
            padding: 10px;
            margin: 0px 10px;
          }
        }
        .homepage_design_blocks, .homepage-blocks{
          .design-details {
            margin: 1px 7px;
            max-width: 100%;
            width: 100%;
            font-size: 18px;
            font-weight: 600;
            .view-more-designs{
              font-size: 14px;
              text-align: right;
            }
          }
          .design-slider, .flash_deals{
            .design-slides{
              img{
                margin: auto;
                width: 100%;
              }
            }
          }
          .flash_deals, .design-slider{
            overflow-x: scroll;
            overflow-y: hidden;
            white-space: nowrap;
            scroll-behavior: smooth;
            .design-slider-wrapper{
              display: inline-block;
              width: 47%;
              overflow: hidden;
              text-align: center;
              border: 1px solid #eee;
            .design-col2{
                white-space: initial;
              }
              .design_desc{
                .details_block{
                  float: left;
                }
                .truncate{
                  font-size: $big_font;
                }
              }
            }
          }
        }
      }
      #more-items-loader{
        border: 4px dotted $dark_red;
        border-radius: 50%;
        width: 36px;
        height: 36px;
        margin: 0 auto;
        animation: spin 2s ease-in-out infinite;
      }
    }
  }
  //

  .ongoing-fd-timer{
    .deal_timer{
      margin: 0.3em;
      padding: 0.3em;
      background: $dark_red;
      color: $text_white;
      border-radius: 0.2em;
      font-size: $big_font;
    }
  }

  .truncate {
    width: 100%;
    // color: $text_black;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: initial;
    a{
      color: $text_black;
    }
  }

  .opera_footer_fix {position: relative;bottom: 0;width: 100%;z-index: 9999;margin-top: 12px;}
  .opera_footer_fix a {margin-bottom: 0em;width: 100%;left: 0px;font-weight: bold;line-height: inherit;}
  .opera_checkout_position_fix{width: 100%;margin-top: 18px;background-color: #0E9A7D !important;}

  .design_desc {
    padding: 0.5em;
    margin-bottom: 0rem;
    .wishlist-forms {
      .wishlist-heart-button {
        font-size: 1.5rem;
        margin: 0;
        padding: 0;
        float: right;
        background: transparent;
        color: $red_background;
        position: absolute;
        right: 0;
        &:focus {
          outline: none;
        }
      }
      .wishlist-heart-button.empty-heart {
        color: gray;
      }
    }
  }
  .details_block {
    font-size: $big_font;
    .actual_price{
      display: inline-block;
      color: #595959;
      text-decoration: line-through;
      margin-left: 3px;
      font-weight: 600 !important;
    }
  }
}
// *::-webkit-scrollbar {
//   display: none;
// }
@media only screen and (min-width: 1024px){
  #container{
    margin-top: 8.5rem !important;
  }
  div#boards{
    margin-right: 3rem;
    margin-left: 3rem;
  }
  .design-slider-wrapper{
    width: 25%;
  }
  .tag-slide {
    width: 20%;
  }
  .front_home_page{
    display: inline;
  }
  .flash_deals, .design-slider{
    .design-slider-wrapper{
      width: 20% !important;
    }
  }
}
a.round{
  text-decoration: none;
  display: inline-block;
  padding: 13px 15px;
  font-size: 31px;
  @media (max-width: 767px) {
    padding: 2px 7px;
  }
}
  
a.round:hover {
  background-color: #ddd;
  color: black;
}

.previous {
  background-color: #f1f1f1;
  color: black;
}

.next {
  background-color: #f1f1f1;
  color: black;
}

.design-slider {
  padding: 10px 0;
  &::-webkit-scrollbar {
    width: 3px;
    height: 5px;
  }
  &::-webkit-scrollbar-track {
    background: transparent;
  }
  &::-webkit-scrollbar-thumb {
    background-color: rgba(155, 155, 155, 0.5);
    border-radius: 20px;
    border: transparent;
  }
.truncate {
  color: #3c4345;
  font-size: 14px;
  font-weight: 500;
  line-height: 17px;
  padding: 5px 0;
}
}
.row.block-headers {
  font-size: 22px;
  padding: 10px 0;
  font-weight: 500;
  color: #3C4345;
}
body .design_desc .truncate {
  color: #3c4345;
  font-size: 15px;
  font-weight: 400;
  line-height: 17px;
  padding: 2px 0 4px;
}
.design-slides {
  width: 100%;
  max-width: 300px;
}
.homepage_design_blocks {
  padding: 20px;
  @media screen and (max-width: 700px) {
    padding: 5px;
  }
}

.home-page-widget .widget-title .design_blocks .swiper .swiper-wrapper .swiper-slide{
  .p-detail-box {
    color: #670b19 ;
    font-size: 1rem;
  }
 .price-flex.d-flex.price-container {
    justify-content: unset ;
    color: #595959;
    margin-left: 3px;
    font-size: 0.875rem;
    font-family: Inter;
  }
 .discount_price.title_wrap{
    color: #595959;
    margin: 0;
    font-size: 0.875rem;
  }
  .actual_price.discount-price {
    color: #595959;
    font-weight: 100 !important;
    font-size: 0.875rem;
  }
  .design-col2.details_block.percent_discount {
    font-size: 0.875rem;
    font-weight: 400;
    line-height: 1.25rem;
    letter-spacing: -0.02rem;
    text-align: left;
    padding-left: 2%;
    color: #26a541;
    font-size: 0.875rem;
  }
  a img {
    width: 100%;
  }
}
.design-slides {
  width: 100%;
  max-width: 300px;
}
.design-col.details_blocks {
  display: flex;
  margin: 0 5px;
}
.details_block {
  display: flex;
}
// new css
.home-unbox-slider{
  position: relative;
  .details_block.custom-home-page {
    font-size: 13px !important;
    font-weight: 600;
    color: #423f3f;
    text-wrap: wrap;
    flex-wrap: wrap;
  .custom-discount {
    display: inline-flex;
    color: #F44336;
    width: 100%;
  }
  }
  .truncate {
    font-size: 13px !important;
    padding: 2px 0 4px !important;
}
.prev-btn{
  position: absolute;
  top: 33%;
  z-index: 9;
}
.next-btn {
  position: absolute;
  right: 0;
  top: 33%;
  z-index: 9;
}
.prev-btn,
button.next-btn {
  display: flex;
  align-items: center;
  font-size: 38px;
  background: #f1f1f1;
  color: #670b19;
  border-radius: 50px;
  padding: 0 16px 3px;
}
}
.custom_home_blocks {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
}
.price-tag.d-flex {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.price-container {
  display: flex;
  font-family: Inter;
  font-weight: 400;
  line-height: 1.25rem;
  letter-spacing: -0.02rem;
  text-align: left;
}

.deal-text{
  margin-right : 0.4rem;
}

.lightning{
  display: flex;
  color: #B10D28;
  font-family: Inter;
  font-size: 0.9rem;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  letter-spacing: -0.32px;
}

.discount_price {
  color: #330033;
  font-size: 0.9375rem;
}

.design-col2.details_block.percent_disc {
  font-family: Inter;
  font-size: 0.875rem;
  font-weight: 400;
  line-height: 1.25rem;
  letter-spacing: -0.02rem;
  text-align: left;
  margin-right: 13px;
  color: #F44336;
}

.actual_price{
  color: #7c697c;
  text-decoration: line-through
}


img.product-image {
  border-radius: 7px;
}

.list-item {
  padding: 0.3rem;
}

.homeblock_elements {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
}

.p-detail-box{
  color: #636466;
  display: block;
  font-size: 12px;
  overflow: hidden;
  font-weight: 400;
  font-style: normal;
  line-height: 2em;
  height: 2em;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  overflow: hidden;
}

@media only screen and (max-width: 1023px) {
  .home-unbox-slider .prev-btn, .home-unbox-slider button.next-btn{
    display: none !important;
  }
}
@media only screen and (min-width: 1200px)  {
  .home-unbox-slider{
    .details_block.custom-home-page{
      font-size: 12px !important;
    }
    .truncate {
      font-size: 14px !important;
  }
  }
}
@import 'recommendation';
@import 'blaze';
@import 'unbxd_recommendations_red';
