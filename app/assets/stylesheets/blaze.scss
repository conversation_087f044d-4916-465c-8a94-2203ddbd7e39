.blaze-slider{
    --slides-to-show:1;
    --slide-gap:20px;
    direction:ltr;
    position: relative;
}
.blaze-track-container{
    overflow:hidden;
}
.blaze-track{
    will-change:transform;
    touch-action:pan-y;
    display:flex;
    gap:var(--slide-gap);
    --slide-width:calc( (100% - (var(--slides-to-show) - 1) * var(--slide-gap)) / var(--slides-to-show) );
    box-sizing:border-box;
}
.blaze-track>*{
    box-sizing:border-box;
    width:var(--slide-width);
    flex-shrink:0;
}

  .blaze-pagination button{
    color: transparent;    
    width: 3px;
    height: 3px;
    font-family: "Lato";
    font-size: 0;
    line-height: 0;
    color: black;
    opacity: 0.25;
    border-radius: 70%;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    padding: 0 !important;
  }
  .blaze-pagination button.active{
    background-color: black;
    opacity: 1;
  }
  .blaze-pagination {
    display: flex;
    gap: 15px;
  }

  .controls {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
    position: relative;
    left: 0;
    bottom: 10px;
  }
  .blaze-slider{
    direction:ltr;
    position: relative;
    overflow: hidden;

    .blaze-track {
      display: flex;
      align-items: center;
    }

    .blaze-item {
      flex: 0 0 auto;
    }

    .banner-wrapper {
      width: 100%;
      display: flex;
      justify-content: center;
    }

    .banner-slide {
      img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 0 auto;
      }
    }
}
.home-unbox-slider{
  .prev-btn{
    position: absolute;
    top: 33%;
    z-index: 9;
  }
  .next-btn {
    position: absolute;
    right: 0;
    top: 33%;
    z-index: 9;
  }
  .prev-btn,
  button.next-btn {
    display: flex;
    align-items: center;
    font-size: 38px;
    background: #f1f1f1;
    color: #670b19;
    border-radius: 50px;
    padding: 0 16px 3px;
    &:hover{
      background-color: $dark_red;
      color: #fff;
    }
  }
}
.foo-slider.blaze-slider {
  --slides-to-show: 6 !important;
}
.foo-slider.blaze-slider.unbxd-blaze-container.start .blaze-prev,
.foo-slider.blaze-slider.unbxd-blaze-container.end .blaze-next {
  display: none;
}
@media (max-width: 1200px) {
  .foo-slider.blaze-slider {
    --slides-to-show: 5 !important;
  }
}
@media (max-width: 768px) {
  .foo-slider.blaze-slider {
    --slides-to-show: 3.7 !important;
  }
}

@media (max-width: 640px) {
  .foo-slider.blaze-slider {
    --slides-to-show: 2.7 !important;
  }
}

@media (max-width: 480px) {
  .foo-slider.blaze-slider {
    --slides-to-show: 1.7 !important;
  }
}
@media (max-width:1023px) {
  .prev-btn,
  button.next-btn{
    display: none !important;
  }
}