.gift-card-order-new {
  font-size: 16px;

  max-width: 500px;
  margin: auto;
  margin-top: 3.5em;

  .form-group {
    position: relative;

    label {
      display: none;
    }

    label.gift-card-thumb {
      display: initial;
    }

    small {
      position: relative;
      top: -0.75em;
      font-size: 0.7em;
    }

    .form-button {
      padding: 0.8em;

      display: block;
      width: 100%;

      border: 1px solid #670b19;

      background-color: #b11f2b;
      color: white;

      text-transform: uppercase;
      font-weight: bold;
    }
  }

  fieldset {
    .gift-card-selection-list {
      list-style-type: none;
      text-align: center;

      margin: auto;
      position: relative;

      .gift-card-selection-list-item {
        display: inline;
        margin: 0 0.5em;
      }

      input {
        display: none;

        &:checked ~ label:nth-child(3) {
          border: 2px solid #b11f2b;
        }

        & ~ label:nth-child(3) {
          border: 2px solid transparent;
          padding: 2px;
          display: inline-block;
        }

        &:checked ~ .gift-card-selection-preview {
          display: initial;
        }
      }

      .gift-card-selection-preview {
        float: left;

        display: none;
        position: relative;
        margin-top: 1em;

        left: 0;
      }
    }
  }

  .form-group:last-child {
    .form-text {
      top: unset;
    }
  }
}

.gift-card-order-show {
  font-size: 16px;

  margin-bottom: 2em;

  .acknowledgement {
    font-size: 1.2em;
    line-height: 1;
    text-align: center;
    
    background-color: rgb(238, 238, 238);
    
    max-width: 700px;
    margin: auto;
    padding: 1.5em;
    margin-top: 3em;
  }
}