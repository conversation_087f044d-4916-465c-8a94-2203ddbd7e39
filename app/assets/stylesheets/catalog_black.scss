/*Catalog Specific CSS */
//importing variables
@import 'variables';
@import 'black_theme';
body.modal-open{
  overflow: hidden;
  position: fixed;
}
body {
  font-family: "Inter", sans-serif !important;
}

body {
  overflow: scroll !important;
  .heading_underline {
    text-decoration: underline;
  }
  .line_through_text {
    text-decoration: line-through;
  }
  .truncate {
    width: 100%;
    color: $text_black;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .store_page_design {
    margin: 0.1em;
    > li {
      -webkit-transition: all 0.5s ease;
      -moz-transition: all 0.5s ease;
      -o-transition: all 0.5s ease;
      transition: all 0.5s ease;
      padding: 0 0.15rem 0.35rem;
      img {
        width: 100%;
      }
      .label-ribbon:before{
        font-family: "foundation-icons";
        content: "\f12b";
        color: #f1f1f1 !important;
        font-size: 16px;
        padding-right: 5px;
      }
      .label-ribbon{
        border-color: #7d1438 !important;
        position: absolute;
        font-weight: 500;
        font-size: 12px;
        padding: 4px 0.5em 4px 0.5em;
        background-color: #d43153 !important;
        color: #f1f1f1 !important;
        display: inline-block;
        line-height: 1;
        margin: -16px 0px 0px -10px;
        border: 0 solid transparent;
        z-index: 1;
      }
      .label-ribbon:after{
        position: absolute;
        content: '';
        top: 100%;
        left: 0;
        background-color: transparent !important;
        border-style: solid;
        border-width: 0px 0.78em 1em 0px;
        border-color: transparent;
        border-right-color: inherit;
        width: 0;
        height: 0;
      }
      .b1g1{
        &.label-ribbon:before{
          content: '';
        }
      }
      .fr_page {
        box-shadow: $card_box_shadow;
        .panel {
          position: relative;
          padding: 0.4em 0.3em 0.5em 0.3em;
          background: $snow_white;
          border: 1px solid $snow_white;
          &.design_desc {
            margin-bottom: 0em;
            a {
              color: $text_black;
            }
          }
          .add_to_cart_link {
            margin-bottom: 0.7em;
            width: 89%;
            bottom: 0em;
            color: $text_white;
            background: #0e9a7d;
            position: absolute;
            padding: 10px 2px;
            box-shadow: $green_btn_box;
            font-weight: 700;
          }
          .sold_out_link {
            margin-bottom: 0.7em;
            width: 89%;
            bottom: 0em;
            color: $text_white;
            font-weight: 700;
          }
        }
      }
    }
    li {
      &.original_price {
        font-size: 0.8em;
        color: white;
      }
      &.discount_price {
        font-weight: bold;
        color: white;
      }
      &.percent_off {
        color: red;
        margin-top: -1.7em;
        font-size: 0.9em;
      }
    }
    .design_desc {
      padding: 0.5em;
      li {
        padding-bottom: 0em;
      }
    }
  }
  .postfix {
    border-style: solid;
    border-width: 1px;
    display: block;
    font-size: 0.875rem;
    height: 2.3125rem;
    line-height: 2.3125rem;
    overflow: visible;
    padding-bottom: 0;
    padding-top: 0;
    position: relative;
    text-align: center;
    width: 100%;
    z-index: 2;
  }

  .postfix.button {
    border-color: true;
  }

  .postfix.button.radius {
    border-radius: 0;
    -webkit-border-bottom-right-radius: 3px;
    -webkit-border-top-right-radius: 3px;
    border-bottom-right-radius: 3px;
    border-top-right-radius: 3px;
  }

  .postfix.button.round {
    border-radius: 0;
    -webkit-border-bottom-right-radius: 1000px;
    -webkit-border-top-right-radius: 1000px;
    border-bottom-right-radius: 1000px;
    border-top-right-radius: 1000px;
  }

  span.postfix, label.postfix {
    background: #f2f2f2;
    color: #333333;
    border-color: #cccccc;
  }

  .store_page_block {
    margin-bottom: 2em;

    #top_content {
      &.read-more{
        height:25px;
        overflow:hidden;
      }
      font-size:0.9rem;
      #hidden_content {
        > * {
          font-size:0.9rem;
        }
      }
      > * {
        font-size:0.9rem;
      }
    }

    a#view-more-top-content,a#view-more-seo-post {
      color:#008CBA;
      font-size: 0.9rem;
      text-align: center;
    }

    .heading_title{
      padding: 0.2rem 0rem;
      max-width: 100%;
      .columns{
        padding: 0px;
      }
      .product-title{
        font-size: 1.2rem;
        h1{
          margin: 0px;
        }
        .product_count{
          font-size: 0.8rem;
        }
      }
      #toggle-design-box{
        float: right;
        text-align: right;
        span{
          font-size: 0.9rem;
          vertical-align: super;
        }
        i{
          font-size: 2rem;
          vertical-align: top;
        }
      }
    }
    #category_links{
      overflow-x: auto;
      overflow-y: hidden;
      white-space: nowrap;
      padding: 0.3rem 0rem;
      max-width: 100%;
      .custom{
        background-color: #009688;
        border-color: #009688;
        color: #FFFFFF;
        font-size: 0.8rem;
        padding: 0.8em;
        margin-bottom: 0.3rem;
        box-shadow: $green_btn_box;
      }
    }
    #action_buttons{
      /*padding: 0 0.625rem;*/
      #short_btn{
        padding: 0;
      }
      #filter_btn{
        padding: 0 0.625rem 0 0;
      }
      .select_box{
        width: 100%;
        overflow: hidden;
        position: relative;
        height: 50px;
        #custom_sort{
          border: 2px solid $text_black;
          width: 100%;
          height: 100%;
          position: absolute;
          left: 10px;
          top: 0px;
          text-transform: uppercase;
          font-size: 12px;
          color: $text_filter_color;
          padding-top: 18px;
        }
        .form_input_m_select{
          width: 100%;
          font-size: 14px;
          border: 0;
          overflow: hidden;
          text-transform: none;
          background: 0 0;
          -webkit-appearance: none;
          opacity: 0;
          filter: alpha(opacity=0);
          margin-bottom: 12px;
        }
      }
      #filter-button{
        width: 100%;
        height: 50px;
        background: #e7e7e7;
        color: $text_filter_color;
        border: 2px solid $text_black;
        font-size: 12px;
      }
      .filter-border{
        border-left-color: #424242 !important;
        border-left-width: 1px !important;
      }
    }

    #more-designs-loader{
      display: none;
      border: 4px dotted #fff;
      border-radius: 50%;
      width: 36px;
      height: 36px;
      margin: 0 auto;
      animation: spin 2s ease-in-out infinite;
    }
    #load-more-designs-btn{
      display: none;
      font-size: 14px;
      border-radius: 2px;
      box-shadow: 0px 4px 2px #1b524d;
      cursor: pointer;
      background-color: #0e9a7d;
      border-color: #237363;
    }

    .float-Btn{
      position: fixed;
      bottom: 16px;
      right: 12px;
      z-index: 19;
      i:before{
        background-color: #d44777;
        width: 3.5rem;
        height: 3.5rem;
        line-height: 3.5rem;
        border-radius: 50%;
        color: #fff;
        text-align: center;
        font-size: 2.2rem;
        opacity: 0.95;
      }
    }
    #back-top{
      display: none;
    }
    #toggle-design-box{
      #toggle-design-icon{
        -webkit-transition: all 0.5s ease;
        -moz-transition: all 0.5s ease;
        -o-transition: all 0.5s ease;
        transition: all 0.5s ease;
      }
    }
  }

  .reveal-modal {
    overflow: scroll;
    height: 100%;
    top: 0px;
    bottom: 0px;
    margin-top: 0px;
    position: fixed;
    -webkit-overflow-scrolling: touch;
    #loader {
      display: none;
      border: 10px dotted #fff;
      border-radius: 50%;
      width: 60px;
      height: 60px;
      position: absolute;
      left: 43%;
      z-index: 9999;
      top: 44%;
      animation: spin 2s ease-in-out infinite;
    }
  }


  #designable_details{
    border-radius: 0px;
    height: 100%;
    .chips_maker{
      background: #000000;
      padding: 0px 4px;
      display: none;
      border-radius: 2px;
      .chip{
        display: inline-block;
        padding: 2px 10px;
        height: 25px;
        margin: 3px 2px;
        font-size: 15px;
        line-height: 20px;
        border-radius: 16px;
        background-color: #607D8B;
        .closebtn{
          padding-left: 10px;
          color: #FF9800;
          font-weight: bold;
          float: right;
          font-size: 20px;
          cursor: pointer;
        }
      }
    }
    .panel_heading{
      padding: 0px;
    }
    .panel_content.small-4{
      padding: 0px 0px 59px 0px;
      height: 100%;
      overflow-y: scroll;
      border-radius: 0px;
      background-color: $gray_bg;
    }
    .tabs-content{
      padding-bottom: 59px;
      width: 66%;
      background-color: #fff;
      height: 100%;
      margin-bottom: 0px;
      overflow-y: scroll;
      border-radius: 0px;
      .active{
        padding-bottom: 0px;
      }
      .on-off-radiobox{
        display: inline-block;
        margin-bottom: 1rem;
        label {
          color: white;
        }
      }

      .on-off-checkbox{
        display: inline-block;
        margin-bottom: 1rem;
        label {
          color: white;
        }
      }

      .on-off-checkbox.color-switch{
        margin-bottom: 0.5rem !important;
        width: 25%;
        .color-input{
          visibility: hidden;
          width: 0;
        }
        .label{
          position: relative;
          left: 20px;
          bottom: 20px;
        }
        .label-custom-color{
          width: 30px;
          height: 30px;
          box-sizing: border-box;
          margin: 0px;
          position: relative;
          bottom: 5px;
          box-shadow: 1px 1px 2px #808080;
          border-radius: 4px;
        }
        .label-custom-color:after {
          content: '\2713';
          display: block;
          position: absolute;
          bottom: -5px;
          left: 4px;
          opacity: 0;
          color: whitesmoke;
          font-size: 22px;
        }
        input:checked + .label-custom-color:after {
          opacity: 1;
        }
        .multicolor-value{
          background: -webkit-linear-gradient(45deg, #edf1ee 0%, #8BC34A 25%, #FFEB3B 25%, #FFC107 40%, #FF5722 40%, #E91E63 60%, #673AB7 60%, #3F51B5 75%, #03A9F4 75%, #040404 100%);
          background: linear-gradient(45deg, #edf1ee 0%, #8BC34A 25%, #FFEB3B 25%, #FFC107 40%, #FF5722 40%, #E91E63 60%, #673AB7 60%, #3F51B5 75%, #03A9F4 75%, #040404 100%);
        }
      }
      .switch.tiny{
        label {
          height: 1rem;
          width: 2rem;
          background: #9e9696;
        }
        label:after {
          height: 0.5rem;
          width: 0.7rem;
          background: #464a46;
        }
        input{
          left: 8px;
          top: 5px;
        }
        input:checked + label {
          background: #43ac6a;
        }
        input:checked + label:after {
          left: 0.9rem;
        }
      }

      .label-custom{
        vertical-align: top;
        display: inline-block;
        width: 75%;
        float: right;
        .label{
          font-size: 13px;
        }
      }
      .label-desktop-fix{
        width: 90%;
      }
    }
    header{
      .tab-filter-fix{
        font-size: 13px;
        background-color: $gray_bg;
        width: 100%;
        padding: 10px 0px 10px 5px;
        text-align: left;
        margin: 0px;
        line-height: 16px;
        font-weight: 700;
        text-transform: capitalize;
        .tiny-green{
          border-radius: 50%;
          background: #43ac6a;
          color: black;
          padding: 2px 5px;
          margin-left: 3px;
          font-size: 10px;
          font-weight: 700;
        }
      }
      .active-tab{
        background-color: #5d5d5d;
      }
    }

    .short_filter_btn{
      position: fixed;
      bottom: 0;
      left: 0;
      width: 100%;
      box-shadow: 0 2px 34px -4px rgba(0,0,0,.7);
      background: #c1bbae;
      div{
        background-color: #c1bbae !important;
        color: #1f1c1c;
        margin: 0px;
        font-size: 16px;
        border-right: 1px solid #0c0c0c;
        padding: 18px 0px;
      }
    }

    .filter-desktop-fix{
      bottom: 10px;
      left: 172px;
      width: 74%;
    }

  }

  .navigate_page {
    vertical-align: middle;
    margin-bottom: 1em;
  }

  .nav-button {
    margin: 1em 1em 0 0;
  }

  #design_details {
    width: 100%;
  }

  .details_block {
    color: $text_black;
    float:left;
  }

  .discount_new_block{
    font-size: 12px;
    background-color: #e0356f;
    border-radius: 5%;
    padding: 6px 6px;
    line-height: 12px;
    text-align: center;
    position: relative;
    margin-top: -23px;
    float: right;
    color: $text_white;
  }

  .discount_new_wrap {
    padding: 1px;
  }

  .add_new_pos {
    position: relative !important;
    width: 100% !important;
  }

  .discount_font {
    font-size: 0.7em;
    font-weight: normal;
    float:left;
    color:#ffffff;
  }
  .margin-down-5{
    margin-bottom:5px;
  }
  #search_desktop{
    display: none;
  }

  .search_margin{
    margin-top:-15px !important;
  }
}

.home_page_designs {
  margin: 0.1em;
  >li {
    img{
      width:100%;
    }
    .fr_page {
      box-shadow: $card_box_shadow;
      position: relative;
      .panel {
        height: 6.6em;
        position: relative;
        padding:0.6em;
        background: $snow_white;
        border: 1px solid $snow_white;

        &.design_desc {
          margin-bottom: 0em;
          a {
            color: $text_black;
          }
        }
      }
    }
  }
  li {
    &.original_price {
      font-size: 0.8em;
      color: $text_black;
    }
    &.discount_price {
      font-weight: bold;
      color: $text_black;
    }
    &.percent_off {
      color: red;
      margin-top: -1.7em;
      font-size: 0.9em;
    }
  }
  .design_desc {
    padding: 0.5em;
    li {
      padding-bottom: 0em;
    }
  }  

  #design_details {
    width:100%;
  }

  .details_block {
    color: $text_black;
  }

  .design-col1 {
    float:left;
    width:100%;
    font-size: 15px !important;
    font-weight: bold;
  }

  .design-col2 {
    float:right;
    border-radius: 5%;
    font-size: 12px;
    background-color: #e0356f;
    color: $text_white;
    padding: 6px 6px;
    line-height: 12px;
    text-align: center;
    position: relative;
    margin-top: -24px;
  }

  .add-to-cart-bst{
    width: 100%;
    color: white;
    background: #0e9a7d;
    padding: 12px 2px;
  }

  .discount_new_wrap {
    text-align:center;
    word-wrap: break-word;
    padding-left: 0.5px;
  }

  .add_new_pos {
    position: relative !important;
    width: 100% !important;
  }

  .discount_font {
    font-size: 0.8em;
  }

  .design_price {
    font-weight: bold;
  }
}

.footer-info {
  /*border:1px dotted grey;*/
  padding:8px;
  background-color:#fff9fa;
  margin-bottom:20px;
  margin-top:20px;
  box-sizing:border-box;
  #seo_post {
    .seo-list-anchor {
      font-size: 14px;
      padding: 5px;
      line-height: 17px;
    }
    .seo-list-table {
      width:70%;
      border: 1px solid white;
    }
    .seo-list-line-height {
      line-height:30px;
    }
    .seo-list-font {
      font-size:14px;
    }
    h2 {
      font-size: 1.5em;
      color: $text_black;
    }
    p {
      font-size: 1em;
      color: $text_black;
      text-align: justify;
    }
    &.read-more{
      height:7.8em;
      overflow:hidden;
    }
  }
}
.catalog-cert {
    position: absolute;
    width: 40px !important;
    height: 40px;
}
.catalog-rating {
    position: relative;
    float: left;
    width: 40px !important;
    height: 40px;
    margin-top:-25px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@media only screen and (min-width: 64.063em) { 
  .search_margin{
    display: none;
  }
  #search_desktop{
    display: block;
  }
}

@media screen and (min-width: 40em) {
  #toggle-design-box{
    display: none !important;
  }
}
.rating_div{
  .small_rating{
      font-size: 12px;
      background-color: #16be48;
      color: white;
      padding: 5px;
      border-radius: 5%;
      font-weight: bold;
    }
  .green-rating{
     background-color: #16be48;
    }
  .red-rating{
     background-color: #FF5722;
    }
  .orange-rating{
     background-color: #FFA000;
    }
}
/*.star-yellow:before {
      content: "\2605";
      display: inline-block;
      background: -webkit-linear-gradient(left, #FFC315 100%, #e2e0e0 0%);
      background: -o-linear-gradient(left, #FFC315 100%, #e2e0e0 0%); 
      background: -moz-linear-gradient(left, #FFC315 100%, #e2e0e0 0%); 
      background: linear-gradient(left, #FFC315 100%, #e2e0e0 0%); 
      -webkit-text-fill-color: transparent;
      color: transparent;
      -webkit-background-clip: text;
      background-clip: text;
      font-size: 115%;
    }
.star-gray:before {
      content: "\2605";
      display: inline-block;
      background: -webkit-linear-gradient(left, #e2e0e0 100%, #FFC315 0%);
      background: -o-linear-gradient(left, #e2e0e0 100%, #FFC315 0%); 
      background: -moz-linear-gradient(left, #e2e0e0 100%, #FFC315 0%); 
      background: linear-gradient(left, #e2e0e0 100%, #FFC315 0%); 
      -webkit-text-fill-color: transparent;
      color: transparent;
      -webkit-background-clip: text;
      background-clip: text;
      
      font-size: 115%;
    }
.star-half:before{
    content: "\2605";
    display: inline-block;
    background: -webkit-linear-gradient(left, #FFC315 49%, #e2e0e0 50%);
    background: -o-linear-gradient(left, #FFC315 49%, #e2e0e0 50%); 
    background: -moz-linear-gradient(left, #FFC315 49%, #e2e0e0 50%); 
    background: linear-gradient(left, #FFC315 49%, #e2e0e0 50%); 
    -webkit-text-fill-color: transparent;
    color: transparent;
    -webkit-background-clip: text;
    background-clip: text;
    font-size: 115%;
}

.star_align{
  padding: 0px;
  text-align: center;
}
*/

.banner-container {
  border: 1px solid white;
  text-align: center;
  margin-bottom: 0.5rem;
  width: 99%;
}

.pagination-container{
  position: relative;
}

.tripple-dots.swiper-pagination.swiper-pagination-clickable.swiper-pagination-bullets.swiper-pagination-horizontal{
  position: absolute;
  bottom: 0;
  margin-bottom: -6px;
}

.swiper-pagination-bullet {
  background-color: #670b19;
}

.swiper-pagination-bullet.swiper-pagination-bullet-active {
  background-color: #670b19;
}