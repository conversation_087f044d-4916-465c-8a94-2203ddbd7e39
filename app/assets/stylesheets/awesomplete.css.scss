.awesomplete {
  width: 100%;
  [hidden] {
    display: none;
  }
  .visually-hidden {
    position: absolute !important;
    clip: rect(0, 0, 0, 0);
  }
  display: inline-block;
  position: relative;
  > {
    input {
      display: block;
    }
    ul {
      position: absolute;
      left: 0;
      z-index: 1;
      min-width: 100%;
      box-sizing: border-box;
      list-style: none;
      padding: 0;
      margin: 0;
      background: #fff;
      &:empty {
        display: none;
      }
      border-radius: .3em;
      margin: .2em 0 0;
      background: hsla(0, 0%, 100%, 1);
      background: linear-gradient(to bottom right, white, hsla(0, 0%, 100%, 1));
      border: 1px solid rgba(0, 0, 0, 0.3);
      box-shadow: 0.05em 0.2em 0.6em rgba(0, 0, 0, 0.2);
      text-shadow: none;
      &:before {
        content: "";
        position: absolute;
        top: -.43em;
        left: 1em;
        width: 0;
        height: 0;
        padding: .4em;
        background: white;
        border: inherit;
        border-right: 0;
        border-bottom: 0;
        -webkit-transform: rotate(45deg);
        transform: rotate(45deg);
      }
      li {
      	font-size: 0.9em;
        position: reative;
        padding: .2em .5em;
        color: black;
        cursor: pointer;
        &:hover {
          background: hsl(200, 40%, 80%);
        }
        &[aria-selected="true"] {
          background: hsl(205, 40%, 40%);
        }
      }
    }
  }
  mark {
    background: hsl(65, 100%, 50%);
  }
  li {
    &:hover mark {
      background: hsl(68, 100%, 41%);
    }
    &[aria-selected="true"] mark {
      background: hsl(86, 100%, 21%);
      color: black;
    }
  }
}
input::-webkit-calendar-picker-indicator {
  display: none;
}
