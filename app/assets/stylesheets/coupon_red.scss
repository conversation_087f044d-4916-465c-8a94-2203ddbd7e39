body {
  font-family: "Inter", sans-serif !important;
}
.main-form-container{
  display: flex;
  width: 100%;
}

.back-arrow-container{
  display: flex;
  position: relative;
  left: 2px;
  height: 35px;
  width: 40px;
  border: 1px solid #681e19;
  color: #681e19;
  border-radius: 5px;
  align-content: center;
  justify-content: center;
  margin-right: 10px;
  margin-bottom: 25px;
}

.back-arrow-container span{
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-weight:100;
  padding-top: 2px;
}

.right-container{
  width: 100%;
}

.left-container a:hover{
  color: #fff;
}

#coupon-main{
  margin-top: 5rem;
}
#coupon-form{
  .flasherror {
    font-weight: bold;
    background-color: #e88888;
    padding: 5px 9px 3px 5px;
    font-size: 12px;
    width: 98%;
    margin-left: 5px;
    border-radius: 4px;
    line-height: 1.2;
    margin-bottom: 10px;
  }
  .display-text {
    font-weight: normal;
    padding: 0px 0px 0px 8px;
  }
  .space-needed{
    margin-bottom: 100%;
  }
  .coupon-table{
    width: 98%;
    margin-left: 4px;
    border-radius: 5px;
  }
  .coupon-row{
    border: 1px solid gray;
    padding: 5px;
    margin-bottom: 1rem;
    display: flex;
    flex-direction: column;
    width: 100%;
    border-radius: 10px;
  }
  .coupon-details{
    display: flex;
    padding-bottom: 2px;
  }
  .coupon-image{
    width: 27%;
    img{
      max-width: 100px;
      height: 100px;
    }
  }
  .coupon-description{
    padding-left: 10px;
    padding-right: 10px;
    padding-bottom: 0px;
    padding-top: 0px;
  }
  .coupon-percent{
    font-size: 18px;
    font-weight: bold;
     font-family: "Inter", sans-serif ;
    margin: 7px;
    margin-bottom: 9px;
    letter-spacing: 0.4px;
  }
  .min-amount{
    font-size: 11px;
     font-family: "Inter", sans-serif ;
    font-weight: bold;
    color: #413535;
    line-height: normal;
    letter-spacing: 0.4px;
    margin: 7px;
  }
  .expiry-date{
    font-size: 11px;
     font-family: "Inter", sans-serif ;
    font-weight: bold;
    line-height: normal;
    letter-spacing: 0.4px;
    margin: 7px;
    color: #E3795E;
  }
  .coupon-code-div{
    color: #E3795E;
    height: 50px;
    display: flex;
    -webkit-box-pack: justify;
    justify-content: space-between;
    width: 100%;
    margin: 2px;
    border: 0.1px solid gray;
    border-radius: 10px;
  }
  .coupon-code{
    font-weight: bold;
    display: flex;
    -webkit-box-align: center;
    align-items: center;
    font-size:120%;
  }
  .apply-button{
    font-weight: bold;
    padding: 5px !important;
    color: #5E1C1B !important;
    background-color: transparent !important;
  }
  .button-group{
    display: flex;
    align-items: stretch;
    border: none;
    .btn-cancel{
      background-color: #5F1C1B;
      border-radius: 6px;
      color: white;
      padding: 7px;
      display: grid;
      text-align: center;
      margin-left: 10px;
      flex: 1;
    }
  }

  .coupon-input-container {
    display: flex;
    width: 98%;
    margin-left: 5px;
    margin-bottom: -11px; 
  }
  .coupon-code-input {
    flex: 1;
  }
  .check-button {
    margin-left: 10px;
    padding: 5px 10px;
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 16px;
  }
  .btn-apply{
    margin-left: 10px; 
    padding: 10px 12px;
    background-color: #681e19;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 16px;
  }
  .back-link {
    display: inline-flex;
    align-items: center;
    text-decoration: none;
    font-size: 20px; 
    color: #333; 
    margin-right: 10px;
  }
}
@media only screen and (min-width: 1024px){
  form#coupon-form {
    padding: 0 2rem 2rem;
    width: 60%;
  }

  .coupon-page-content{
    display: flex;
    margin-top: 8em;
    justify-content: center;
    align-items: center; 
  }
  .coupon-description{
    padding-left: 80px;
  }
}

#coupon-form .left-container a#desktop-back-link {
  display: block;
}
#coupon-form .right-container a#mobile-tablet-back-link{
  display: none;
}

@media screen and (max-width:991px) {
  .coupon-page-content{
    padding: 0 0 100px;
  }
  #coupon-form .left-container a#desktop-back-link {
    display: none;
  }
  #coupon-form .right-container a#mobile-tablet-back-link{
    display:block;
  }
}
.form-offers-container {
  width: 100%;
  max-width: 650px;
  margin: auto;
}
@media only screen and (max-width: 1024px){
  .coupon-page-content {
    padding: 40px 0 155px;
}
#coupon-form .display-text {
  padding: 20px 0px 10px 8px;
}
#coupon-form .coupon-table {
margin-bottom: 3.25rem;
}
#coupon-form .coupon-input-container {
margin-bottom: 0;
}
.coupon-row{
 margin-bottom: 30px;
}
}
@media screen and (max-width:991px) {
  .coupon-page-content {
   height: 100vh;
   overflow-y: auto;
}

}
@media screen and (max-width:767px) {
  .display-text {
    padding: 0 0px 0 58px;
  }

}