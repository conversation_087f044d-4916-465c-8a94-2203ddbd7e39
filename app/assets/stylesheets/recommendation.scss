.home-unbox-slider{
  position: relative;
  .price-container{
    justify-content: flex-start;
    padding: 0 5px;
  }
  .percent_discount {
    color: #ff0000 !important;
}
  .details_block.custom-home-page,.percent_discount, .p-detail-box  {
    font-weight: 600;
    color: #423f3f;
    width: 100%;
    .actual_price {
      margin: 0 10px;
  }
  .custom-discount,.percent_discount {
    display: inline-flex;
    color: #F44336;
  }
  }
}
.custom_home_blocks {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
}
.price-tag.d-flex {
  justify-content: flex-start !important;
}
@media (max-width:1023px) {
  .prev-btn,
  button.next-btn{
    display: none !important;
  }
}
