
html, body {
  height: 100%;
}

html {
  box-sizing: border-box;
}

* {
  -webkit-box-sizing: inherit;
  -moz-box-sizing: inherit;
  box-sizing: inherit;
  &:before, &:after {
    -webkit-box-sizing: inherit;
    -moz-box-sizing: inherit;
    box-sizing: inherit;
  }
}

body,html{
  height:100%;
  width:100%;
}

html {
  font-size: 100%;
}

body {
  cursor: auto;
  font-style: normal;
  font-weight: normal;
  line-height: 1.5;
  margin: 0;
  padding: 0;
    font-family: "Inter", sans-serif !important;
  position: relative;
  overflow-x: hidden !important;
}

a {
  line-height: inherit;
  text-decoration: none;
  img {
    border: none;
  }
}

#menu-side-nav {
  display: none;
}

.off-canvas-wrap {
  .inner-wrap {
    nav.tab-bar {
      .cart-icon{
        background-image: url('data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7');
        background-repeat: no-repeat;
        display: inline-block;
        width: 2em;
        height: 2em;
        vertical-align: middle;
        background-size: 2em;
      }
      .search-box-margin {
        margin-top: 0.25em;
      }
      #cart_count {
        margin: 0.5em -2em;
        padding: 0.4em;
        background-color: red;
        color: white;
        font-weight: bolder;
      }

      .logo{
        background-image: url('data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7');
        background-repeat: no-repeat;
        display: inline-block;
        width: 6em;
        height: 2em;
        vertical-align: middle;
        background-size: 6em 2em;
      }

      .download-app {
        height:1.7em;
        width:6.8em;
        background-color: #399b44;
        padding: 5px 8px;
        color: #fff;
        border-radius: 10%/45%;
        box-shadow: 1px 1px 3px #444, inset 0px 0px 6px #262;
        text-shadow: 0px 0px 2px #444;
        display:inline;
        font-size: 0.7rem;
      }
    }
    
    div.tab-bar{
      header.scroll{
        width: 95%;
        white-space: nowrap;
        overflow-x: auto;
        overflow-y: hidden;
        -webkit-overflow-scrolling: touch;
        -ms-overflow-style: -ms-autohiding-scrollbar;

        nav{
          margin: -7px 4px -3px 4px;
          i.fi-burst-new{
            font-size: 22px;
            vertical-align: middle;
            margin: -4px 0px;
            color: #de6b23;
            position: absolute;
          }
        }
      }
      #trending-results-box{
        background: white;
        display: none;
        padding: 0px 15px 2px 15px;
        border-radius: 0px 0px 2px 2px;
        .trending-results-text{
          border-bottom: 1px solid #d4d4d4;
          color: #272626;
          font-weight: 600;
          .trending-icon{
            color: #292929;
          }
        }
        ul#trending-results{
          margin: 0px;
          li{
            list-style-type: none;
            font-size: 16px;
            text-transform: capitalize;
            padding: 10px 0px;
            a{
              color: #271f35;
            }
          }
        }
      }
      .show-for-small-up.search_margin{
        width: 96%;
        margin: 0 auto;
        margin-top: -15px;
        padding: 6px 0px;
        display: none;
        input[type='text']{
          border-radius: 2px 0px 0px 2px;
          padding-left: 18px;
          margin-bottom: 0px;
          border-bottom: none;
          background-color: white;
          &:focus{
            box-shadow: none;
            border-bottom: none;
          }
          &::-webkit-input-placeholder { /* Chrome/Opera/Safari */
            color: rgba(39, 31, 53, 0.67);
            font-size: 15px;
          }
          &::-moz-placeholder { /* Firefox 19+ */
            color: rgba(39, 31, 53, 0.67);
            font-size: 15px;
          }
          &:-ms-input-placeholder { /* IE 10+ */
            color: rgba(39, 31, 53, 0.67);
            font-size: 15px;
          }
          &:-moz-placeholder { /* Firefox 18- */
            color: rgba(39, 31, 53, 0.67);
            font-size: 15px;
          }
        }
        button.submit_btn{
          border-radius: 0px 2px 2px 0px;
          padding: 0px;
          margin-bottom: 0px;
          i{

          }
        }
      }
      .scroll-button{
        position: absolute;
        font-size: 20px;
        line-height: 31px;
        background-color: #655d5d;
        color: #dedbdb;
        text-align: center;
        height: 33px;
      }

      #scroll-btn-right{
        display: none;
        top: 0px;
        z-index: 1;
        width: 8%;
        &:after{
          content: '';
          width: 10px;
          height: 10px;
          position: absolute;
          transform: rotate(-45deg);
          border-left: 1px solid white;
          border-top: 1px solid white;
          top: 10px;
          left: 10px;
        }
      }

      #scroll-btn-left{
        top: 0px;
        right: 1px;
        &:after{
          content: '';
          width: 10px;
          height: 10px;
          position: absolute;
          transform: rotate(-45deg);
          border-right: 1px solid white;
          border-bottom: 1px solid white;
          top: 10px;
          right: 10px;
        }
      }

      .tab-fix{
        font-size: 0.8rem !important;
        padding: 0.5rem 0.8rem !important;
        width: fill-content;
        border-radius: 0px;
        background-color: #e7e7e7;
        border-color: #b9b9b9;
        color: #333333;
        margin-left: -3px;
      }

      .tab-fix:hover, .tab-fix:focus{
        background-color: #e7e7e7;
        border-color: #b9b9b9;
      }

      .medium-tab-button{
        width: 100%;
      }
    }

    .search_margin{
      margin-top: -15px;
      input[type='text']{
        color: #383737;
      }
    }
      
  }
  .submit_btn {
    background-color: white;
    border-color: transparent;
    border-left-color: #737373;
    i.fi-magnifying-glass{
      color: #1b1b1b;
      font-size: 18px;
    }
  }
  .main-section {
    margin-top: 7vh;
    overflow: hidden;
    position: relative;
    -webkit-overflow-scrolling: touch;
  }

  #main-section{
    width: 105vw;
    height: 99%;
    overflow: auto;
    padding-right: 1em;
  }

  ul.off-canvas-list li label
  {
    padding: 0.4em 0.6em;
  }

  .left-submenu .back > a
  {
    padding: 0.1em 0.6em;
  }

  #container{
    width: 96vw;
    overflow-y: hidden;
    padding: 1em 0em;
    margin: 2%;
    margin-top: 5em;
    overflow-x: hidden;
  }

  .left-off-canvas-menu, .right-off-canvas-menu {
    min-height: 200vh;
  }
}
.no-scroll-background{
  overflow: hidden !important;
}
.static-nav-bar{
  -webkit-transform: translate3d(0px, -33px, 0px);
  -moz-transform: translate3d(0px, -33px, 0px);
  -ms-transform: translate(-33px, 0);
  -ms-transform: translate3d(0px, -33px, 0px);
  -o-transform: translate3d(0px, -33px, 0px);
  transform: translate3d(0px, -33px, 0px);
  transition: transform .3s ease-in-out;
  width: 70% !important;
}
section.main-section{
  display: none;
}

@media only screen
  and (min-width: 320px)
  and (max-width: 568px){
  .download-app {
    font-size: 0.5rem !important;
    padding: 6px 5px !important;
  } 
}
@media only screen and (min-width: 64.063em) {
  .show-for-small-up.search_margin{
    width: 40% !important;
    margin-top: -98px !important;
  }
  div#search-tab{
    height: 2.3125rem !important;
    #trending-results-box{
      position: fixed;
      width: 40%;
    }
  }
}

.left {
  float: left !important;
}

.right {
  float: right !important;
}

.off-canvas-wrap {
  -webkit-backface-visibility: hidden;
  position: relative;
  width: 100%;
  overflow: hidden;
  &.move-right, &.move-left {
    min-height: 100%;
    -webkit-overflow-scrolling: touch;
  }
}

.inner-wrap {
  position: relative;
  width: 100%;
  -webkit-transition: -webkit-transform 500ms ease;
  -moz-transition: -moz-transform 500ms ease;
  -ms-transition: -ms-transform 500ms ease;
  -o-transition: -o-transform 500ms ease;
  transition: transform 500ms ease;
  &:before {
    content: " ";
    display: table;
  }
  &:after {
    content: " ";
    display: table;
    clear: both;
  }
}

.fixed {
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 99;
  left: 0;
}

.tab-bar {
  -webkit-backface-visibility: hidden;
  background: #333333;
  color: #FFFFFF;
  height: 2.8125rem;
  line-height: 2.8125rem;
  position: relative;
}

.tab-bar-section {
  height: 2.8125rem;
  padding: 0 0.625rem;
  position: absolute;
  text-align: center;
  top: 0;
  &.right {
    text-align: right;
    left: 2.8125rem;
    right: 0;
  }
}

.left-small {
  height: 2.8125rem;
  position: absolute;
  top: 0;
  width: 2.8125rem;
  left: 0;
}

.tab-bar .menu-icon {
  color: #FFFFFF;
  display: block;
  height: 2.8125rem;
  padding: 0;
  position: relative;
  text-indent: 2.1875rem;
  transform: translate3d(0, 0, 0);
  width: 2.8125rem;
  span::after {
    content: "";
    display: block;
    height: 0;
    position: absolute;
    top: 50%;
    margin-top: -0.5rem;
    left: 0.90625rem;
    box-shadow: 0 0 0 1px #FFFFFF, 0 7px 0 1px #FFFFFF, 0 14px 0 1px #FFFFFF;
    width: 1rem;
  }
}

.page{
  height:100%;
}

.android_fix{
  overflow: auto !important;
}

.safari_seven_fix {
  overflow: scroll !important;
}

.tab-custom-fix{
  font-size: 0.8rem !important;
  padding: 0.5rem 0rem 0.5rem 0.2rem !important;
}

.safari_login_fix {
  margin-top: 35px;
}

.android_menu_fix {
  position: relative;
}

.android_top_margin {
  margin-top: -35px;
}

.orbit-slide-number {
  display: none;
}

button, .button {
  -webkit-appearance: none;
  -moz-appearance: none;
  border-radius: 0;
  border-style: solid;
  border-width: 0;
  cursor: pointer;
  font-family: Roboto, "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-weight: normal;
  line-height: normal;
  margin: 0 0 1.25rem;
  position: relative;
  text-align: center;
  text-decoration: none;
  display: inline-block;
  padding: 1rem 2rem 1.0625rem 2rem;
  font-size: 1rem;
  background-color: #008CBA;
  border-color: #007095;
  color: #FFFFFF;
  transition: background-color 300ms ease-out;
}

button.tiny, .button.tiny {
  padding: 0.625rem 1.25rem 0.6875rem 1.25rem;
  font-size: 0.6875rem;
}

button.round, .button.round {
  border-radius: 1000px;
}

button.secondary, .button.secondary {
  background-color: #e7e7e7;
  border-color: #b9b9b9;
  color: #333333;
}

.button.secondary {
  &:hover, &:focus {
    background-color: #b9b9b9;
    color: #333333;
  }
}

.button-group {
  list-style: none;
  margin: 0;
  left: 0;
  &:before {
    content: " ";
    display: table;
  }
  &:after {
    content: " ";
    display: table;
    clear: both;
  }
  &.even-5 li {
    display: inline-block;
    margin: 0 -2px;
    width: 20%;
    > button, .button {
      border-left: 1px solid;
      border-color: rgba(255, 255, 255, 0.5);
    }
    &:first-child {
      button, .button {
        border-left: 0;
      }
    }
    button, .button {
      width: 100%;
    }
  }
  > li {
    display: inline-block;
    margin: 0 -2px;
    > button, .button {
      border-left: 1px solid;
      border-color: rgba(255, 255, 255, 0.5);
    }
    &:first-child {
      button, .button {
        border-left: 0;
      }
    }
  }
}

.column, .columns {
  padding-left: 0.9375rem;
  padding-right: 0.9375rem;
  width: 100%;
  float: left;
}

.column + .column:last-child, .columns + .column:last-child, .column + .columns:last-child, .columns + .columns:last-child {
  float: right;
}

@media only screen {
  .column, .columns {
    position: relative;
    padding-left: 0.9375rem;
    padding-right: 0.9375rem;
    float: left;
  }

  .hide-for-medium-up {
    display: inherit !important;
  }

  .show-for-medium-up {
    display: none !important;
  }

  .small-2 {
    width: 16.66667%;
  }
  .small-3 {
    width: 25%;
  }
  .small-4 {
    width: 33.33333%;
  }
}

@media only screen and (min-width: 30em) {
  .medium-1 {
    width: 8.33333%;
  }
  .medium-2 {
    width: 16.66667%;
  }
  .medium-3 {
    width: 25%;
  }
}
