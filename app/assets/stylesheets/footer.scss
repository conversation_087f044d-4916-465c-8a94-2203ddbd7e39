body.modal-open{
  overflow: hidden;
  position: fixed;
  font-family: "Inter", sans-serif !important;
}
#mobile_footer{

  padding-top:19px;
  padding-bottom:1px;
  background-color: #0C0C0C;
  margin-top: 35px;
  z-index: 1;
  box-shadow: 0 2px 34px -4px rgba(0, 0, 0, 0.7);

  .row div{
    text-align: center;
    margin:0 auto;
  }

  img{
    display: inline-block !important;
  }

  span{
    color: #9C9B9B;
    font-size: 10px;
    margin-top: 5px;
    line-height: 15px;
  }

  #footer_nav{
    border-top: 1px solid #797979;
    margin-top: 12px;
    margin-bottom: 12px;
    margin-left:0px;
    .phoneNumber{
      padding: 0px; 
      font-size: 12px;
    }
  }

  #footer_nav li{
    float: left;
    text-align: center;
    background-color: #504F4F;
    padding: 5px;
    width: 100%;
    border-right: 1px solid #777575;
    list-style: none;
    font-size: 14px;
    color: $text_white;
  }

  #footer_nav a{
    color: #C2C2C2 !important;
    text-decoration: none;
    font-size: 12px;
  }

  #footer_nav li:first-child{
    border-right:0px;
    padding-bottom: 0px; 
  }

  .expand ul{
    display: block !important;
  }

  .f_free,
  .f_world_wide,
  .f_contact,
  .f_money_back
  {
    display: inline-block;
    margin: 0 !important;
    width: 35px;
    height: 35px;
    background: image-url('sprite.png') no-repeat;
  }

  .f_free{
    background-position: -108px -359px;
  }

  .f_world_wide{
    background-position: -162px -359px;
  }

  .f_contact{
    background-position: -3px -359px;
  }

  .f_money_back{
    background-position: -56px -359px;
  }

  .visible-xs {
    display: block !important;
  }


  .nopadding{
    padding-left:0px;
    padding-right:0px;
  }

  .col-xs-3 {
    width: 25%;
    float: left;
  }

  .row {
    margin-left: 0px;
    margin-right: 0px;
    max-width: 100%;
  }

  .col-xs-4 {
    width: 33.3333%;
    float: left;
  }

  .copyright {
    text-align: center;
    padding-right: 0px;
    text-align: center;
    margin-bottom: 15px;
    padding-left: 0px;
  }

  .copyright p{
    font-size: 1em;
    line-height: 18px;
    color: #9C9C9C;
    padding-bottom: 1px;
    line-height: 17px;
    font-size: 13px;
  }
  .clr {
    clear: both;
  }

  .sign_in_button a{
    color: rgb(181, 252, 181);
  }
}

body .opera_footer_fix {
  position: relative;
  bottom: 0;
  width: 100%;
  z-index: 9999;
  margin-top: 12px;
}

body .opera_footer_fix a {
  margin-bottom: 0em;
  width: 100%;
  left: 0px;
  font-weight: bold;
  line-height: inherit;
}

/***Mobile subscribe window***/
.modal-top{
  margin-top: 6px !important;
}
.modal-tr-bg{
  background: rgba(0, 0, 0, 0.45) !important;
}
#mobile-subscribe-window{
  overflow-y: scroll;
  position: fixed;
  top: 6px;
  width: 95%;
  margin: 0% 0% 0% 2.5%;
  border: none;
  box-shadow: none;
  border-radius: 0px;
  background: transparent;
  z-index: 9999999999 !important;
  #modal-subscribe-box{
    height: 85%;
    overflow-y: scroll;
  }
  .modal-footer{
    #subscribe-input{
      font-size: 18px;
      margin: 5px 0px;
      border: 1px solid #afacac;
      height: 38px;
      background: #efefef;
      text-align: center;
      &::-webkit-input-placeholder { /* Chrome/Opera/Safari */
        color: grey;
      }
      &::-moz-placeholder { /* Firefox 19+ */
        color: grey;
      }
      &:-ms-input-placeholder { /* IE 10+ */
        color: grey;
      }
      &:-moz-placeholder { /* Firefox 18- */
        color: grey;
      }
    }
    #email-subscribe-button{
      margin: 5px 0px;
      box-shadow: 2px 2px 3px 0px grey;
      width: 100%;
      height: 40px;
      font-size: 18px;
    }
    #email-cancel-button{
      border: 1px solid black;
      width: 100%;
      height: 32px;
      margin: 5px 0px;
      color: black;
      font-size: 14px;
    }
  }
  input{
    border-radius: 3px;
    height: 55px;
    background: #424040;
    outline: medium none;
    border: medium none;
    font-size: 12px;
    color: rgb(255, 255, 255);
  }
  label{
    font-size: 19px;
    text-align: center;
  }
}

#newsletter-sub-image {
  width: 100%;
}

@media only screen and (min-width: 30em) {
  #mobile-subscribe-window{
    margin: 20px auto;
    width: 45%;
  }
  #newsletter-sub-image {
    height: 180px !important;
  }
  @media only screen and (min-height: 20em) {
    #newsletter-sub-image {
      height: 240px !important;
    }
  }
}

@media only screen and (min-width: 60em) {
  #mobile-subscribe-window{
    margin: 20px auto;
    width: 25%;
  }
  #newsletter-sub-image {
    height: 400px !important;
  }
}
.subscribe_text_message{
  text-align: center;
  margin-top: -3px;
  color: rgb(68, 68, 68);
}

.close_subscribe{
  width: 24px;
  font-size: 28px;
  text-align: center;
  color: #BA4800 !important;
}

#email_subscribe_button{
  width: 78px !important;
  float: right;
  margin-top: -55px !important;
  border: medium none;
  height: 55px !important;
  border-radius: 0px 2px 2px 0px !important;
  color: rgb(255, 255, 255) !important;
  position: absolute;
  right: 4px;
  background: rgb(222, 100, 24) none repeat scroll 0% 0% !important;
  font-size: 18px !important;
}

#subscribe_input{
  font-size: 16px !important;
  margin-bottom: 0px;
  &:focus{
    box-shadow: none;
    border-bottom: none;
  }
}

.transparent_bg {
  background: rgba(0, 0, 0, 0.45) !important;
}

.subscribe_success{
  font-size: 19px !important;
  margin-top:12px !important;
}
.feedbackDiv{
  background-color: white;
}

@media only screen and (min-width: 520px) {
  #notice_banner{
    margin: 0 auto;
    width: 360px;
  }
}
#notice_banner{
  display: none;
  background-color: #d84646;
  padding: 6px;
  margin-bottom: 10px;
  color: black;
  font-size: medium;
  text-align: center;

  .rounded_block{
    border-radius: 5px;
    background-color: #fdc5c6;
    padding: 1px 5px 6px;
  }
  .notice_header{
    padding-bottom: 1px;
    .header_text{
      font-size: 1rem;
    }
    .header_sub_text{
      font-size: 0.8rem;
    }
    #notice_close_btn{
      color: #9b0000;
      float:right;
    }
  }
  .notice_body{
    text-align: center;

    .coupon_cutout{
      border-style: dashed;
      display: inline-block;
      border-radius: 5px;
      border-width: 1.6px;
      padding: 3px 15px;

      .coupon_sub_text{
        font-size: 0.5rem;
        font-weight: 600;
      }
      .coupon_code{
        color: #9b0000;
        font-size: 1.4rem;
        font-weight: 600;
        line-height: 1;
      }
    }

    .coupon_message{
      display: inline-block;
      padding: 2px;
      text-align: left;

      .offer_sub_msg{
        font-size: 10px;
      }
      .offer_msg{
        font-size: 12px;
        font-weight: 600;
      }
    }
  }
}
