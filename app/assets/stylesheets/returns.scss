@import 'variables';
@import 'white_theme';
body {
  font-family: "Inter", sans-serif !important;
}
table tr {
	background: none !important;
}
table.table-borderless  {
  background: none;
  border: none;
}
.table-borderless td,
.table-borderless th {
  border: 0 !important;
  background: none;
  color: $text_black;
}

body.modal-open{
  overflow: hidden;
  position: fixed;
}

.order_return_links {
  background-color: $snow_white;
  &.active {
    background-color: $body_white;
  }
  a, a:focus, a:hover{
    color: $text_black;
  }
}

.reveal-modal {
  overflow-y: auto;
  height: 100%;
  position: fixed;
  top: 20px !important;

  .close-reveal-modal {
    color: $text_black;
  }
}
.cancel_modal{
  background-color:#191919;
  padding-top: 50px;
  min-height: 0 !important;
  height: 250px;
  top: 35% !important;
  #cancellation_message{
    font-size: 1.3em;
  }
  .close_cross{
    padding: 1% 5% 1% 7%;
    right: 0.3rem;
  }
  .footer
  {
    margin-top:40px;
    float:right;
  }
}

.user_return_panel {
  .modal-content {
    margin: auto;
    display: block;
    width: 80%;
    max-width: 100%;
    font-size: 12px;
    -webkit-animation-name: zoom;
    -webkit-animation-duration: 0.6s;
    animation-name: zoom;
    animation-duration: 0.6s;
  }

  @-webkit-keyframes zoom {
    from {transform:scale(0)} 
    to {transform:scale(1)}
  }

  @keyframes zoom {
    from {transform:scale(0)} 
    to {transform:scale(1)}
  }

  .img-check{
    cursor:pointer;
  }
  .check{
    opacity:0.3;
    color:#428bca;
  }

  #new_return b {
    font-size: 12px;
    color: $text_black;
  }

  .done label:after{
    content:'\2713';
    display:inline-block;
    color:#43AC6A;
    padding:0 6px 0 0;
    font-size: x-large;
    font-weight: bolder;
    position: relative;
    top: -6.5em;
    left: 2.5em;
  }
  .designer_well {
    padding: 5px;
    margin: 5px 5px 5px 5px;
    background-color: $body_white;
    border-radius: 4px;
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, .05);
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, .05);
    border: none;

    table {
      background: $snow_white;
      box-shadow: $card_box_shadow;
      td {
        text-align: center;
        color: $text_black;
      }
    }

    .small-5, .small-2 {
      padding: 10px;
      font-size: large;
    }
  }
  .well-legend {
    font-size: 14px;
    text-align:center;
    width: auto;
    padding: 5px 20px 5px 20px;
    margin-bottom: 20px;
    /*background: #010710;*/
    border: 1px solid #e3e3e3;
    border-radius: 2px;
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, .05);
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, .05);
  }

  .item_block {
    /*border: 0.1em solid #4d4d4d;*/
    margin-bottom: 1em;
    padding: 0.5em 0em;
    box-shadow: $card_box_shadow;
    background-color: $body_white;
    margin-left: 0;
    margin-right: 0;
    padding-left: 1em;

    .columns {
      padding-left: 0.4em;
    }
  }

  .status_notice {
    background-color: #E8C55B;
    border: 2px solid #D4A22E;
    border-radius: 5px;
    margin-top: 2%;
    height: auto;
    padding: 8px 15px;
    color: black;
    font-size: small;
  }

  .notice_div {
    color:orange !important;
    padding: 0 10px 5px 10px;
    line-height:120%;
    font-size:small;
  }

  #return_policy{
    background-color: $snow_white;
    color: $text_black;
    display: none;
    padding: 10px;
    text-align: justify;
    ul{
      li{
        margin-top: 10px;
        text-align: justify;
      }
    }
  }

  .account_details:hover {
    cursor:pointer;
  }

  #bank_detail, #paypal_detail {
    label {
      color: white
    }
  }

  .return_block {
    background: $snow_white;
    box-shadow: $card_box_shadow;
    margin-bottom: 2em;

    .return_desc {
      font-size: small;
      color: $text_black;
    }

    hr {
      border-color: #848080;
    }
  }

  .return_block:hover {
    cursor: pointer;
  }

  .listing_panel_block{
    background: $snow_white;
    color: $text_black;
    box-shadow: $card_box_shadow;

    .expand{
      display: none;
      float: right;
    }

    .collapse{
      float: right;
    }

    p {
      padding: 10px;
      margin-bottom: 0;
    }

    a {
      color:$text_black;
      &:hover, &:focus{
        color: $text_black;
      }
    }
  }

  .panel_footer, .panel_footer p {
    font-size:small;
    color:$text_black;
  }
}

@media only screen and (max-width: 40em) {
  .orbit-container{
    .orbit-next, .orbit-prev {
      display: block !important; 
    }
  }
}

@media only screen and (min-width: 30em) {
  .reveal-modal {
    top: 12rem !important;
  }
  .user_return_panel.returns{
    margin-top:6rem;
  }
}
@media only screen and (min-width: 1024px) {
  .user_return_panel.returns{
    margin-top: 17em !important;
  }
}
@media screen and (min-width: 1024px) and (max-width: 1300px) {
  .user_return_panel{
    margin-top:12rem !important;
  }
}