//importing variables
@import 'variables_red';
@import 'common_desk';

//@import 'fonts';
body {
  font-family: "Inter", sans-serif !important;
}

html {
  height: 100%;
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
}

* {
  -webkit-box-sizing: inherit;
  -moz-box-sizing: inherit;
  box-sizing: inherit;

  &:before,
  &:after {
    -webkit-box-sizing: inherit;
    -moz-box-sizing: inherit;
    box-sizing: inherit;
  }
}

body,
html {
  height: 100%;
  width: 100%;
}

html {
  font-size: 100%;
}

body {
  cursor: auto;
  font-style: normal;
  font-weight: normal;
  line-height: 1.5;
  margin: 0;
  padding: 0;
  position: relative;
  overflow-x: hidden !important;
}

body.branch-banner-is-active {
  margin-top: 0 !important;
}

::selection {
  background-color: $light_red;
  color: $text_white;
}

a {
  line-height: inherit;
  text-decoration: none;

  img {
    border: none;
  }
}

.no-scroll-background {
  overflow: hidden !important;
}

/* The typing effect */
@keyframes typing {
  from {
    width: 0
  }

  to {
    width: 100%
  }
}

#menu-side-nav {
  display: none;
}

.pages_home {
  .off-canvas-wrap {
    #container {
      width: 100%;
      margin: 0;
      background: #f4f4f4;
    }
  }
}

.off-canvas-wrap {
  .inner-wrap {
    .fixed {
      box-shadow: 0 -1px 15px 0 rgba(0, 0, 0, 0.2);
    }

    nav.tab-bar {
      .cart-icon {
        position: relative;
        background-image: image-url('bagicon.png');
        background-size: 1.4rem;
        background-repeat: no-repeat;
        display: inline-block;
        width: 1.4em;
        height: 2em;
        vertical-align: sub;
        margin-top: 0.2rem;
      }

      .cart_count {
        display: flex;
        justify-content: center;
        align-items: center;
        position: absolute;
        bottom: 17px;
        top: 10px;
        right: 14px;
        border-radius: 50%;
        padding: 3px;
        background: #670B26;
        color: white;
        text-align: center;
        font-size: 1rem;
        transform: translate(50%, -50%);
      }

      .user-wishlist {
        font-size: 1.8rem;
        margin-right: -0.7rem;
        margin-top: 0.3rem;

        .fi-heart {
          vertical-align: middle;
        }
      }

      .logo {
        background-image: image-url('logo-white1.png');
        background-repeat: no-repeat;
        display: inline-block;
        width: 5.5em;
        height: 2.5em;
        vertical-align: middle;
        background-size: 5.5em 2.5em;
        margin-left: 0.9rem;
      }

      .download-app {
        color: #5e1c1b;
        font-size: $small_font;
        margin-left: 3px;
        margin-top: 4px;
        margin-right: 8px;
      }

      .app-download {
        background: image-url('downloadicon.png');
        background-size: 25px;
        width: 23px;
        height: 22px;
        background-repeat: no-repeat;
        margin: 9px 1px 1px 1px;
      }
    }

    div.tab-bar,
    .searchbar-icon-wrapper {
      header.scroll {
        padding: 0.4rem;
        padding-bottom: 0.1rem;
        width: 98%;
        white-space: nowrap;
        overflow-x: scroll;
        overflow-y: hidden;
        -webkit-overflow-scrolling: touch;
        -ms-overflow-style: -ms-autohiding-scrollbar;

        nav {
          margin: -15px 0px -6px 0px;

          i.fi-burst-new {
            font-size: 22px;
            vertical-align: middle;
            margin: -4px 0px;
            color: #de6b23;
            position: absolute;
          }
        }
      }

      .trending-results-box {
        background: white;
        display: none;
        padding: 0px 15px 2px 15px;
        border-radius: 0px 0px 2px 2px;
        text-align: left;
        z-index: 2 !important;
        position: absolute;
        width: inherit;

        .trending-results-text {
          border-bottom: $border_black;
          color: #272626;
          font-weight: 600;

          .trending-icon {
            color: #292929;
          }
        }

        ul#trending-results {
          margin: 0px;

          li {
            list-style-type: none;
            font-size: 16px;
            text-transform: capitalize;
            padding: 10px 0px;

            a {
              color: #271f35;
            }
          }
        }
      }

      .show-for-small-up.search_margin,
      .search_margin {
        width: 96%;
        margin: -3px auto;
        display: none;
        margin-top: 2px;

        input[type='text'] {
          padding-left: 18px;
          margin-bottom: 0px;
          border-bottom: none;
          background-color: white;

          &:focus {
            box-shadow: none;
            border-bottom: none;
          }

          &::-webkit-input-placeholder {
            /* Chrome/Opera/Safari */
            color: rgba(39, 31, 53, 0.67);
            font-size: 15px;
          }

          &::-moz-placeholder {
            /* Firefox 19+ */
            color: rgba(39, 31, 53, 0.67);
            font-size: 15px;
          }

          &:-ms-input-placeholder {
            /* IE 10+ */
            color: rgba(39, 31, 53, 0.67);
            font-size: 15px;
          }

          &:-moz-placeholder {
            /* Firefox 18- */
            color: rgba(39, 31, 53, 0.67);
            font-size: 15px;
          }
        }

        button.submit_btn {
          padding: 0px;
          margin-bottom: 0px;
        }
      }

      .scroll-button {
        position: absolute;
        font-size: 20px;
        line-height: 31px;
        background-color: $text_black;
        color: $text_white;
        text-align: center;
        height: 33px;
      }

      #scroll-btn-right {
        display: none;
        top: 0px;
        z-index: 1;
        width: 8%;

        &:after {
          content: '';
          width: 10px;
          height: 10px;
          position: absolute;
          transform: rotate(-45deg);
          border-left: 1px solid white;
          border-top: 1px solid white;
          top: 10px;
          left: 10px;
        }
      }

      #scroll-btn-left {
        top: 0px;
        right: 1px;

        &:after {
          content: '';
          width: 10px;
          height: 10px;
          position: absolute;
          transform: rotate(-45deg);
          border-right: 1px solid white;
          border-bottom: 1px solid white;
          top: 10px;
          right: 10px;
        }
      }

      .search-css {
        border-radius: 0.4rem;
        border: 2px solid #d8d8d8;
        margin: 0px 9px 6px 9px;
        max-width: 67.5rem;
        width: 95%;
      }

      .tab-fix {
        font-size: 0.9rem !important;
        padding: 0.3rem !important;
        width: fill-content;
        border-radius: 6px;
        color: $text_black;
        background-color: white;
        margin: 0.05rem;
        margin-bottom: 0px !important;
        letter-spacing: 1px;
        border: 1px solid #d8d8d8;
      }

      .tab-fix:hover,
      .tab-fix:focus {
        background-color: #e7e7e7;
        border-color: #b9b9b9;
      }

      .medium-tab-button {
        width: 100%;
      }
    }

    .search_margin {
      input[type='text'] {
        color: #383737;
        padding-left: 18px;
        margin-bottom: 0px;
        border-bottom: none;
        background-color: rgba(252, 237, 240, 0.1);
      }
    }

  }

  .submit_btn {
    background-color: transparent;
    border-color: transparent;

    i.fi-magnifying-glass {
      color: #681e19;
      font-size: 18px;
    }
  }

  i.fi-download {
    color: $text_black;
    font-size: 18px;
  }

  .main-section {
    overflow: visible;
    overflow: visible;
    position: relative;
    -webkit-overflow-scrolling: touch;
  }

  .no-search-bar {
    margin-top: 4rem;
  }

  .with-menu {
    margin-top: 8.3rem;
  }

  #main-section {
    width: 100%;
    height: 99%;
    overflow: visible;
  }

  ul.off-canvas-list li label {
    padding: 0.4em 0.6em;
  }

  .left-submenu .back>a {
    padding: 0.1em 0.6em;
  }

  #container {
    margin: 1%;
    overflow: visible;
  }

  .left-off-canvas-menu,
  .right-off-canvas-menu {
    min-height: 200vh;
  }
}

.off-canvas-wrap {
  position: relative;
  width: 100%;
  overflow: visible !important;
}

@media only screen and (min-width: 400px) {
  .static-nav-bar {
    width: 80% !important;
  }
}

@media only screen and (min-width: 300px) and (max-width: 400px) {
  .static-nav-bar {
    width: 78% !important;
  }
}

@media only screen and (min-width: 200px) and (max-width: 300px) {
  .static-nav-bar {
    width: 75% !important;
  }
}

section.main-section {
  display: none;
}

@media only screen and (min-width: 320px) and (max-width: 568px) {
  .download-app {
    font-size: 1rem !important;
  }
}

@media only screen and (min-width: 64.063em) {
  .show-for-small-up.search_margin {
    width: 40% !important;
    margin-top: -83px !important;
  }

  div.search-tab {
    height: 2.3125rem !important;

    .trending-results-box {
      position: fixed;
      width: 40%;
    }
  }
}

.left {
  float: left !important;
}

.right {
  float: right !important;
}

.off-canvas-wrap {
  -webkit-backface-visibility: hidden;
  position: relative;
  width: 100%;
  overflow: visible;

  &.move-right,
  &.move-left,
  &.offcanvas-overlap {
    min-height: 100%;
    -webkit-overflow-scrolling: touch;
  }
}

.inner-wrap {
  position: relative;
  width: 100%;
  -webkit-transition: -webkit-transform 500ms ease;
  -moz-transition: -moz-transform 500ms ease;
  -ms-transition: -ms-transform 500ms ease;
  -o-transition: -o-transform 500ms ease;
  transition: transform 500ms ease;

  &:before {
    content: " ";
    display: table;
  }

  &:after {
    content: " ";
    display: table;
    clear: both;
  }
}

div#carts_block {
  overflow: visible;
}

.border-top {
  box-shadow: 0px 1px 8px 0px rgba(0, 0, 0, 0.15);
  border-top: 0.3125rem solid $dark_red;
  background: white;
}

.fixed-header {
  display: none;
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 9;
  left: 0;
  box-shadow: 0 0 5px rgba(0, 0, 0, .35);
  -webkit-transform: translateY(-50px);
  transform: translateY(-50px);
  will-change: transform;
  -webkit-transition: -webkit-transform .25s ease-out;
  transition: -webkit-transform .25s ease-out;
  transition: transform .25s ease-out;
  transition: transform .25s ease-out, -webkit-transform .25s ease-out;
}

.fixed {
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 99;
  left: 0;
}

.tab-bar {
  -webkit-backface-visibility: hidden;
  background: white !important;
  color: $text_black;
  height: 2rem;
  line-height: 2rem;
  position: relative;
  margin: 0.625rem 0px;
}

.search-tab {
  line-height: 3.3rem !important;
}

.tab-bar-section {
  height: 2rem;
  padding: 0 0.375rem !important;
  position: absolute;
  text-align: center;
  top: 0;

  &.right {
    text-align: right;
    left: 2rem;
    right: 0;
  }
}

.left-small {
  height: 2rem;
  position: absolute;
  top: 0;
  width: 2rem;
  left: 10px !important;
}

.noleftpadding {
  padding-left: 0px !important;
}

.nopadding {
  padding: 0px !important;
}

.tab-bar .menu-icon {
  color: $text_white;
  display: block;
  height: 2rem;
  padding: 0;
  position: relative;
  text-indent: 2.1875rem;
  transform: translate3d(0, 0, 0);
  width: 2rem;

  span::after {
    content: "";
    display: block;
    height: 0;
    position: absolute;
    top: 50%;
    margin-top: -0.5rem;
    left: 0.5rem;
    box-shadow: 0 4px 0 1px #681e19, 0 9px 0 1px #681e19, 0 14px 0 1px #681e19 !important;
    width: 1rem;
  }
}

.static-tab-bar {
  .search-tab {
    width: 100%;
    position: absolute;
    margin: 0;
  }

  .show-for-small-up {
    margin-top: -0.15em !important;
    margin-bottom: 0 !important;
    margin-left: 0.8rem !important;
  }
}

.page {
  height: 100%;
}

.android_fix {
  overflow: auto !important;
}

.safari_seven_fix {
  overflow: scroll !important;
}

.tab-custom-fix {
  font-size: $menu_font_size !important;
  padding: 0.5rem 0rem 0.5rem 0.2rem !important;
}

.safari_login_fix {
  margin-top: 35px;
}

.android_menu_fix {
  position: relative;
}

.android_top_margin {
  margin-top: -35px;
}

.orbit-slide-number {
  display: none;
}

.order_return_links {
  color: black
}

.order_return_links.active {
  color: white;
}

button,
.button {
  -webkit-appearance: none;
  -moz-appearance: none;
  border-radius: 0;
  border-style: solid;
  border-width: 0;
  cursor: pointer;
  font-weight: normal;
  line-height: normal;
  margin: 0 0 0.5rem;
  position: relative;
  text-align: center;
  text-decoration: none;
  display: inline-block;
  padding: 1rem 2rem 1.0625rem 2rem;
  font-size: 1rem;
  background-color: $dark_red;
  border-color: $dark_red;
  color: #FFFFFF;
  transition: background-color 300ms ease-out;
}

button.tiny,
.button.tiny {
  padding: 0.625rem 1.25rem 0.6875rem 1.25rem;
  font-size: $font_size;
}

button.round,
.button.round {
  border-radius: 1000px;
}

button.secondary,
.button.secondary {
  background-color: #e7e7e7;
  border-color: #b9b9b9;
  color: #333333;
}

.button.secondary {

  &:hover,
  &:focus {
    background-color: #b9b9b9;
    color: #333333;
  }
}

.button-group {
  list-style: none;
  margin: 0;
  left: 0;
  border: 1px solid #d8d8d8;

  &:before {
    content: " ";
    display: table;
  }

  &:after {
    content: " ";
    display: table;
    clear: both;
  }

  &.even-5 li {
    display: inline-block;
    margin: 0 -2px;
    width: 20%;

    >button,
    .button {
      border-left: 1px solid;
      border-color: rgba(255, 255, 255, 0.5);
    }

    &:first-child {

      button,
      .button {
        border-left: 0;
      }
    }

    button,
    .button {
      width: 100%;
    }
  }

  >li {
    display: inline-block;
    margin: 0 -2px;

    >button,
    .button {
      border-left: 1px solid;
      border-color: rgba(255, 255, 255, 0.5);
    }

    &:first-child {

      button,
      .button {
        border-left: 0;
      }
    }
  }
}

.column,
.columns {
  width: 100%;
  float: left;
}

.column+.column:last-child,
.columns+.column:last-child,
.column+.columns:last-child,
.columns+.columns:last-child {
  float: right;
}

.back-button {
  height: 2rem;
  position: absolute;
  top: 0;
  width: 2rem;
  left: 20px !important;
  color: $dark_red !important;
  z-index: 1;

  .left-arrow {
    border: solid $dark_red;
    border-width: 0 3px 3px 0;
    display: inline-block;
    padding: 3px;
    transform: rotate(135deg);
    -webkit-transform: rotate(135deg);
  }
}

.desk_web {
  .search_margin {
    display: inline-block;

    .search-css {
      height: 40px;
    }
  }
}

.desk_web {
  display: none;
}

@media only screen and (min-width: 1024px) {
  .desk_web {
    display: block;
  }

  .m_web {
    display: none;
  }
}

@media only screen {

  .column,
  .columns {
    position: relative;
    float: left;
  }

  .hide-for-medium-up {
    display: inherit !important;
  }

  .show-for-medium-up {
    display: none !important;
  }

  .small-2 {
    width: 16.66667%;
  }

  .small-3 {
    width: 25%;
  }

  .small-4 {
    width: 33.33333%;
  }

  .small-5 {
    width: 41.66667%;
  }
}

@media only screen and (min-width: 30em) {
  .medium-1 {
    width: 8.33333%;
  }

  .medium-2 {
    width: 16.66667%;
  }

  .medium-3 {
    width: 25%;
  }

  .order_return_links {
    height: 4rem;
    display: flex;
    justify-content: center;
    align-items: center;
    color: black
  }

  .order_return_links.active {
    color: white;
  }
}

@media screen and (min-width: 1023px) and (max-width: 1299px) {
  .header-mega-menu-wrapper {
    .nav-link-wrap {
      .action-link {
        padding: 9px 4px 10px 4px;
      }
    }
  }
}



.cc-window {
  box-shadow: 0 -1px 15px 0 rgba(0, 0, 0, 0.64);
}

.pagination_orders_returns {
  text-align: center;
  margin: 1rem 0;

  .pagination.prev,
  .pagination.next {
    display: inline-flex;
    border-color: #585858;
    cursor: pointer;
  }

  .first {
    float: left;
  }

  .last {
    float: right;
  }

  .prev {
    margin-right: 8px;
    border: 0px !important;

    &:hover {
      background: none !important;
    }

    &:before {
      content: "";
      height: 10px;
      width: 10px;
      -webkit-transform: rotate(-45deg);
      -moz-transform: rotate(-45deg);
      -ms-transform: rotate(-45deg);
      -o-transform: rotate(-45deg);
      transform: rotate(-45deg);
      margin-top: 4px;
      margin-right: 5px;
    }
  }

  .next {
    margin-left: 8px;
    border: 0px !important;

    &:hover {
      background: none !important;
    }

    &:after {
      content: "";
      height: 10px;
      width: 10px;
      -webkit-transform: rotate(45deg);
      -moz-transform: rotate(45deg);
      -ms-transform: rotate(45deg);
      -o-transform: rotate(45deg);
      transform: rotate(45deg);
      margin-top: 4px;
      margin-left: 5px;
    }
  }

  .current {
    background-color: #670b19;
    color: #fff !important;
    border: 1px solid;
    border-color: #670b19;
    font-style: normal;
    font-weight: 700;
    padding: 10px 11px;
    border-radius: 100px;
    margin: 0.5rem;
  }

  span.gap {
    margin: 0px 5px;
  }

  a {
    cursor: pointer;
    font-size: 15px;
    border-radius: 100px;
    margin: 0;
    padding: 9px 11px 9px 12px;
    border: 1px solid transparent;
  }

  a:hover {
    background-color: transparent;
    color: #670b19 !important;
    border: 1px solid #670b19;
    border-radius: 100px;
  }

  a:focus {
    color: #670b19 !important;
  }
}

.all-orders,
.all-returns {

  a:hover,
  a:focus {
    color: #670b19 !important;
  }
}

.logo-action-text {
  font-family: "Inter", sans-serif;
  color: black;
  font-size: 12px;
  position: relative;
  bottom: 4px;
}

.profile-action-wrap,
.logo-wrap {
  display: flex !important;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.logo-wrap svg {
  height: 20px;
}

.logo-icon {
  text-align: center;
}

@media screen and (max-width: 1025px) {
  .right.medium-1.small-1.columns.user-wishlist.nopadding.user-wishlist-icon {
    margin-top: 4px !important;
    width: 6.666%;
  }
}

@media screen and (min-width: 767px) and (max-width: 1024px) {
  .user-cart-icon {
    width: 10% !important;
  }
}

@media screen and (max-width: 766px) {
  .user-cart-icon {
    width: 16.66667% !important;
  }
}

@media screen and (max-width: 280px) {
  .off-canvas-wrap .inner-wrap nav.tab-bar .user-wishlist {
    margin-right: 0.3rem;
  }
}

.off-canvas-wrap #container {
  overflow: visible;
}

@media screen and (max-width: 480px) {

  .prev a,
  .first a,
  .last a,
  .next a {
    margin: 0;
    padding-right: 3px;
    padding-left: 3px;
  }
}