body {
  font-family: "Inter", sans-serif !important;
}
.off-canvas-wrap {
  .inner-wrap {
    nav.tab-bar {
      .cart-icon{
        background-image: image-url('cart-64.png');
      }
      .logo{
        background-image: image-url('logo-white.png');
      }
    }
  }
}

section.main-section{
  display: block;
}

sup.menu-tag{
  color: #ffffff;
  padding: 3px 4px;
  font-size: 10px;
  background: #bf445e;
  background-size: 100% 100%;
  border-radius: 2px;
}
#menu-side-nav {
  height: 100%;
  width: 0px;
  position: fixed;
  z-index: 9999;
  top: 0;
  left: 0;
  background-color: $menu_background !important;
  overflow-x: hidden;
  overflow-y: scroll;
  display: block;

  .menu-accordion{
    margin-left: 0px;
    display: none;
    background-color: $menu_background !important;
    i.fi-burst-new, i.fi-burst-sale{
      font-size: 30px;
      color: #de6b23;
      vertical-align: middle;
    }
  }
  .has-submenu{
    padding : 0.5rem 1rem;
  }
  a{
    background: $menu_background !important;
    color: #dad9d9 !important;
  }
  a:hover{
    background: $menu_background !important;
    color: #dad9d9 !important;
  }
  .menu-content{
    background: $menu_background !important;
    padding: 0px;
    .border-custom{
      border-left: 1px dashed #262626;
    }
  }
  .all-menu{
    display: block;
    padding: 0.5rem 1rem;
    line-height: 14px;
  }
  .menus_static{
    list-style-type : none;
    font-size: 15px;
    border-left: 1px dashed #262626;
    padding-left: 8px;
    li{
      margin: 5px 20px 5px 0px;
      border-bottom: 1px solid #6d6d6d;
      padding-bottom: 8px;
    }
  }
  .submenu{
    font-size: 20px;
    margin-left: 12px;
  }
}

#branch-banner-iframe{
  top: 56px !important;
  z-index: 1 !important;
}
