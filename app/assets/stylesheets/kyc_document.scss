.user-document-card {
  max-width: 400px;
  margin: 2rem auto;
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  font-family: 'Inter', sans-serif;
  text-align: center;
}

.user-document-header {
  background: linear-gradient(135deg,#a83232, #5c1212);
  color: #fff;
  padding: 1.5rem 1rem;

  h2 {
    color: white;
    font-size: 1.3rem;
    font-weight: 600;
    margin: 0.3rem 0;
  }
  .secure-text {
    font-size: 0.85rem;
    opacity: 0.85;
  }
}

.user-document-body {
  padding: 1.5rem;

  h3 {
    font-size: 1rem;
    font-weight: 600;
    color: #7a1c1c;
    margin-bottom: 0.3rem;
  }

  .info-text {
    font-size: 0.9rem;
    color: #555;
    margin-bottom: 1rem;
  }

  .upload-box {
    position: relative;
    border: 2px dashed #d3b1b1;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    background: #fff5f5;
    transition: border 0.3s;

    input[type="file"] {
      opacity: 0;
      position: absolute;
      width: 100%;
      height: 100%;
      left: 0;
      top: 0;
    }

    &:hover {
      border-color: #7a1c1c;
    }

    .icon-upload {
      font-size: 2rem;
      color: #7a1c1c;
      margin-bottom: 0.5rem;
    }

    .upload-title {
      font-weight: 500;
      color: #7a1c1c;
    }

    .upload-sub {
      font-size: 0.8rem;
      color: #777;
      margin-top: 0.3rem;
    }
  }
}

.file-clear {
  position: absolute;
  top: 6px;
  right: 8px;
  font-size: 16px;
  color: #7a1c1c;
  background: #fff;
  border: 1px solid #d3b1b1;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  line-height: 18px;
  text-align: center;
  box-shadow: 0 1px 3px rgba(0,0,0,0.2);
  display: none;
  transition: background 0.2s, color 0.2s;
}

.file-clear:hover {
  background: #7a1c1c;
  color: #fff;
}

#file-error {
  display: none;
  background: #ffdddd;
  color: #a70000;
  border: 1px solid #ff5c5c;
  border-radius: 4px;
  padding: 6px 10px;
  margin-top: 0.7rem;
  font-size: 0.85rem;
  font-weight: 500;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-3px); }
  to { opacity: 1; transform: translateY(0); }
}

.submit-btn {
  background: #7a1c1c;
  color: #fff;
  border: none;
  width: 100%;
  padding: 0.8rem;
  border-radius: 6px;
  font-weight: 500;
  font-size: 0.95rem;
  cursor: pointer;
  transition: background 0.3s, transform 0.2s;

  &:hover {
    background: #5c1212;
  }

  &:active {
    transform: scale(0.97);
  }
}

.user-document-footer {
  border-top: 1px solid #eee;
  padding: 1rem;
  font-size: 0.85rem;
  background: #fff;
  text-align: center;

  .icon-lock {
    color: #7a1c1c;
    font-size: 1rem;
    margin-right: 4px;
  }

  .footer-box {
    display: inline-block;
    background: #f5f5f5;
    border-radius: 6px;
    padding: 0.5rem 0.8rem;
  }

  strong {
    color: #7a1c1c;
    margin-bottom: 0.3rem;
    display: block;
  }

  .footer-text {
    color: #666;
    font-size: 0.8rem;
  }
}
