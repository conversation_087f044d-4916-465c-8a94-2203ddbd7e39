// Place all the styles related to the Pages controller here.
// They will automatically be included in application.css.
// You can use Sass (SCSS) here: http://sass-lang.com/
@import 'variables_red';
@import 'red_theme';
$gray_colour:#eeeeee81;
$font_helpcenter: "Inter", sans-serif !important;
body {
  font-family: "Inter", sans-serif !important;
}
.pages_eid{
  #eid-container{
    .design-block{
      margin: 4px 0px;
    }
    .featured-products{
      border: 1px solid gray;
      .bestseller-title, .view-more{
        text-align: center;
      }
    }
    img{
      width: 100%;
    }
  }
}
@media only screen and (max-width: 620px) {
  .pages_eid{
    #eid-container{
      .columns{
        padding: 0px;
      }
      .pad-right{
        padding-right: 4px !important;
      }
      .pad-left{
        padding-left: 4px !important;
      }
      .row{
        .row{
          margin: 0 auto;
        }
      }
    }
  }
}
.pages_stitching_information{
  #container{
    margin-top: 1.8em;
  }
  .stitching_steps{
    background-color: $light_grey;
    font-size: $big_font;
    .title{
      font-size: 150%;
      text-align: center;
      color: $text_black;
    }
    .step_circle{
      display: inline-block;
      width:50px;
      height:50px;
      border-radius:100%;
      font-size:3%;
      line-height:50px;
      text-align:center;
      margin-left: 10%;
      background: $dark_red;
    }

    .step_text{
      color: $text_black;
      font: inherit;
    }
  }
  .size_image{
    .button{
      background-color: $dark_red;
      padding: 8px 12px;
      margin-bottom: 0px;
    }
  }

  .listing_panel_block{
    background: $light_grey;
    margin-bottom: 20px;

    a:hover, a:focus{
      color: $text_black;
    }

    li, label{
      cursor: auto;
    }

    table{
      border: none;
      color: $text_black;
    }
    p{
      text-align: left;
      background: none repeat scroll 0% 0%;
      margin: -7px 0px 0px;
      border-radius: 2px 2px 0px 0px;
      padding: 6px;
    }

    ul{
      font-size: 14px;
    }
    
    td{
      vertical-align: baseline;
    }
  }

  .accordion{
    table {
      display:none;
    }
    
    a {
      outline: none;
      color: inherit;
    }  

    .expand{
      display: none;
      float: right;
    }

    .collapse{
      float: right;
    }
  }

  .stitching_details{
    .panel_content{
      padding: 0px;
    }
    .stitching_question{
      color:$text_black;
      font-weight: bold;
    }

    .stitching_answer{
      text-align: justify;
      color: $text_black;
      font: inherit;

    }
  }

  .statement{
    border:1px solid;
    text-align:center; 
    color: $text_black;  
    font-size: 14px;
    height:24px;
    padding: 7px;
    margin: 0 auto;
    width: 80%;
    font-style: inherit;
    font-weight: bold;
  }

  .message{
    padding: 2px;
    text-align: center;
    background: $light_grey;
    color: $text_black;
    font-weight: bold;
  }
  .stitching_number{
    color: #e47450;
    font-weight: 400;
    text-align: left
  }
}

#contactUs, #emailUs{
  background-color:$body_white;
  min-height: 20vh;
  width: 95.8%;
  left: 10px;
  right: 10px;
  top: 50px;
  .close-reveal-modal{
    padding: 1% 5% 1% 7%;
    right: 0.3rem;
  }
  .closeEbtn{
    background: #D44F67;
    border: #D44F67;
    color:white;
    font-size: small;
    border-radius: 4%;
    margin: 1rem;
    margin-left: 5rem;
    padding: 1rem 2.1rem;
  }
  .submitEbtn{
    border-radius: 4%;
    background: $dark_red;
    border: $dark_red;
    color: white;
    padding: 1rem 2.1rem;
    margin: 1rem;
  }
  hr{
    margin: 0;
  }
}

#trackModal{
  background-color:$body_white;
  padding-top: 50px;
  min-height: 33vh;
  top: 50px;
  .close-reveal-modal{
    padding: 1% 5% 1% 7%;
    right: 0.3rem;
  }
  #order_number_field{
    margin-bottom: 22px;
  }
}

#list_of_order_modal{
overflow: auto;
position: fixed;
height: 100%;
background-color:$body_white;
top: 0px !important;
width: 100%;
.close-reveal-modal{
  padding: 1% 5% 1% 7%;
  right: 0.3rem;
  }
}

.pages_faq{
  .main-section{
    #container{
      .faq_main_div{
        font-size: 0.65em;
        .questions_ul{
          list-style: none;
          margin-left: 0px;
        }
        .question_li_content{
          h3{
            font-size: $big_font;
            &:before{
              content: "[+]  ";
            }
          }
        }
        .question_selected{
          h3{
            font-size: $big_font;
            &:before{
              content: "[-]  ";
            }
          }
        }
        #search_ques{
          font-size: 1.5em;
        }
        #search_answer{
          span{
            font-size: $big_font;
          }
        }
        .search_res{
          padding: 12px 8px;
          box-shadow: $card_box_shadow;
        }
        .faq_head{
          font-size: 1.5em;
          padding: 5px;
          margin-bottom: 10px;
        }
        .quick_links{
          text-align: center;
          .quick_link{
            height: 110px;
            border: 1px solid gray;
            h2{
              font-size: 1em;
            }
            }
          }
        .faqs_row{
          font-size: 1.2em;
          .faqs_all{
            margin-top: 30px;
            }
          .view_more{
            margin-left: -17px;
            background-color: #1D2A29;
            padding: 10px;
            border-radius: 12px;
            }
          }

        .faq_link_selected{
          color: $text_black;
          background-color: #d3b9be;
        }
        .faq_link_selected::before{
          content:"";
          position: absolute;
          right: 40%;
          top: 102px;
          width: 0;
          height: 0;
          border-left:8px solid transparent;
          border-bottom: 10px solid $body_white;
          border-right: 8px solid transparent;
          }
        .answer_selected{
          padding: 12px;
          box-shadow: $card_box_shadow;
          span{
            font-size: 0.9em !important;
          }
        }
        .navigation {
          height: 300px;
          overflow: auto;
        }
        hr{
          margin: 0.7rem 0 0.5rem 0;
        }
        .answer_nav{
          height: 200px;
          overflow: auto;
        }
      }
    }
  }
}

#container{
  margin-left: 0px !important;
  #help_center{
    font-family: $font_helpcenter;
    .accordion_title{
      // margin-left: -0.2em;
      background-color: #7e7e7e;
      border: solid;
      border: #7e7e7e;
      color: white;
    }
    .tab-title.active{
        a{
          background-color: $dark_red;
          border-bottom: none;
          color: white;
        }
      }

    .tab-title{
      width: 50%;
      a{
        padding: 0.5em;
        text-align: center;
      }
    }
    .tabs-content{
      background-color: white;
      width: 100%;
      padding: 5px;
      display: inline;
    }
    .accordion{
      margin-left: -0.3em;
    }
    #issue_panel{
      .columns{
        padding-left: 0px;
        padding-right: 0px;
      }
    }
    .category-links{
      height: 110px;
      border: 1px solid #6e6e6e;
      background: white;
      color: black;
      font-size: 4.5vw;
      width: 45%;
      margin-bottom: 3.7vw;
      margin-left: 3.7vw;
      text-align: center;
    }
    .content_replace{
      padding: 0.9375rem;
      display: block;
      color: black;
      background-color: #cacaca;
    }
    .answer_category_query{
      display: none;
      margin-left: 1.5rem;
      color: black;
      margin-bottom: 0.5rem;
    }
    .answers_category_query{
      display: none;
      margin-left: 1.5rem;
      color: black;
      margin-bottom: 0.5rem;
    }
    .category_query_level_two{
      margin-left: 0.7rem;
      color:black;
      list-style: none;
      display: none;
      hr{
        margin-top: 0.1rem ;
      }
    }
    .category_query{
      display: none;
      list-style: none;
      color: black;
      hr{
        margin: 0.1rem 0 1rem;
      }
    }
    .category_query:first-child{
      padding-top:1em;
    }
    .stage_passed{
      background-color: #009688;
    }
    .stage_passed:before{
      content: '\2713  '
    }
    .category_query:before{
      content: '\203A'
    }
    .category_query_level_two:before{
      content: '\203A'
    }
    .category_list{
      margin-left: 0;
    }
    .blur{
      opacity: 0.6;
    }
    .button.info{
      background-color: $dark_red !important;
      color: white;
    }
    #loadingImage{
      position: fixed;
      left: 45%;
      top: 30%;
      font-size: 4em;
    }
    #error_order_not_found_email{
      color: #D84315;
      padding: 3px;
    }
    .login_hr:after {
    content: "OR"; /* section sign */
    color: #999;
    display: inline; /* for vertical centering and background knockout */
    background-color: #cacaca; /* same as background color */
    padding: 0 0.5em; /* size of background color knockout */
    }
    .login_hr {
    font-family: Arial, sans-serif; /* choose the font you like */
    text-align: center; /* horizontal centering */
    line-height: 1px; /* vertical centering */
    height: 1px; /* gap between the lines */
    font-size: 1em; /* choose font size you like */
    border-width: 1px 0; /* top and bottom borders */
    border-style: solid;
    border-color: #676767;
    margin: 20px 10px; /* 20px space above/below, 10px left/right */

    /* ensure 1px gap between borders */
    -webkit-box-sizing: content-box;
    -moz-box-sizing: content-box;
    -ms-box-sizing: content-box;
    -o-box-sizing: content-box;
    box-sizing: content-box;
    }
    
    .track_btn{
      background-color: $dark_red;
      text-align: center;
      color: white;
      width: 65%;
      margin:auto;
      display: table;
      padding: 3px;
      border-radius: 5px;
    }
    #issue_text_help_center,#order_number_text,#email_id_help_center{
      color: black;
      &:hover {
          color:white;
        }
    }
    .helpcenter_qna{
      overflow: auto;
      display: none;
      background: $gray_colour;
      margin-left: 0.3em;
      border: 0.1px solid #6e6e6e;
      width: 99%;
      height: 350px;
      font-family: $font_helpcenter;
      .no_order_found{
        font-family: $font_helpcenter;
        margin: auto;
        font-size: 21px;
        margin-top: 150px;
        display: table;
      }
      .order_menu{
        margin-top: 0.5em;
        height: auto;
        width: 100%;
        background: white;
        border:0.8px solid black;
        line-height: 2;
        display: inline-flex;
        .order_info{
          width: 20%;
          text-align: left;
          margin: 5px;
          #product_no{
            font-size: 12px;
            font-weight: bolder;
          }
        }
        .product_detail{
          margin: 5px;
          text-align: left;
          width: 50%;
          font-size: 12px;
          #item_name{
            font-weight: bolder;
          }
          #item_total{
            font-size: 12px;
            font-weight: bolder;
          }
        }
        .product_status{
          margin-top: 10px;
          margin-right: 5px;
          font-size: 12px;
          text-align: center;
          width: 30%;
          #confirmed{
            font-weight: bolder;
            margin-top: 10px;
          }
          #status{
            padding: 0px 10px;
            border-color: $dark_red;
            font-weight: 400;
          }
        }
      }
    }
    .support_info{
      display: inline-flex;
      width: 100%;
      text-align: center;
      #contact_us, #email_us{
        background-color: $dark_red;
        border: 0;
        padding: 6px;
        border-radius: 6px;
        margin: 2rem;
        margin-top: 16px;
        margin-bottom: 10px;
        width: 30%;
        color: white;
      }
    }
    #category_title{
      color: white;
      background: $dark_red;
      border: 0.1px solid black;
      width:fit-content;
      border-left: 0;
      padding: 0.3rem 1rem;
      display: none;
    }
    .page_title{
      background-color: white;
      color: $dark_red;
      padding: 0.5rem 0.5rem 0.8rem 0.5rem;
      width: 100%;
      border-bottom: 2px solid $dark_red;
      text-align: center;
      display: inline-flex;
      #main{
        width: 87%;
        font-size: 24px;
        padding-left: 3rem;
        margin: 0;
      }
      #faq{
        width: 13%;
        padding: 6px;
        border-radius: 5%;
        text-align: center;
        font-size: 13px;
        font-weight: 1000;
        border: 1px solid;
      }
      #nav_arrow{
        display: none;
        width: 10%;
        color: $dark_red;
        font-size: 28px;
        padding: 0;
        margin: 0;
        border: 0.2px solid $dark_red;
        background: white;
      }
    }
    center{
    margin: 10px; 
    padding-top: 20px; 
    height: auto; 
    width: 97%; 
    line-height: 2;
    .login_req{
      width:50%; 
      margin-top: 20px;
      p{
        font-size:1.2em;
        color:black;
        }
      }
    }
    .step_image{
      margin-top: 1.5rem;
    }
  }
  #track_order{
    width:94%;
    margin:3.7vw; 
    background: #670e19;
    border-radius: 10px;
  }
  #faq_direct{
    a{
      border-radius: 10px;
      width:94%;
      margin:3.7vw; 
      background: #670e19;
    }
  }
}

table{
  width: 100%;
  text-align: center;
  background: unset;
  border:0;
  margin: 0;
  text-align: center;
  tr{
    td{
      border:0;
      background:white;
      line-height: 50px;
      font-weight: 500;
      font-size: 14px;
    }
  }
}

.privacy-text{
  text-align: center;
  width: 98%;
  line-height: 1.8rem;
}
.red-button {
  -webkit-appearance: none;
  -moz-appearance: none;
  font-weight: normal;
  background-color: $dark_red !important;
  border-color: $dark_red;
  color: #FFFFFF;
  transition: background-color 300ms ease-out;
}
.design-col2 {
  font-size: 12px !important;
  padding: 0;
}
@media only screen and (min-width: 1024px){
  #container #help_center .category-links{
    height: 170px;
    border: 1px solid #6e6e6e;
    background: white;
    color: black;
    font-size: 4.5vw;
    width: 40%;
    margin-bottom: 3.7vw;
    margin-left: 3.7vw;
    text-align: center;
  }
  #trackModal{
    top: 150px;
  }
}