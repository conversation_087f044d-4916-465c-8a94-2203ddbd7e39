@charset "UTF-8";
//importing variables
@import 'variables';
@import 'white_theme';

body {
    font-family: "Inter", sans-serif !important;
  &.modal-open {
    overflow: hidden;
    position: fixed;
  }

  .main-section {
    background: $snow_white;
  }
  #menu-modal {
    overflow-y: scroll;
    position: fixed;
    background: #000;
    height: 100%;
    width: 100%;
    top: 0% !important;
    .close-reveal-modal {
      background-color: inherit;
    }
  }


  table {
    width:100%;
    &.table {
      background: inherit;
      border: none;
      margin-left: 0px;
    }
    tbody {
      tr {
        td {
          padding: 10px !important;
          white-space: pre-wrap;
          word-wrap: break-word;
        }
        &:nth-of-type(even) {
          background: inherit !important;
          margin: 0px;
        }
      }
    }
  }

  .padding {
    li {
      padding: 0.25rem 0.625rem;
    }
  }

  .widget {
    padding-top: 4%;
  }

  .menu_item {
    background-color: #2aa4a5;
    margin: 0px 10px 10px 0px;
    width: 46%;
    height: 60px;
    border: 1px solid;
  }

  .bottom {
    margin-bottom: 10px;
  }

  .tab-data {
    background: $snow_white !important;
  }

  .padded-border {
    padding: 5px;
    border: 2px solid #7b7474;
  }

  .wide {
    width: 90%;
    margin-left: 5%;
  }

  .tab-head {
    background-color: #bababa !important;
  }

  .banner-container {
    margin-bottom: 20px;
  }

  .border-bottom {
    border-bottom: 1px solid;
  }

  .border {
    border: 1px solid;
  }

  .price {
    a {
      font-size: 15px;
      font-weight: bold;
    }
    a:hover {
      color: #fff !important;
    }
  }

  .item {
    width: 100%;
    height: 100%;
  }

  .row-bo2 {
    vertical-align: middle;
    width: 100%;
    border: 1px solid #CCD3D3;
    padding: 10px;
    float: left;
  }

  .box2 {
    float: left;
    width: 23.9%;
    margin: 10px 0.3% 40px 0.8%;
  }

  .prodct {
    width: 100%;
    text-align: center;
    img {
      height: 200px;
    }
  }

  .price {
    padding: 10px 0px 5px;
    text-align: center;
    width: 100%;
    float: left;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    a {
      color: #fff;
    }
  }

  .align {
    text-align: center;
    vertical-align: middle;
    padding: 0px 0px 10px 0px !important;
    font-size: 15px;
  }

  img {
    width: 100%;
    height: auto;
  }

  .large_thumbs {
    height: 200px;
  }

  .tab-view {
    display: inline;
    font-size: 13px;
  }

  .mobile-view {
    display: none;
    font-size: 11px;
  }
}


@media all and (max-width: 736px) and (min-width: 425px) {
  body {
    .tab-view {
      display: none;
    }
    .mobile-view {
      display: block;
    }
    .prodct {
      img {
        height: 200px;
      }
    }
    .large_thumbs {
      height: 200px;
    }
    .row-bo2 {
      margin-left: 5%;
      width: 90%;
    }
    .price {
      a {
        font-size: 16px;
      }
    }
  }
}

@media all and (max-width: 424px) and (min-width: 375px) {
  body {
    .tab-view {
      display: none;
    }
    .mobile-view {
      display: block;
    }
    .prodct {
      img {
        height: 160px;
      }
    }
    .large_thumbs {
      height: 160px;
    }
    .row-bo2 {
      margin-left: 5%;
      width: 90%;
    }
    .price {
      a {
        font-size: 14px;
      }
    }
  }
}

@media all and (max-width: 374px) and (min-width: 320px) {
  body {
    .tab-view {
      display: none;
    }
    .mobile-view {
      display: block;
    }
    .prodct {
      img {
        height: 140px;
      }
    }
    .large_thumbs {
      height: 140px;
    }
    .row-bo2 {
      margin-left: 5%;
      width: 90%;
    }
    .price {
      a {
        font-size: 13px;
      }
    }
  }
}