@charset "UTF-8";
//importing variables
@import 'variables_red';
@import 'red_theme';

$border: #b3b3b3;
#blog-breadcrumb{
  border-bottom: 1px solid $border;
  ul{
    list-style: none;
    margin: 0px;
    li{
      display: inline-block;
      a{
        span{
          font-size: 14px;
          cursor: pointer;
        }
      }
    }
    li:not(:nth-child(1)){
      &:before{
        padding: 0 5px;
        content: "»";
      }
    }
  }
}

#fashion-blog-show{
  .title-block{
    border-bottom: 1px solid $border;
    padding: 10px 0px;
    margin-bottom: 20px;
    .title{
      h1{
        color: $text_black;
      }
    }
    .meta-title{
      margin-top: 5px;
    }
    .published-date{
      margin-top: 10px;
      color: $text_red;
    }
    .fixed-share-button{
      margin-left: 16px;
      i{
        font-size: 1.4em;
        width: 36px;
        height: 36px;
        line-height: 36px;
        border-radius: 5%;
        text-align: center;
        color: #FFF;
        margin-right: 5px;
        cursor: pointer;
      }
      i:before{
        width: 30px;
      }
      .fi-social-twitter{background-color:#32CCFE;}
      .fi-social-google-plus{background-color:#CF3D2E;}
      .fi-social-facebook{background-color:#3C599F;}
    }
  }
  .body-block{
    p{
      margin-left: 0px !important;
      margin-right: 0px !important;
      text-align: Justify;
      color: $text_black !important;
      span{
        color: $text_black !important;
      }
    }
    img{
      max-width: 100% !important;
      height: auto !important;
      float: none !important;
      margin: 5px;
    }
    blockquote{
      background: $snow_white;
      border: 1px solid #504e4e;
      border-left: 10px solid #504e4e;
      margin: 1.5em 10px;
      padding: 0.5em 10px;
      text-align: justify;
      font-style: italic;
      p{
        display: inline;
      }
    }
    .has-image{
      text-align: center !important;
    }
  }
  .footer{
    padding: 12px 0px;
    border-top: 1px solid $border;
    .next-previous-post{
      float: right;
    }
    .fi-rewind, .fi-arrow-left, .icon-next{
      background-color: $text_red;
      font-style: normal;
      color: $text_white;
      padding: 0.35em;
      font-size: $big_font;
    }
    .icon-next:after{
      content: "\f10b";
      font-family: "foundation-icons";
    }
  }
  #fashion-blog-all{
    padding: 0px 4px;
    .recent-title{
      font-size: 20px;
      color: $text_black;
      margin: 5px 0px;
    }
  }
}

#fashion-blog-all{
  .row{
    max-width: 100%;
  }
  .category-title{
    h2{
      text-transform: uppercase;
      font-size: 20px;
      margin-bottom: 8px;
      background-color: $body_white;
      display: inline-block;
      position: relative;
      z-index: 1;
      margin-bottom: 8px;
      padding-right: 20px;
    }
    &:after{
      position: absolute;
      z-index: 0;
      top: 14px;
      left: 0;
      width: 100%;
      height: 4px;
      background-color: #c5c5c5;
      content: " ";
    }
  }
  .card {
    overflow: hidden;
    box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.16), 0 2px 10px 0 rgba(0, 0, 0, 0.12);
    color: #272727;
    margin-bottom: 15px;
    .content {
      padding: 1rem 0.5rem 1rem 1rem;
      background-color: #f5f1f1;
      .blog-title{
        color: #212121;
        font-size: $big_font;
        font-weight: 600;
        text-transform: capitalize;
      }
      .publish-date{
        color: $text_red;
        font-size: $font_size;
        padding-bottom: 12px;
      }
    }
    .image{
      position: relative;
      .title {
        position: absolute;
        bottom: 0;
        left: 0;
        padding: 1.3rem;
        color: #fff;
      }
    }
  }
  .paginate-blog{
    margin-top: 10px;
    text-align: center;
    .prev-page, .next-page{
      height: 26px;
      line-height: 26px;
      border-radius: 5%;
      text-align: center;
      font-size: 1em;
      color: $text_white;
      padding: 5px 10px;
      margin-right: 5px;
      background-color: $text_red;
      cursor: pointer;
    }
    .prev-page:before{
      content: "\f1a9";
      font-family: "foundation-icons";
    }
    .next-page:after{
      content: "\f148";
      font-family: "foundation-icons";
    }
  }
}
#back-top{
  display: none;
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 99;
  a{
    i:before{
      background-color: $text_red;
      width: 35px;
      height: 35px;
      line-height: 35px;
      border-radius: 50%;
      color: white;
      text-align: center;
    }
  }
}

@media only screen and (max-width: 360px){
  #fashion-blog-show{
    .fixed-share-button{
      display: block;
      margin-left: 0px !important;
    }
  }
}

@media only screen and (max-width: 480px) {
  #blog-breadcrumb{
    span.name{
      font-size: 12px;
    }
  }
  #fashion-blog-all{
    .columns{
      padding-left: 0px;
      padding-right: 0px;
    }
    .card {
      margin-bottom: 0.750em;
      .image{
        width: 36%;
        display: inline-block;
        background-color: #f5f1f1;
        border-radius: 2px 0px 0px 2px;
        img{
          border-radius: 2px 0px 0px 2px;
          height: 148px;
          width: 95%;
          padding: 6px 0px 6px 6px;
          background-color: #f5f1f1;
        }
      }
      .content{
        width: 64%;
        display: inline-block;
        float: right;
        height: 148px;
        border-radius: 0px 2px 2px 0px;
      }
    }
  }
}

@media only screen and (min-width: 480px) and (max-width: 1024px){
  #fashion-blog-all{
    .card{
      height: 396px;
      .image{
        img{
          height: 250px;
          width: 100%;
          border-radius: 2px 2px 0px 0px;
        }
      }
      .content{
        height: 100%;
        border-radius: 0px 0px 2px 2px;
      }
    }
  }
}

@media only screen and (min-width: 1024px){
  #fashion-blog-all{
    .category-title{
      margin-left: 0.9375rem;
      margin-right: 0.9375rem;
      padding: 0px;
    }
    .card{
      height: 360px;
      .image{
        img{
          height: 250px;
          width: 100%;
          border-radius: 2px 2px 0px 0px;
        }
      }
      .content{
        height: 100%;
        border-radius: 0px 0px 2px 2px;
      }
    }
  }
}