// Place all the styles related to the surveys controller here.
// They will automatically be included in application.css.
// You can use Sass (SCSS) here: http://sass-lang.com/
body {
  font-family: "Inter", sans-serif !important;
}

.accordion-navigation.active>a,
dd.active>a {
  color: #000000;
}

.survey {
  width: 100%;
  background: #ffffff;
  padding: 20px 0px;
  color: #000000;
  border-radius: 5px;

  .content {
    background-color: #404040;
    border: none;
  }

  .zero {
    color: azure;
    white-space: nowrap;
    width: 100%;
    height: 10%;
  }

  .ten {
    color: azure;
    white-space: nowrap;
  }

  .rating-tab {
    width: 100%;

    .sl-handle {
      top: -0.6rem;
      height: 2rem;
      background-color: #03a9f4;
    }

    .sl-segment-color {
      background-color: #b3e5fc;
    }

    .grey-stars:before,
    .grey-stars-bottom:before {
      content: "\2605";
      font-size: 70px;
      letter-spacing: 5px;
      line-height: 70px;
    }

    .grey-stars {
      display: inline-block;
      width: 37px;
      color: #ffc315;
      padding-left: 7%;
    }

    .number_star {
      display: inline-block;
      position: relative;
      width: 0px;
      left: 10px;
    }

    .bottom {
      display: inline-block;
      width: 3%;
      padding: 24px;
      margin-left: 4px;
      padding-left: 50px;
      display: inline-block;
      width: 3%;
      padding: 21px;
      margin-left: 8px;
    }

    .number_star_bottom {
      display: inline-block;
      position: relative;
      top: -20px;
      width: 0px;
      left: 8px;
    }

    #next_nps_bifurcation {
      background-color: #670b19;
      border-radius: 4px;
      color: #ffffff;
      cursor: pointer;
      display: grid;
      place-content: center;
      margin: 5rem auto 1rem;
      max-width: 140px;
      height: 45px;
      padding: 0.3rem;
      display: none;

      &:hover {
        background-color: #911023;
        transform: translateY(-4px);
        transition: 0.3s;
      }

      .next-text {
        font-weight: bold;
        font-size: 24px;
        text-align: center;
      }
    }

    #no_of_rating_nps {
      color: white;
      position: relative;
      margin: 0% 0% 0% 13%;
      font-size: 19px;
      font-weight: bold;
    }
  }

  .response-question {
    text-align: -webkit-center;
    font-weight: 600;
    font-size: 24px;
    width: 100%;

    #done-box {
      padding: 0 3rem;
    }

    .review_table {
      display: grid;
      grid-template-columns: auto 1fr;
      grid-template-rows: 40px auto;
      gap: 0 2rem;
      max-width: 1385px;
      margin: 1rem 0;

      .image-container {
        max-width: 225px;
        display: grid;
        place-content: start;
        grid-row: span 2;

        img {
          border: 1px solid #d3d3d3;
          border-radius: 1rem;
          margin-top: 8px;
          overflow: hidden;
        }
      }

      .product-title {
        text-align: start;
        margin-bottom: 0.5rem;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }

      .review-question-container {
        width: 100%;

        .review_text {
          text-align: start;

          .review_issue {
            font-size: 18px;

            .issues {
              text-align: center;

              [id^="stitching"] {
                .answer {
                  flex-wrap: wrap;
                }
              }
            }
          }

          .review_text_box {
            .review-box {
              border: 1px solid #d3d3d3;
              border-radius: 10px;
              box-shadow: none;
              max-height: 150px;

              &:hover,
              &:focus,
              &:active {
                border: 1px solid #670b19;
              }

            }

            .submit-cancel-container {
              display: flex;
              justify-content: center;
              gap: 3rem;
              margin: 2rem;

              span.button {
                color: #ffffff;
                cursor: pointer;
                font-weight: 400;
                font-size: 14px;
                background-color: #670b19;
                border-radius: 4px;
                display: grid;
                place-content: center;
                width: 140px;
                height: 45px;
                padding: 0.3rem;
                font-size: 24px;
                font-weight: bold;

                &:hover {
                  background-color: #911023;
                  transform: translateY(-4px);
                  transition: 0.3s;
                }
              }
            }
          }
        }
      }
    }

    .review_table_0 {
      margin-top: 1rem;
    }

  }

  .question_group {
    display: flex;
    gap: 0 1rem;
    flex-wrap: wrap;
    max-width: 1385px;
    margin: 0 auto;
    padding: 0;
    border: 1px solid #d3d3d3;
    padding: 1rem;
    border-radius: 10px;

    .nps_questions {
      flex: 1 1 calc(50% - 1rem);
    }
  }

  .response-button {
    font-size: 15px;
    border-radius: 5px;
  }

  .new_survey {
    .rating-div {
      max-width: 1000px;
      margin: 0 auto;

      .rating-title {
        text-align: center;
        font-size: 24px;
        padding: 3rem 0 2rem 0;
        margin: 0 auto;
      }

      .rating-description {
        display: flex;
        justify-content: space-between;
        width: 100%;
        max-width: 1385px;
        margin: 0 auto 0.5rem;
      }

      .slider-container {
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        gap: 1.2rem;

        .emoji-container {
          display: flex;
          justify-content: space-between;
          width: 100%;
          max-width: 1340px;
          font-size: 25px;
        }

        #rating-slider {
          width: 100%;
          max-width: 1340px;
          cursor: pointer;
          -webkit-appearance: none;
          appearance: none;
          width: 100%;
          height: 12px;
          background: #eeeeee;
          outline: none;
          -webkit-transition: 0.2s;
          transition: opacity 0.2s;
          border-radius: 4px;

          &::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 25px;
            height: 25px;
            background: #670b19;
            cursor: pointer;
            border-radius: 50%;
            opacity: 0.8;

            &:hover {
              opacity: 1;
            }

            &:active {
              opacity: 1;
            }

            &:-moz-range-thumb {
              width: 25px;
              height: 25px;
              background: #04aa6d;
              cursor: pointer;
            }
          }
        }

        .rating-btns-container {
          display: flex;
          justify-content: space-between;
          width: 100%;
          max-width: 1385px;

          .number-btns {
            text-align: center;
            height: 45px;
            width: 65px;

            .number_star {
              height: 100%;
              width: 100%;
              font-weight: 600;
              border-radius: 4px;
              background-color: #ffffff;
              display: grid;
              place-content: center;
              border: 1px solid;
              cursor: pointer;
            }
          }
        }
      }
    }
  }
}

.survey-message {
  padding: 50px 0px;
  font-size: 25px;
  text-align: center;
  color: #673ab7;
  border-radius: 5px;
  background: aliceblue;
  display: none;
}

.rating-tab-design {
  text-align: center;
  /*width: 260px;*/
  position: static;
  display: inline-table;

  .grey-stars-design:before {
    content: "\2605";
    font-size: 25px;
    letter-spacing: 5px;
    line-height: 60px;
    cursor: pointer;
  }

  .grey-stars-design {
    display: inline-block;
    width: 40px;
    color: #353535;
  }

  .number_star-design {
    display: inline-block;
    position: relative;
    top: 20px;
    right: 29px;
    width: 0px;
  }
}

.bifurcation {
  float: left;
  clear: both;
  margin-left: 15px;
}

.nps_questions {
  .survey-questions-answers {
    list-style: none;
    font-size: 20px;
    text-align: left;

    input[type="radio"] {
      accent-color: #670b19;
      cursor: pointer;
    }
  }
}

.sub-bifurcation {
  .sub-heading {
    font-size: 16px;
  }
}

.submit_question_group {
  text-align: center;
  width: 100%;
  margin-top: 1rem;

  .submit {
    color: #ffffff;
    cursor: pointer;
    font-weight: 400;
    font-size: 14px;
    background-color: #670b19;
    border-radius: 4px;
    cursor: pointer;
    display: grid;
    place-content: center;
    max-width: 140px;
    height: 45px;
    padding: 0.3rem;
    margin: 0 auto;
    font-size: 24px;
    font-weight: bold;

    &:hover {
      background-color: #911023;
      transform: translateY(-4px);
      transition: 0.3s;
    }
  }
}

.nps_questions {
  .answer {
    padding: 0;
    margin: 0;
    display: flex;
    gap: 0;

    .survey-questions-answers {
      label {
        font-size: 18px;
      }

      text-align: left;
    }
  }

  .questions {
    font-size: 18px;
    padding: 0;
    margin: 0;
  }
}

.accordion .accordion-navigation,
.accordion dd {
  border: 1px solid #d3d3d3;
  border-radius: 10px;
  overflow: hidden;
}

.accordion .accordion-navigation>a,
.accordion dd>a {
  padding: 0.5rem;
}

.accordion-navigation .sub-text {

  .question_group {
    border: none;

    .nps_questions {
      .survey-questions-answers {
        font-weight: normal;
      }
    }
  }
}

// Only for star ratings page
.nps_questions[data-condition="gte_3"] {
  display: flex;
  align-items: center;
  justify-content: space-around;

  &+.submit_question_group {
    width: 140px;
    margin: 1rem 2rem;
  }
}

// Final Thank you page

@keyframes scale-in {
  0% {
    transform: scale(0.5);
    opacity: 0;
  }

  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.submission-message {
  animation: scale-in 0.5s ease-in-out forwards;

  .feedback-logo {
    img {
      border-radius: 50%;
      padding: 0.7rem;
      max-height: 50px;
      background: linear-gradient(135deg, #670b19 0%, #bd293a 50%, #e02b40 100%);
    }
  }

  .thank-you-text {
    margin-top: 1.2rem;
    margin-bottom: 0.4rem;
  }

  .feedback-subtext {
    font-size: 14px;
    font-weight: 400;
    color: #404040;
  }

  .return-to-home {
    background: linear-gradient(160deg, #670b19 0%, #bd293a 50%, #e02b40 100%);
    border-radius: 0.5rem;
    margin: 1rem auto;
    color: white;
    padding: 0.5rem 2rem;
    width: 100%;
    max-width: 300px;
    display: inline-block;
    font-size: 16px;
    font-weight: 400;
    transition: transform 0.3s ease-in-out;

    &:hover {
      transform: translateY(-4px);
    }
  }
}

dl,
dt,
dd,
ul,
ol,
li,
h1,
h2,
h3,
h4,
h5,
h6,
pre,
form,
p,
blockquote,
th,
td {
  margin: 2px 0px;
}

ul {
  margin-left: 0rem;
}

@mixin responsive($max-width, $container-width, $slider-width) {
  @media only screen and (max-width: $max-width) {
    .survey .new_survey .rating-div {

      .rating-description,
      .slider-container .rating-btns-container {
        max-width: $container-width;
      }

      .slider-container #rating-slider,
      .slider-container .emoji-container {
        max-width: $slider-width;
      }
    }
  }
}

@include responsive(1500px, 1100px, 1060px);
@include responsive(1200px, 900px, 860px);
@include responsive(1000px, 750px, 710px);
@include responsive(800px, 600px, 567px);
@include responsive(650px, 500px, 480px);

@media only screen and (max-width: 1400px) {
  .survey {
    .question_group {
      margin: 0
    }
  }
}

@media only screen and (max-width: 1024px) {
  .survey .new_survey .rating-div .rating-title {
    padding: 2rem 1rem;
  }
}

@media only screen and (max-width: 1000px) {
  .survey .new_survey .rating-div .slider-container .rating-btns-container .number-btns .number_star {
    border: none;
  }

  .survey {
    .new_survey {
      .text-message {
        .question_group[data-next-classes="#review_designs"] {
          margin: 1rem;
        }
      }

      .rating-div {
        .rating-description {
          padding: 0 1rem;
        }
      }
    }

    .response-question {

      .review_table {
        gap: 0 1rem;
        padding: 0 1rem;

        .image-container {
          max-width: 150px;
        }

        .product-title {
          font-size: 18px;
        }

        .review-question-container {
          width: 100%;

          .review_text {
            .review_text_box {
              .submit-cancel-container {
                margin: 1rem 0rem 0rem;

                .button {
                  margin: 0;
                }
              }
            }
          }
        }
      }

      #done-box {
        max-width: 1385px;
      }

      .review-the-products {
        max-width: 1385px;
      }

      #done-box {
        font-size: 18px;
      }
    }

    .question_group {
      flex-direction: column;
      align-items: center;

      .nps_questions {
        width: 100%;

        .answer {
          .survey-questions-answers {
            label {
              font-size: 16px;
              margin-right: 2rem;
            }
          }
        }

        .questions {
          font-size: 14px;
        }
      }

      .nps_questions[data-condition="gte_3"] {
        justify-content: space-between;
      }
    }

    .survey-questions-answers {
      font-size: 16px;
    }
  }
}

@media only screen and (max-width: 600px) {
  .survey .new_survey .rating-div {

    .rating-description,
    .slider-container .rating-btns-container {
      max-width: 100%;
    }

    .slider-container #rating-slider {
      width: 97%;
    }

    .slider-container .emoji-container {
      max-width: none;
      padding: 0px 6px;
    }

    .rating-title {
      font-size: 18px;
      padding: 0 0.5 2rem;
    }

    .rating-content .rating-description {
      gap: 30px;
    }
  }

  .survey .new_survey .rating-div .rating-description {
    padding: 0 1rem;
  }

  .survey {
    padding: 0;

    .rating-tab {
      #next_nps_bifurcation {
        max-width: 110px;
        height: 40px;
        margin: 3rem auto 1rem;

        .next-text {
          font-size: 18px;
        }
      }
    }

    .question_group {
      gap: 0;

      .submit_question_group {
        .submit {
          max-width: 110px;
          height: 40px;
          font-size: 18px;
        }
      }
    }

    .response-question {
      padding: 0 1rem;

      .review-the-products {
        font-size: 18px;
      }

      .review_table {
        grid-template-rows: auto auto;
        gap: 1rem;
        padding: 0;

        .image-container {
          grid-row: span 1;

          img {
            margin-top: 0;
          }
        }

        .product-title {
          max-height: 55px;
          white-space: normal;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          line-clamp: 2;
          -webkit-box-orient: vertical;
        }

        .review-question-container {
          grid-column: span 2;

          .nps_questions[data-condition="gte_3"] {
            flex-direction: column;

            &+.submit_question_group {
              max-width: 110px;
              height: 40px;
              font-size: 18px;
            }
          }

          .review_text {
            .review_issue {
              font-size: 14px;

              .issues {
                .accordion-navigation {
                  .content {
                    padding: 0;

                    .sub-text {
                      margin-left: 0;
                    }
                  }
                }

                .answer {
                  .survey-questions-answers {
                    label {
                      margin-right: 3rem;
                    }
                  }
                }
              }
            }

            .review_text_box {
              .submit-cancel-container {
                justify-content: space-between;
                flex-direction: row-reverse;

                span.button {
                  width: 110px;
                  height: 40px;
                  font-size: 18px;
                }
              }
            }
          }
        }
      }

      #done-box {
        padding: 0;
      }
    }
  }
}
