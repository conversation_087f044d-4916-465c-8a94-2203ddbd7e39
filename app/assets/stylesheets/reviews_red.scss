// Place all the styles related to the reviews controller here.
// They will automatically be included in application.css.
// You can use Sass (SCSS) here: http://sass-lang.com/
// Place all the styles related to the review controller here.
// They will automatically be included in application.css.
// You can use Sass (SCSS) here: http://sass-lang.com/
@import 'variables_red';
@import 'red_theme';

body {
  font-family: "Inter", sans-serif !important;
}

.page.designs_reviews,
.page.reviews_site_review {
  #container {
    margin-top: 1.8em;
  }
}

#site_review_top_content {
  padding: 20px;
  background-color: $white;
  margin-bottom: 20px;
  margin-top: 20px;
  box-sizing: border-box;
  width: 100%;
  max-width: 1200px;
  margin: 2rem auto;
  border: 1px solid #eeeeee;

  #top_content {
    &.read-more {
      overflow: hidden;
    }

    font-size:0.9rem;
  }
}

#review_of_design {
  background: $light_grey;
  margin-bottom: 16px;
  color: $text_black;
}

.review-container {
  width: 100%;
  height: 100%;
  color: $text_black;
  padding: 6px;
  max-width: 1200px;
  margin: 0 auto;

  .heading {
    font-size: 36px;
    font-weight: 500;
  }

  .heading_review {
    text-align: left;
    color: rgba(48, 48, 48, 0.8);
    margin-bottom: 0;
    padding-bottom: 1%;
    font-size: 1em;
    font-weight: bold;

    .view_all {
      font-size: 15px;
      float: right;
      margin-right: 10px;
    }
  }

  .heading-content {
    font-size: 15px;
    font-weight: bold;
    display: flex;
    flex-flow: row-reverse;
    align-items: center;
    justify-content: space-between;
  }

  .tabbed-btn {
    .add-new-review {
      display: flex;
      justify-content: end;

      .btn {
        margin: 0;
      }
    }
  }

  .tabbed-btn-row {
    margin-top: 1.5rem;
    background-color: #bababa;
    display: flex;

    .tabbed-btn {
      text-align: center;
      vertical-align: middle;
      padding: 0.1rem 1.4rem;
      font-size: 16px;
      cursor: pointer;
      color: $white;
      font-weight: 500;
    }

    .active-btn {
      background-color: #303030;
    }

    .passive-btn {
      background-color: transparent;
      border: none;
    }
  }

  .tabbed-view {
    width: 100%;
    color: $text_black;
    padding-bottom: 5px;

    .review-row:first-child {
      border-bottom: none;
    }

    #hidden_site_review {
      display: none;
    }

    .review-row {
      width: 100%;
      clear: both;

      .pagination {
        text-align: center;

        .page,
        .first,
        .prev,
        .next,
        .last {
          padding: 6px 12px;
        }
      }

      .add-new-review {
        float: right;
      }

      .best_designs_btn {
        text-align: end;
      }

      .block {
        background-color: $body_white;
        width: 100%;
        padding: 15px;
        border: 1px solid #eeeeee;
        display: flex;
        align-items: center;
        gap: 2rem;
        position: relative;

        .margin-left-top {
          margin-top: 1%;
          margin-left: 8%;
        }

        .view-more {
          display: inline-block;
        }

        .view-more-btn {
          font-size: 12px;
          text-wrap-mode: nowrap;
        }

        .user-sign {
          width: 70px;
          height: 70px;
          border-radius: 50%;
          background-color: #000000;
          text-align: center;
          display: grid;
          place-content: center;
          overflow: hidden;

          .user-name {
            position: relative;
            font-size: 1rem;

            .user_image {
              width: 100%;
              height: 100%;
              border-radius: 50%;
              position: relative;
            }
          }
        }

        .design-img {
          overflow: hidden;
          width: 50px;
          display: grid;
          place-content: center;

          .image_block_hidden {
            width: 50px;
            height: 50px;
          }
        }

        .stars {
          font-size: 12px;
          max-width: 110px;

          .star {
            min-width: 110px;
          }

          .star img {
            background: none;
          }

          .user-name-full {
            overflow: hidden;
          }

          .rated-ago {
            margin-top: 0px;
          }
        }

        .review-text {
          font-size: 12px;
          text-align: justify;
          margin-left: 0px;
          max-width: 720px;
          flex: 20%;
        }
      }
    }
  }

  #product_reviews {
    border: 1px solid #eeeeee;
    padding: 1rem 2rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }

  #site_reviews {
    display: none;
    padding: 1rem 2rem;
    border: 1px solid #eaeaea;
    gap: 1rem;
    flex-direction: column;
  }
}

.reveal-modal {
  color: $text_black;

  .close-reveal-modal {
    &:hover {
      color: red;
    }
  }

  #review-text {
    width: 100%;
    min-height: 100px;
    max-height: 65vh;
    resize: vertical;
    overflow-y: auto;
    padding: 20px;
    margin-top: 10px;
    box-sizing: border-box;
  }

  .submit-review {
    display: flex;
    justify-content: right;

    .btn-custom-primary {
      font-size: 16px;
      padding: 0.7rem 1rem;
    }
  }
}

.black-content {
  position: absolute;
  top: 50%;
  overflow: auto;
  left: 50%;
  transform: translate(-50%, -50%);

  #modalTitle {
    color: $text_black !important;
  }

  #modal_star {
    border-bottom: 1px solid white;
    padding-bottom: 15px;
  }

  @media (max-height: 600px) {
    .modal-review-text {
      height: 100%;
      overflow-y: auto;
    }
  }

  @media only screen and (min-device-width:500px) and (orientation:landscape) {
    .modal-review-text {
      height: 100%;
      overflow: auto;
    }
  }
}

.btn-custom-green {
  padding: 3px 12px !important;
  color: #fff;
  background-color: #2ecc71;
  border-color: #2ecc71;
}

.btn-custom-primary {
  margin: 0px;
  padding-left: 0px;
  padding-top: 0px;
  padding-bottom: 0px;
  padding-right: 0px;
  color: white;
  background-color: #670b19;
  font-size: 14px;
  padding: 7px 12px;
  border-radius: 5px;

  &:active,
  &:focus,
  &:hover {
    background-color: #4c0913;
    transform: scale(0.98);
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.3);
  }
}

.btn-custom-red {
  padding: 3px 12px !important;
  color: #fff;
  background-color: #EC407A;
  border-color: #EC407A;
}

.btn-custom-green:hover {
  background-color: #27ae60;
  border-color: #27ae60;
}

.btn-custom-blue:hover {
  border-color: $dark_red;
}

.btn-custom-red:hover {
  background-color: #E91E63;
  border-color: #E91E63;
}

.btn-custom-dark-red {
  background-color: #8f1b1d;
  width: 100%;
  height: 28%;
}

@media only screen and (max-width: 700px) {
  .review-container {
    .heading {
      font-size: 30px;
    }

    .heading-content {
      flex-wrap: wrap;
      gap: 1rem;

      .tabbed-btn {
        width: 100%;

        .review-btn {
          width: 100%;
        }

        .btn-custom-primary {
          width: 100%;
          padding: 0.8rem 1rem;
        }
      }

      .tabbed-btn-row {
        width: 100%;
        margin-top: 0;
      }
    }

    .tabbed-btn-row .tabbed-btn {
      padding: 0.5rem 1rem;
      font-size: 15px;
    }

    #product_reviews,
    #site_reviews {
      padding: 1rem;
    }

    .tabbed-view .review-row {
      .pagination {

        .page,
        .first,
        .prev,
        .next,
        .last {
          padding: 5px;
        }

        .first,
        .prev,
        .next,
        .last {
          font-size: 14px;
        }

        .gap {
          display: none;
        }
      }

      .block {
        flex-wrap: wrap;
        gap: 1rem;

        .design-img {
          flex: 50%;
        }

        .review-text {
          flex: 100%;
        }
      }
    }
  }

  .reveal-modal {
    min-height: unset;

    .btn-custom-primary {
      width: 100%;
      margin-top: 8px;
    }
  }
}
