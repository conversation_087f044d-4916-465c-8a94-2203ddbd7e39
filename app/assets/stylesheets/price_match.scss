@import 'variables_red';

#container.off-canvas-wrap #main-section #container {
  margin: 0;
}

.price-match-container {
  background: hsl(45, 45%, 96%);
  width: 100%;

  #price-match-banner {
    color: $plain-white-color;
    padding: 2rem;
    text-align: center;
    background: $banner-gradient;

    .image-box {
      max-width: 275px;
      display: grid;
      place-content: center;
      margin: 0 auto;
    }

    .content-title {
      font-size: 36px;
      font-weight: 600;
    }

    .content-description {
      max-width: 900px;
      font-size: 20px;
      margin: 1rem auto;
    }
  }

  .price-match-steps {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 3rem;
    padding: 4rem;
    position: relative;
    text-align: left;
  }

  .step {
    max-width: 900px;
    width: 100%;
    display: flex;
    align-items: flex-start;
    gap: 2rem;
    position: relative;

    &::after {
      position: absolute;
      content: "";
      background: $connector-gradient;
      left: 37px;
      top: 97px;
      z-index: 10;
    }

    &:nth-child(1)::after {
      width: 3px;
      height: 50px;
    }

    &:nth-child(2)::after {
      width: 3px;
      height: 73px;
    }

    &:nth-child(3)::after {
      display: none;
    }

    .svg {
      max-width: 80px;
      border: 1px solid #e0ddd1;
      border-radius: 0.5rem;
      overflow: hidden;
      background: #fefefe;
    }

    .steps {
      width: 100%;
      background: $plain-white-color;
      border: 1px solid $step-border-color;
      border-radius: 0.75rem;
      padding: 2rem;
      box-shadow: 0 8px 25px -8px hsla(348, 65%, 35%, 0.15);

      .title {
        font-size: 18px;
        color: $highlight-color;
        font-weight: 600;
      }

      .details a {
        color: $highlight-color;
        text-decoration: underline;
        font-weight: bold;
        transition: background-color 0.4s ease;

        &:hover {
          background-color: $highlight-color;
          color: $plain-white-color;
          padding: 0 5px;
          text-decoration: none;
          border-radius: 8px;
        }
      }

      .whatsapp-button {
        display: inline-block;
        background: $banner-gradient;
        padding: 0.7rem 1.3rem;
        border-radius: 0.5rem;
        margin-top: 1rem;
        cursor: pointer;
        transition: transform 0.3s ease, box-shadow 0.3s ease;

        &:hover {
          transform: scale(1.03);
          box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
        }

        a {
          color: $plain-white-color;
          text-decoration: none;
          display: flex;
          align-items: center;
          justify-content: space-between;
          gap: 10px;

          img {
            max-height: 20px;
          }
        }
      }
    }
  }

  #live-chat {
    position: fixed;
    bottom: 20px;
    right: 20px;
  }
}


@media only screen and (max-width: 1400px) {
  .price-match-container {
    .price-match-steps {
      margin-left: auto;
      margin-right: auto;
      max-width: 800px;
    }
  }
}

@media only screen and (max-width: 1024px) {
  .price-match-container {
    .price-match-steps {
      max-width: 700px;
    }
  }
}

@media only screen and (max-width: 768px) {
  .price-match-container {
    #price-match-banner {
      .content-title {
        font-size: 26px;
      }

      .content-description {
        font-size: 16px;
      }
    }

    .price-match-steps {
      max-width: 500px;
      gap: 3rem;
      padding: 2rem;

      .step {
        flex-direction: column;
        text-align: center;
        align-items: center;
        gap: 5rem;

        &::after {
          left: 50%;
          transform: translate(-50%, 0);
          height: 50px;
          width: 3px;
        }

        &:nth-child(3)::after {
          display: block;
        }
      }
    }
  }
}
