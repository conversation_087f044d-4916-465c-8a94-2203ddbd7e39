// Place all the styles related to the store controller here.
// They will automatically be included in application.css.
// You can use Sass (SCSS) here: http://sass-lang.com/
@import 'variables';
@import 'white_theme';
body {
  font-family: "Inter", sans-serif !important;
}
.store_page_design {
  margin: 0.1em;
  >li {
    img{
      width:100%;
    }
    .fr_page {
      box-shadow: 0 0 2em black;
      .panel {
        height: 7.8em;
        position: relative;
        padding:0.6em;
        background: $snow_white;
        border: 1px solid $snow_white;

        &.design_desc {
          margin-bottom: 0em;
          a {
            color: white;
          }
        }
        .add_to_cart_link {
          margin-bottom: 0.7em;
          width: 89%;
          bottom: 0em;
          color: white;
          background: $dark_green;
          position: absolute;
        }
        .sold_out_link {
          margin-bottom: 0.7em;
          width: 89%;
          bottom: 0em;
          color: white;
          position: absolute;
        }
      }
    }
  }
  li {
    &.original_price {
      font-size: 0.8em;
      color: white;
    }
    &.discount_price {
      font-weight: bold;
      color: white;
    }
    &.percent_off {
      color: red;
      margin-top: -1.7em;
      font-size: 0.9em;
    }
  }
  .design_desc {
    padding: 0.5em;
    li {
      padding-bottom: 0em;
    }
  }
}
.store_page_block {
  margin-bottom: 2em;
}
.short_filter_btn {
  .btn_apply {
    margin-top: 1em;
    background-color: $dark_green !important;
  }
}

.on-off-radiobox label {
  color: white;
}
.on-off-checkbox label {
  color: white;
}

.navigate_page {
  vertical-align: middle;
  margin-bottom: 1em;
}

.nav-button {
  margin:1em 1em 0 0;
}

#design_details {
  width:100%;
}

.details_block {
  color: white;
}

.design-col1 {
    float:left;
    width:80%;
    font-size: 15px !important;
}

.design-col2 {
    float:right;
    width:20%;
    font-size: 11px !important;
    background-color: red;
    margin-top: 4px;
    border-radius: 3px;
}

.discount_new_wrap {
  text-align:center;
  word-wrap: break-word;
  padding-left: 0.5px;
}

.add_new_pos {
  position: relative !important;
  width: 100% !important;
}

.discount_font {
  font-size: 0.8em;
}

.design_price {
  font-weight: bold;
}
