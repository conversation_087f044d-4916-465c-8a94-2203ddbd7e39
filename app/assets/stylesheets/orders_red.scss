// Place all the styles related to the Orders controller here.
// They will automatically be included in application.css.
// You can use Sass (SCSS) here: http://sass-lang.com/
@import 'variables_red';
@import 'red_theme';
@import 'selectize';
@import 'payments';
body {
  font-family: "Inter", sans-serif !important;
}
.modal-open {
  overflow: hidden !important;
}
#container{
  margin-top: 0.5em !important;
}


#paypal_button{
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  display: table;
}

.btn {
  background: #670b19;
  border: 0px;
  width: 100%;
  color: #fff;
  display: inline-block;
  font-size: 14px;
  font-weight: bold;
  padding: 8px 15px;
  text-decoration: none;
  text-align: center;
  position: relative;
  transition: color .1s ease;
}
.btn.btn-big {
  font-size: 18px;
  padding: 15px 20px;
  min-width: 100px;
}
.btn-close {
  color: #303030;
  font-size: 30px;
  text-decoration: none;
  position: absolute;
  right: 5px;
  top: 0;
}
.btn2{
  background-color: #fff; 
  color: #8f1b1d; 
  border: 2px solid #8f1b1d;
}
.modal:target:before {
  display: none;
}
.modal:before {
  content:"";
  display: block;
  background: rgba(0, 0, 0, 0.6);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 10;
}
.modal .modal-dialog {
  background: #fefefe;
  border: #333333 solid 1px;
  border-radius: 5px;
  position: fixed;
  bottom: 0;
  left: -0.5px;
  z-index: 11;
  width: 100%;
  -webkit-transform: translate(0, 0);
  -ms-transform: translate(0, 0);
  transform: translate(0, 0);
  -webkit-transition: -webkit-transform 0.3s ease-out;
  -moz-transition: -moz-transform 0.3s ease-out;
  -o-transition: -o-transform 0.3s ease-out;
  transition: transform 0.3s ease-out;
}
.modal:target .modal-dialog {
  top: -100%;
  -webkit-transform: translate(0, -500%);
  -ms-transform: translate(0, -500%);
  transform: translate(0, -500%);
}
.modal-body {
  padding: 0px 25px;
  font-size: 14px;
}
.modal-header{
  border-bottom: #eeeeee solid 1px;
  background: #E0E0E0;
  padding: 5px 10px;
  p{
    margin: 0px;
  }
}
.modal-footer{
  padding: 15px 25px;
  form{
    padding-bottom: 7px;
  }
}

.modal-header h2 {
  font-size: 20px;
}

.domestic-offer-modal {
  &.reveal-modal{
    height: 80%;
    min-height: 80vh;
    width: 80%;
    left: 10%;
  }
}
.offers_tab {
  .hidden_offers{
    display : none;
  }
  border-left-style: solid;
  border-left-color: #670B19;
  border-left-width: 3px;
  border-radius: 2px;
  padding-left: 0.5rem ;
  .offer_row, .offers_title {
    padding-bottom: 0.5rem ;
  }
  .offer_text_box {
    display: flex;
    padding-right: 0px;
    .offer_text {
      padding-left: 0.5rem;
      font-size: 13px;
      padding-top: 0.3rem;
    }
  }
  .offer_image {
    min-height: 20px;
    min-width: 20px;
  }
  box-shadow: 0 2px 2px 0 #E0E0E0;
  .tnc_offer {
    font-size: 12px;
    padding-top: 0.3rem;
  }
  .hide_up {
    display :none;

    #up_arrow_toggle {
      border: solid black;
      border-width: 0 2px 2px 0;
      display: inline-block;
      padding: 3px;
      transform: rotate(-135deg);
      -webkit-transform: rotate(-135deg);
      margin-left: 10px;
      margin-top: 8px;
      border-color: #670B19;
    }
  }
  #down_arrow_toggle {
    border: solid black;
    border-width: 0 2px 2px 0;
    display: inline-block;
    padding: 3px;
    transform: rotate(45deg);
    -webkit-transform: rotate(45deg);
    margin-left: 10px;
    margin-bottom: 2px;
    border-color: #670B19;
  }
  .domestic_offers_toggle {
    display :flex;
    color: #670B19;
    font-size: 13px;
  }
}

#codModal{
  overflow-y: scroll;
  background-color:$body_white;
  top: 0 !important;
  position: fixed;
  width: 100%;
  height: 100%;
  border: 0;
  #cod-confirmation-message{
    color: $text_black;
  }
  #cod-confirmation-message{
    margin-top: 5%;
  }
  .close-reveal-modal{
    font-size: 1.8em;
    padding: 1% 4% 3% 10%;
    right: 0.3em;
  }
  .cod-otp-verification-form{
    font-size: 0.9em;
    text-align: center;
    padding-top: 10%;
    .error-msg-for-otp{
      display: none;
      color: red;
    }
    .cod-otp-input{
      width: 50%;
      font-size: 1.2em;
      text-align: center;
      color: $text_black;
      background-color: inherit;
      border-width: 0 0 3px;
      margin: 5% auto;
      box-shadow: none !important;
    }
    #otp-phone-change-form-and-content{
      display: none;
    }
    .cod-otp-modal-buttons{
      width: 50%;
      border-radius: 0 !important;
      text-transform: uppercase;
    }
    #otp-phone-text-value{
      border: 0;
      padding: 0;
      background: 0;
      width: auto;
      font-size: 1.2em;
      font-weight: 800;
      clear: both;
      margin: 0;
      display: inline;
    }
    #phone-change-button{
      font-size: 0.9em;
      margin: 0;
    }
    .button-as-text[type=button]{
      background: 0;
      display: inline;
      padding: 0;
      color: $dark_red;
    }
  }
  .footer
  {
    margin-top:40px;
    float:right;
    #successButton
    {
      background: linear-gradient(to bottom, #0E9A7D, #267363);
      border-radius: 0.1rem;
      font-size: 1rem;
      padding: 18px 52px;
    }
  }
}

#paypalSuccessModal{
  -webkit-text-size-adjust: 100%;
  background: transparent;
  padding: 0px;
  border: 0px;
  #paypalModal{
    background: $menu_background;
    width: 90%;
    margin-right: auto;
    margin-left: auto;
    .modal-body{
      padding: 15px;
      #error-message{
        .error-text{
          font-weight: bold;
          margin-bottom: 10px;
        }
        .paypal-success-reasons{
          margin-left: 10px;
        }
      }
      .text-message{
        margin-bottom: 15px;
      }
      p{
        margin-top: 10px;
        margin-bottom: 10px;
        text-align: center;
      }
      .close-modal{
        float: right;
        line-height: 0px !important;
        font-size: 30px;
        color: $text_white;
        margin-right: -10px;
      }
    }
    .modal-footer{
      text-align: center;
      .retry-payment{
        font-weight: bold;
      }
      .cancel{
        color: white;
        background-color: transparent;
      }
    }
  }
}


#get_app_link{
  display: none;
  .panel{
    box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.4);
    padding-right: 8px;
    h1{
      color: $text_black;
    }
    .close{
      float: right;
      border-radius: 50%;
      background-color: transparent;
      padding: 2px 6px;
      color: $text_black;
      font-weight: bold;
    }
    p{
      padding-right: 2rem;
      text-align: justify;
      color: $text_black;
    }
    .success{
      margin-left: 19%;
      text-align: center;
      font-size: 1.2em;
      display: inline-block;
      padding: 0px 20px 0px 25px;
      border-radius: 4px;
      background-color: $dark_red;
      color: $body_white;
      box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2);
      .fi-social-android{
        font-size: 30px;
        color: $body_white;
        vertical-align: middle;
      }
      .fi-social-apple{
        font-size: 30px;
        color: $body_white;
        vertical-align: middle;
      }
    }
  }
}

.card_message {
  display:none;
  color: #076915;
}

#create_orders_block{
  .accordion {
    padding-bottom: 1em;
  }
  // font-size: $big_font;
  // label {
  //   color: inherit;
  // }
  // .accordion {
  //   padding-left: 0em;
  //   margin-left: 0em;
  //   .accordion-navigation {
  //     > a {
  //       background: inherit;
  //       color: inherit;
  //       padding: 0rem;
  //       &:before {
  //         content: '»';
  //         float: left;
  //         color: $dark_red;
  //         font-weight: 700;
  //         padding-right: 2px;
  //       }
  //     }
  //     > .content {
  //       padding: 0rem;
  //       &.active {
  //         background: inherit;
  //       }
  //     }
  //     &.active > a:before {
  //       content: '«';
  //     }
  //   }
  // }
  .item_block {
    border: 0.1em solid #dcdcdc;
    margin-bottom: .5em;
    color: $text_light_gray;
    padding: 3px;
    .truncate{
      color: $text_light_gray;
    }
  }
  ul.addons-notes{
    padding-right: 0;
    li.accordion-navigation{
      a{
        font-size: 14px;
        font-family: "Inter", sans-serif !important;
      }
      .text-right{
        float: right;
      }
      .content{
        .row{
          .left{
            margin-left: 5px;
          }
        }
      }
    }
  }
  .order_total {
    padding: 0.2em 1em;
    margin-top: 1em;
  }
  #totals_block{
    color: $text_light_gray;
    .row{
      .columns{
        padding-right: 0px;
      }
    }
  }
  .notice_rts {
    color: rgb(103, 11, 25);
    font-size:12px;
    line-height:15px;
    font-style:italic
  }
  .grand_total, .grand_total_with_cod, .grand_total_pd{
    color: $text_black;
    font-size: 15px;
    font-weight: 600;
  }
  .shipping_address {
    margin-bottom: 2em;
    padding: 1em;
    .address-text{
      width: 100%;
      font-size: 14px;
      color: $text_light_gray;
    }
    table {
      background: inherit;
      border: none;
      margin-bottom: 0em;
      tr {
        &:nth-of-type(even) {
          background: inherit;
        }
        td {
          color: inherit;
          padding: 0.1em;
        }
      }
    }
  }
}

#order_show_block {
  font-size: $big_font;
  .order-ack-title{
    color: $text_black;
  }
  .panel_block {
    .panel_content {
      padding: 1em;
    }
  }
  hr {
    border: $gray solid;
    border-width: 0.1em 0 0;
  }
  table {
    width: 100%;
    border: 0em;
    background: inherit;
    tr {
      background: inherit;
      td, th {
        color: inherit;
        line-height: 1em;
        padding: 0.3em;
      }
    }
  }
  .line_item_details {
    padding: 0.4em;
    color: $text_black;
    border: none;
  }
  .designer-order-block{
    background-color: $body_white;
    .panel_content{
      background-color: $body_white;
    }
  }
}

.notice_class {
  padding-left:10px;
  color:orange;
  font-size:14px;
  line-height:15px;
}

.quantity_total {
  margin-right: 0em;
}

table.customer_order {
  border: 1px solid $gray;
  border-collapse: collapse;
}

table.customer_order tr {
  td {
    border: 1px solid $gray;
    color: white;
  }
  th {
    border: 1px solid $gray;
    color: white;
  }
  td.cost {
    text-align: right;
  }
}

table.customer_order tr{
  background: #333333;
  color: white;
}


/* The Modal (background) */
.modalForm {
  display: none; /* Hidden by default */
  position: fixed;
  z-index: 999;
  -webkit-transform: translate3d(0,0,0);
  transform: translate3d(0,0,0);
  padding-top: 80px;
  left: 0;
  top: 0;
  width: 100%;
  height: 800px;
  background-color: rgb(0,0,0);

  /* Modal Content (Iframe) */
  .modal-content {
    margin: auto;
    display: block;
    width: 80%;
    max-width: 100%;
    -webkit-animation-name: zoom;
    -webkit-animation-duration: 0.6s;
    animation-name: zoom;
    animation-duration: 0.6s;
  }

  @-webkit-keyframes zoom {
    from {transform:scale(0)} 
    to {transform:scale(1)}
  }

  @keyframes zoom {
    from {transform:scale(0)} 
    to {transform:scale(1)}
  }
  /* The Close Button */
  .close {
    position: absolute;
    top: 25px;
    right: 5px;
    color: #f1f1f1;
    font-size: 40px;
    font-weight: bold;
    transition: 0.3s;
  }

  .close:hover,
  .close:focus {
    color: #bbb;
    text-decoration: none;
    cursor: pointer;
  }
  /* 100% Image Width on Smaller Screens */
  @media only screen and (max-width: 700px){
    .modal-content {
      width: 100%;
    }
  }

  @media only screen and (min-width: 1200px){
    .modal-content {
      width: auto;
      height: auto;
    }
  }
}

/* Order status on order show page */
#order_status {
  float:left;
  margin-left: 1%;

  #circle_div {
    position: relative;
    float:left;
    width: 16%;

    .circle_stage {
      display: inline-block;
      border-radius:50%;
      -moz-border-radius: 50%;
      -webkit-border-radius: 50%;
      -o-border-radius: 50%;
      font-size:12px;
      line-height:50px;
      text-align:center;
      border: 2px solid;
    }
    .circle_stage_domestic{
      display: inline-block;
      border-radius: 50%;
      -moz-border-radius: 50%;
      -webkit-border-radius: 50%;
      -o-border-radius: 50%;
      color: $text_black;
      text-align: center;
      border: 2px solid $light_grey;
      font-size: 0.7em;
      vertical-align: middle;
    }
    .square_stage {
      display: inline-block;
      font-size:12px;
      line-height:50px;
      text-align:center;
      border: 2px solid;
    }

    .step_image {
      background: transparent;
      position: relative;
      top: -4px;
    }

    .step_number {
      position: relative;
      top: -14px;
      color: black;
    }

    .vertical_line {
      width:6px;
      height:40px;
      margin-left: 52px;
      margin-bottom: -8px;
      display: inline-block;
      border: 1px solid
    }
  }

  #notes_div {
    width: 82%;
    margin-left: 80px;

    .status_note {
      margin-left: 18px;
      color: $text_black;
      padding-top: 2px;
      font-size:small;
      margin-top: -3px;
      margin-bottom: 25px;
    }
  }
}

.all_order_stages{
  display: inline-block;
  .horizontal-line{
    width: 2em;
    height: 0.3em;
    background: yellowgreen;
    border: 1px solid white;
    border-radius: 1px;
    float: right;
  }
  .base {
  background: yellowgreen;
  display: inline-block;
  height: 1em;
  margin-left: 5%;
  position: relative;
  width: 2.5em;
  float: left;
  margin-right: 5%;
  .pointer {
    border-left: 15px solid yellowgreen;
    border-top: 8px solid transparent;
    border-bottom: 8px solid transparent;
    content: "";
    height: 0;
    left: 40px;
    position: absolute;
    top: 0px;
    width: 0;
  }
  .uc_base{
    font-size: x-small;
    color: black;
    padding: 2px;
  }
}
}
.status_text{
  margin: 2% 10% 0% 10%;
  width: 80%;
  text-align: center;
  color: $text_black;
  border-radius: 2px;
  font-size: 0.8em;
  font-weight: 600;
  border: $border_black;
}
#return_panel{
  .bordered_block{
    background-color: transparent;
    box-shadow: none;
    .policy_button{
      background-color: $light_red;
      color: $text_white;
    }
  }
}
/* Return Button */
body.modal-open{
  overflow: hidden;
  position: fixed;
}

.reveal-modal {
  overflow-y: auto;
  height: 100%;
  position: fixed;
  top: 20px !important;

  .close-reveal-modal {
    color: $gray;
  }
}

#bank_deposit_order_box {
    display: none;
    margin-right: 30px;
    background-color: #cecece;
    color: #000000;
    text-align: center;
    padding: 8px;
    position: fixed;
    z-index: 10;
    bottom: 1%;
    font-size: 16px;
    line-height: 21px;
    border: 2px solid #30924A;
    .bank_deposit_order_text_close{
      float: right;
      text-align: right;
      position: relative;
      left: 25px;
      bottom: 25px;
      padding: 0px 3px;
      cursor: pointer;
      border-radius: 50%;
      background-color: #a9a9a9;
    }
    .bank_deposit_order_text{
      span{
        color: #30924A;
        text-transform: uppercase;
        font-weight: 500;
      }
    }
  }

.shipping_options {
  .shipping_option {
    padding: 8px 8px 0px 8px;
    .shipping_radio_button {
      font-size: 14px;
    }
    .shipping_option_price {
      float:right;
      font-size: 14px;
    }
  }
  .delivery_message {
    padding-left: 35px;
    font-size: 13px;
    color: $dark_red;
  }
}

.row .cashback-reminder {
  background-color: #eee;
  padding: 1.3em 1.6em;
  margin-bottom: 1em;
  text-align: center;
  border: 2px solid #bdbdbd;
}

#cancel-confirmation-modal {
  .cancel-reason-request {
    margin-bottom: 0.45em;
  }

  .cancel-helper-info {
    margin-top: 1.2em;
  }

  .cancel-order-reason-select-warning {
    font-size: 0.9em;
    color: #a94442;
    margin-top: 0.5em;
  }
}

#otp-verification-modal.reveal-modal {
  #otp-form-and-content {
    padding-top: 10%;
    font-size: 0.9em;
    text-align: center;
  }

  #otp-show-phone-details {
    font-size: 1.2em;
    font-weight: bold;
  }

  #otp-error-msg {
    display: none;
    color: red;
  }

  #otp-form {
    .cod-otp-input {
      width: 50%;
      font-size: 3em;
      font-family: monospace;
      padding: 1em 0em;
      text-align: center;
      color: #303030;
      background-color: inherit;
      border-width: 0 0 3px;
      margin: 2% auto;
    }

    #otp-submit-button {
      width: 50%;
      text-transform: uppercase;
    }

    #resend-otp {
      color: #670b19;
      display: inline;
      padding: 0;
      background: none;
    }
  }
}

.addon_form_link {
  padding: 0.5em;
  border: 2px solid #670b19;

  a {
    color: #670b19;
  }

  &.filled {
    border-color: #808080;
  }

  &.filled {
    a {
      color: #1a1a1a;
    }
  }
}

#returnStatusModal{
  height: auto;
  min-height: auto;
  max-height: 90%;
  overflow-y: auto;
}

#return-track-button{
  width: 100%;
}

#return_status{
  
  #horizontal_line {
    width:4px;
    height:40px;
    position: relative;
    top: 0%;
    z-index: 0;
    left: 50%;
    right: 0%;
    border-radius:0% !important; 
  }
  table{
    width: 100%;
    tr, td{
      text-align: center !important;
    }
  }
  .stage_name {
    top: 15px;
    font-weight:bold;
    font-size: 20px;
    color: #670b19;
  }
  .timestamp{
    display: block;
    font-size: 13px;
  }
  .note{
    display: block;
    font-size: 13px;
    color: lightslategrey;
  }
  .step_image{
    min-width: 35px;
  }
}
form .row .row .columns {
  padding: 0.3rem 1rem;
}
.cart_mini_info{
  .row{
    margin-bottom: 10px !important;
  }
}
.order_summary {
  background: #eee;
}
.wallet_payment {
  background: #eee;
  margin-bottom: 10px !important;
  padding: 15px 15px 0 15px;
}
.credit_card_information {
  background: #eee;
  padding: 10px;
  h5 {
    font-weight: bold !important;
    font-size: 18px;
  }
  p {
    font-size: 15px;
}
}
.checkout_message_icons {
  display: flex;
  justify-content: flex-end;
}

.document-upload {
  position: relative;
  padding: 1rem;
  background-color: #f7f7f7;
  font-size: 0.8rem;
  text-align: left;
  max-width: 1000px;
  margin: 0 auto;

  .za-label {
    display: block;
    font-weight: bold;
    padding-right: 10rem;
    margin-bottom: 0.5rem;
  }

  .za-note {
    display: block;
    color: red;
    font-size: 0.8rem;
    padding-right: 10rem;
  }

  .upload-button {
    position: absolute;
    top: 50%;
    right: 4rem;
    transform: translateY(-50%);
    background-color: #670b19;
    color: white;
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 0.8rem;
    text-decoration: none;
    white-space: nowrap;

    &:hover {
      background-color: #ca0101;
    }
  }
}

@media only screen and (max-width: 768px){
  .cart_mini_info{
    .row{
      margin-bottom: unset!important;
    }
  }
  .credit_card_information {
    margin-bottom: 20px !important;
  }
}
@media only screen and (min-width: 768px){
  .paymentSteps {
    display: none;
  }
  #create_orders_block{
    margin-top: 5rem;
    .order_total {
      margin-top: 0rem;
    }
  }
  #order_show_block {
    margin-top: 8rem;
  }
}
@media screen and (min-width:1024px)  {
  .sticky-button{
    position: relative;
    margin-bottom: 20px;
  }
}
@media only screen and (min-width: 64.0625em) {
  .large-block-grid-4 > li {
    list-style: none;
    width: 20%;
    &:nth-of-type(1n) {
      clear: none;
    }
    &:nth-of-type(4n+1) {
      clear: both;
    }
  }
}
@media screen and (min-width:1025px) {
  .sticky-desktop, .sticky-desktop-int{
    display: none;
  }
}
@media screen and (max-width:1024px) {
  .sticky-mobile,.sticky-mobile-int{
    display: none;
  }
  .upload-button {
    position: static;
    transform: none;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 1rem auto 0;
    max-width: 35%;
    text-align: center;
  }

  .za-label,.za-note {
    padding-right: 0;
  }
}
.designer_order_image {
  width: 100% !important;
}

.subscription-container {
  border: 1px solid #e0e0e0;
  padding: 20px;
  background-color: #f9f9f9;
  font-family: Arial, sans-serif;
  margin: 20px auto;
  padding-left: 20px;
  margin-right: 0px;
  margin-left: 0px;
}

input[type="checkbox"]#subscription_enabled {
  display: none;
}

.subscription-header {
  display: flex;
  align-items: center;

  input[type="checkbox"]#subscription_enabled {
    display: none;
  }

  label[for="subscription_enabled"] {
    position: relative;
    cursor: pointer;
    margin-right: 10px;
    margin-left: 0px;
    
    &::before {
      content: '';
      display: inline-block;
      width: 20px;
      height: 20px;
      border: 2px solid #ccc;
      border-radius: 4px;
      background-color: #fff;
      vertical-align: middle;
      transition: all 0.2s ease-in-out;
    }
  }

  input[type="checkbox"]#subscription_enabled:checked + label::before {
    background-color: #670b19;
    border-color: #670b19;
  }

  label[for="subscription_enabled"]::after {
    content: '\2713';
    position: absolute;
    top: 2px;
    left: 4px;
    color: white;
    font-size: 16px;
    line-height: 1;
    opacity: 0;
    transition: opacity 0.2s ease-in-out;
  }

  input[type="checkbox"]#subscription_enabled:checked + label::after {
    opacity: 1;
  }
}

.subscription-header {
  display: flex;
  align-items: center;
  margin-bottom: 10px;

  .fa {
    color: #4CAF50;
    margin-right: 10px;
  }

  h3 {
    margin: 0;
    font-size: 0.9em;
    font-weight: bold;
  }

  .saved-label {
    background-color: #d4edda;
    color: #155724;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.7em;
    margin-left: 10px;
    font-weight: bold;
  }
}

.subscription-details {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
  font-size: 0.9em;

  p {
    margin: 0;
    line-height: 1.4;
    color: #555;
  }

  .saved-amount {
    color: #4CAF50;
    font-weight: bold;
    font-size: 1em;
    text-align: right;
  }
}

.breakdown-link {
  margin-bottom: 15px;

  a {
    color: #555;
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }
}

.breakdown-details {
  display: none;
  table {
    width: 100%;
    border-collapse: collapse;

    td {
      padding: 10px !important;
      color: black;
      position: relative; 
    }

    .price {
      text-align: right;
    }

    .discount {
      color: #04c430;
    }
    .membership-fee-description {
      font-size: 0.85em;
      color: #555;
      display: block; 
      margin-top: 2px;
    }
    .final-payable {
      font-weight: bold;
      border-top: 1px solid #ccc;
      margin-top: 10px;
      padding-top: 10px;

      .price {
        font-size: 1.1em;
      }
    }
  }
}

.green-discount{
  color: #04c430 !important;
}

.alert-box.success {
  background-color: #dff0d8;
  color: #3c763d; 
  border: 1px solid #d6e9c6; 
  border-radius: 4px;
  padding: 10px;
}

.alert-box.success .emoji {
  font-size: 1.2em; 
  margin-right: 5px;
}
@import 'newly_added_products'

