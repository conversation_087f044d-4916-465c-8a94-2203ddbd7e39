// Place all the styles related to the StitchingMeasurement controller here.
// They will automatically be included in application.css.
// You can use Sass (SCSS) here: http://sass-lang.com/
body {
  font-family: "Inter", sans-serif !important;
}
#main-section{
  overflow-x: hidden !important;
}
.accordion{
  margin-left: 0px;
  .content{
    color: black;
  }
}

.ui-pnotify-title{
  color: black;
}
.ui-pnotify-text{
  font-size: 0.7em;
}
button.round, .button.round {
    border-radius: 10px;
}
.next-stage{
  background: #8f1b1d !important;
  text-transform: uppercase;
  font-size: 14px;
}
.img-check, .back-img-check{
  cursor:pointer;
}
.label{
  display: inline-block;
  font-weight: normal;
  line-height: 1;
  margin-bottom: auto;
  position: relative;
  text-align: center;
  text-decoration: none;
  text-align: left;
  white-space: nowrap;
  padding: 0.25rem 0.5rem 0.25rem;
  font-size: 0.875rem;
  background-color: #ffffff;
  color: #303030;
}

input[type="text"]:focus, input[type="text"]:hover, input[type="password"]:focus, input[type="password"]:hover, input[type="email"]:focus, input[type="email"]:hover, input[type="number"]:focus, input[type="number"]:hover, input[type="tel"]:focus, input[type="tel"]:hover, input[type="time"]:focus, input[type="time"]:hover, textarea:focus, textarea:hover, select:focus, select:hover{
  box-shadow: 0 1px 0 0 #670e19;
  border-bottom: 1px solid #670e19;
  background-color: white;
}
input[type="text"], input[type="password"], input[type="email"], input[type="number"], input[type="tel"], input[type="time"], textarea, select{
  margin: 0 0 0.5em 0;
  border: 0;
  border-bottom: 1px solid #9e9e9e;
  background-color: white;
  box-shadow: none;
  outline: none;
  &::-webkit-input-placeholder { /* Chrome/Opera/Safari */
    color: grey;
  }
  &::-moz-placeholder { /* Firefox 19+ */
    color: grey;
  }
  &:-ms-input-placeholder { /* IE 10+ */
    color: grey;
  }
  &:-moz-placeholder { /* Firefox 18- */
    color: grey;
  }
}
.off-canvas-wrap #container{
  width: 96vw;
  overflow-y: hidden;
  padding: 1em 0em;
  margin: 2%;
  margin-top: 2em;
  overflow-x: hidden;
}

.conversion{
  color: #c13e7f;
}

.mapping_error{
  color: red; 
}

.measurement_type{
  font-size: 0.8em;
}
.disable_accordion{
  color: white;
  display: block;
  font-family: "Helvetica Neue", Helvetica, Roboto, Arial, sans-serif;
  font-size: 1rem;
  padding: 1rem;
  background-color: grey;
}
.measurement_list{
  list-style: none;
}
.measurement_li{
  color: black;
  background-color: #d8d8d8;
  padding: 6px;
  margin-bottom: 4px;
  border-radius: 0.1em;
  text-align: center;
}
.other_button{
  webkit-appearance: none;
  -moz-appearance: none;
  border-radius: 0;
  border-style: solid;
  border-width: 0;
  cursor: pointer;
  font-family: "Helvetica Neue", Helvetica, Roboto, Arial, sans-serif;
  font-weight: normal;
  line-height: normal;
  margin: 0 0 1.25rem;
  position: relative;
  text-align: center;
  text-decoration: none;
  display: inline-block;
  padding: 0.6em !important;
  font-size: 1rem;
  background-color: #008CBA;
  border-color: #007095;
  color: #FFFFFF;
  margin-left: -6.5em !important;
  transition: background-color 300ms ease-out;
}

.login_hr:after {
  content: "OR"; /* section sign */
  color: #999;
  display: inline; /* for vertical centering and background knockout */
  background-color: white; /* same as background color */
  padding: 0 0.5em; /* size of background color knockout */
}
.login_hr {
  font-family: Arial, sans-serif; /* choose the font you like */
  text-align: center; /* horizontal centering */
  line-height: 1px; /* vertical centering */
  height: 1px; /* gap between the lines */
  font-size: 1em; /* choose font size you like */
  border-width: 1px 0; /* top and bottom borders */
  border-style: solid;
  border-color: #676767;
  margin: 20px 10px; /* 20px space above/below, 10px left/right */

  /* ensure 1px gap between borders */
  -webkit-box-sizing: content-box;
  -moz-box-sizing: content-box;
  -ms-box-sizing: content-box;
  -o-box-sizing: content-box;
  box-sizing: content-box;
}

.active_li{
  background-color: #670e19;
  color: white;;
}
.info{
  color: #670f19;
}

#loadingImage{
  position: fixed;
  left: 45%;
  top: 30%;
}

.btn-primary{
  border: antiquewhite;
  border-style: groove;
  }
.accordion .accordion-navigation > a:hover, .accordion dd > a:hover {
    background: black;
    color: white;
}
.accordion .accordion-navigation > a, .accordion dd > a {
  background: grey;
  color: white;
  display: block;
  font-family: "Helvetica Neue", Helvetica, Roboto, Arial, sans-serif;
  font-size: 1rem;
  padding: 1rem;

}

.accordion .accordion-navigation.active > a, .accordion dd.active > a{
  background-color: grey;
}
.product_type_btn{
  height: 12em;
  color: black !important;
  background-color: white;
  &:hover{
    background-color: #8c8c8c;
  }
}

.product_type_btn_bottom{
  height: 12em;
  color: black !important;
  background-color: white;
  &:hover{
    background-color: #8c8c8c;
  }
}
.selected_type{
  background-color: #8c8c8c;
}

@media only screen and (max-width: 40em) {
  .orbit-container{
    .orbit-next, .orbit-prev {
      display: block !important; 
    }
  }
}

.unfilled_measurements{
  color: red;
}
.table_head{
  background-color: grey;
  color: white;
  padding: 15px;
}
.selected_style{
  border-color: #670f19;
  border-style: double;
  border-width: 5px;
}

.touch .orbit-container .orbit-prev, .touch .orbit-container .orbit-next{
  background-color: rgba(97, 0, 0, 0.22) !important
}

@media only screen and (orientation: landscape) {
  .reveal-modal{  
    max-height: 70%;
    overflow-y: scroll;
  }
}

.view-measurements{
  .view-btn{
    margin-top: 10px;
    background-color: #8f1b1d !important;
  }
}

form.measurement-experience {
  h4 {
    padding: 1em;
    margin: 0;
    font-size: 1em;
    background: grey;
    color: white;
  }

  ul.measurement-experience-options {
    list-style-type: none;
    margin: 0;
    margin-top: 1em;

    border: 1px solid #d8d8d8;

    li {
      border-bottom: 1px solid #d8d8d8;

      &:last-child {
        border-bottom: none;
      }

      label {
        display: block;
        padding: 1em 1.4em;
        margin: 0;
      }

      input[type=radio] {
        display: none;

        &:checked + label {
          background: #670e19;
          color: white;
        }
      }
    }
  }

  dl.measurement-experience-options-explanation {
    margin-top: 1.5em;

    dt {
      font-size: 1.2em;
    }

    dd {
      margin-bottom: 1.2em;

      ul {
        margin-bottom: 0.6em;
      }
    }
  }

  input[type=submit] {
    display: block;
    margin: 1em auto 0;
  }
}
.mearsurement-page {
 padding: 67px 0 170px;
}
@media screen and (max-width:1399px) {
  .mearsurement-page {
    height: 100vh;
    overflow-y: auto;
}
}
@media (max-width: 1024px) {
  .foo-slider.blaze-slider {
    --slides-to-show: 1 !important;
  }
}
// video modal css
.youtube_measurement_button {
  ul {
    display: flex;
    width: 100%;
    list-style: none;
    justify-content: center;
    flex-wrap: wrap;
    padding: 0;
    margin: 0;
    .measurement_button {
      display: flex;
      align-items: center;
      background: #fff9fa;
      border-radius: 6px;
      margin-right: 15px;
      padding: 20px;
      margin-bottom: 15px;
      h5 {
        color: #3C4345!important;
        padding: 0;
        margin: 0 10px;
        font-size: 16px;
    }
  }
  }
  div#details {
    display: flex;
    justify-content: center !important;
  }
}
.mearsurement-page {
  padding: 67px 0;
 }
@media screen and (max-width:1399px) {
  .mearsurement-page {
    height: 100vh;
    overflow-y: auto;
}
}
.reveal-modal .close-reveal-modal {
  color: #670b19;
  top: 0;
  right: 14px;
}
@media only screen and (min-width: 30em) {
  .reveal-modal {
    top: 10.25rem;
    max-width: 41.5rem;
    overflow: hidden;
    text-align: center;
}
}
@media only screen and (max-width: 29.9375em) {
  .reveal-modal {
      width: 100%;
       min-height: unset;
      top: 12%;
      iframe{
        width: 100%;
      }
      .close-reveal-modal {
        right: 5px;
    }
  }
}