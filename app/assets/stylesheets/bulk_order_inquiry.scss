$bulk-primary-color: #cc2f28;
$bulk-secondary-color: #303030;
$mirraw-primary-color: #670b19;

.flex-center {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
}

.bulk-page-container {
    max-width: 1920px;

    .bulk-details {
        background-color: #f8f4f4;
        border-width: 30px 50px;
        border-style: solid;
        border-color: #efebeb;
        max-width: 1420px;
        padding: 50px;

        .content {
            &:first-child {
                padding-bottom: 30px;
            }

            &+.content {
                padding-top: 30px;
                padding-bottom: 30px;
            }

            &:last-child {
                padding-top: 30px;
            }
        }

        h3 {
            color: $bulk-primary-color;
        }

        .bulk-icon-container {
            color: $bulk-primary-color;
            display: flex;
            justify-content: space-around;
            flex-wrap: wrap;
            filter: brightness(0.5);
        }

        a {
            color: #ffffff;
            background-color: $bulk-primary-color;
            width: 200px;
            height: 50px;

            &:hover {
                background-color: $mirraw-primary-color;
                transition: background-color 0.5s;
            }
        }

        .bulk-steps {
            h5 {
                color: $bulk-primary-color;
            }
        }

        p {
            font-size: 16px;
        }

        .bulk-services {
            h5 {
                color: $bulk-primary-color;
            }

            .b-text {
                font-size: 18px;
                font-weight: 600;
            }
        }
    }
}

@media screen and (max-width: 991px) {
    .bulk-page-container {
        .bulk-details {
            border-width: 30px 30px;
            padding: 30px;
        }
    }
}

@media screen and (max-width: 540px) {
    .bulk-page-container {
        .bulk-details {
            border-width: 30px 20px;
            padding: 20px;
        }
    }
}
