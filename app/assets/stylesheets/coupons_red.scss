@import 'variables_red';
@import 'red_theme';

$background-colors: "#53cde5" , "#cd8cf3" , "#f396bd" , "#f79a71" , "#9fb7ff" , "#9de4ad" , "#d399eb" , "#c0a99f";
$offwhite_color: #e7e7e7;
body {
  font-family: "Inter", sans-serif !important;
}
#coupons-show{
  margin: 0 auto;
  max-width: 65.5rem;
  width: 100%;
  padding: 16px 0px;
  #title-block{
    h1{
      color: $text_black;
    }
    p{
      font-style: italic;
      color: $text_light_gray;
      margin-top: 10px;
      text-align: justify;
    }
  }
  .coupon-data {
    position: relative;
    display: block;
    width: 85%;
    height: 218px;
    margin: 18px auto;
    border-radius: 2px;
    background: #f9f9f9;
    box-shadow: $card_box_shadow;
    text-align: center;
    -webkit-transition: overflow 0.5s;
    transition: overflow 0.5s;
    .ribbon {
      position: absolute;
      display: block;
      top: -14px;
      right: -18px;
      width: 181px;
      height: 50px;
      overflow: hidden;
    }

    .ribbon .label {
      position: relative;
      display: block;
      left: 5px;
      top: 0px;
      width: 165px;
      padding: 10px 0;
      font-size: 15px;
      text-align: center;
      color: #fff;
      background-color: $light_red;
      border-radius: 3px 0 0 3px;
      text-shadow: -1px -1px 2px #504e4e;
      -webkit-box-shadow: 0px 0px 4px rgba(0,0,0,0.3);
      -moz-box-shadow: 0px 0px 4px rgba(0,0,0,0.3);
      -ms-box-shadow: 0px 0px 4px rgba(0,0,0,0.3);
      box-shadow: 0px 0px 4px rgba(0,0,0,0.3);
    }

    .label:after {
      content: '';
      position: absolute;
      bottom: -8px;
      border-top: 8px solid $dark_red;
      border-right: 7px solid transparent;
    }

    .label:after {
      right: 0;
    }

    .title {
      display: block;
      font-size: 20px;
      color: #540c5d;
      text-transform: capitalize;
      margin-top: 10px;
      text-align: center;
      float: none;
    }
    .coupon-code{
      display: inline-block;
      font-size: 20px;
      color: #131313;
      text-align: center;
      width: 135px;
      padding: 2px 6px;
      border: 2px dashed #131313;
      text-shadow: -1px -1px 1px #656565;
      margin: 45px 0px 10px 0px;
    }
    .description{
      display: block;
      font-size: 12px;
      color: #29982d;
      font-style: italic;
      font-weight: 700;
      margin-top: 7px;
    }
    .shop-now {
      display: block;
      position: absolute;
      bottom: 0;
      width: 100%;
      line-height: 40px;
      text-shadow: -1px -1px 2px #504e4e;
      border-radius: 0px 0px 2px 2px;
      background-color: $dark_red;
      .c-button{
        cursor: pointer;
        display: inline-block;
        color: #efeced !important;
        padding: 0.2em 1.5em;
        font-size: 18px;
        width: 100%;
        font-weight: 700;
      }
    }
  }

  .coupon-data:before {
    content:"";
    position: absolute;
    top: 2%;
    left: 2%;
    height: 10px;
    width: 10px;
    border-radius: 50%;
    background-color: #424242;
  }
}

.pages_mirraw_coupons{
  #container{
    margin-top: 2em;
  }
}

@each $background-color in $background-colors{
  $i: index($background-colors, $background-color);
  .coupon-#{$i}{
    background-color: #{$background-color};
  }
}
.coupon-0{
  background-color: #3d00ff47;
}
#mirraw-coupons-show{
  #title-block{
    border: dashed $offwhite_color;
    width: 85%;
    color: $offwhite_color;
    h1{
      color: $offwhite_color;
      text-transform: uppercase;
      font-size: 30px;
      font-family: initial;
      margin-bottom: 0px;
      line-height: 30px;
    }
  }
  .scissor{
    background: image_url("scissor_img.png") no-repeat;
    height: 35px;
    margin-top: -13px;
    margin-left: 7px;
    background-size: 35px;
  }
  #deal_ends{
    color: $offwhite_color;
    text-align: center;
    margin-bottom: 5px;
    margin-top: -5px;
    .copuon-label{
      width: 50%;
      margin: 0px auto;
      color: darkorange;
      font-size: 30px;
      font-weight: bold;
    }
    #countdown{
      text-align: center;
      #deal_text{
        line-height: 20px;
      }
      .deal_timer{
        width: 50px;
        height: 50px;
        color: darkorange;
        padding: 8px;
        font-weight: bold;
        font-size: 35px;
        margin: 10px 5px;
        border-radius: 3px;
        letter-spacing: 1px;
      }
      .time-sep{
        font-size: 35px;
        font-weight: bold;
        letter-spacing: 2px;
      }
      .clock_label{
        display: flex;
        width: 72%;
        margin: 0px auto;
        line-height: 0px;
        .hr, .min, .sec{
          display: table;
          margin: 7px 30px;
          font-size: 12px;
          font-weight: bold;
          letter-spacing: 1px;
          color: $offwhite_color;
        }
      }
    }
  }
  #table-title{
    .coupon-data-container{
      padding: 10px 0px;
      margin-bottom: 14px;
    }
    .coupon_title{
      text-align: center;
      color: $offwhite_color;
    }
    .coupon-expired-label{
      width: 200px;
      height: 80px;
      background: white;
      margin-top: 18px;
      position: absolute;
      display: block;
      z-index: 1;
      opacity: 0.8;
      -webkit-transform: rotate(-25deg);
      -moz-transform: rotate(-25deg);
      -o-transform: rotate(-25deg);
      -ms-transform: rotate(-25deg);
      transform: rotate(-25deg);
      margin-left: 10px;
      border-radius: 3px;
      .coupon-expired-border{
        border: 4px solid #333333;
        margin: 10px;
        border-radius: 5px;
        .copupon-expired-text{
          text-align: center;
          font-size: 34px;
          font-weight: bold;
          color: rgb(1,56,67);
          -webkit-text-stroke: 1px;
          letter-spacing: 1px;
        }
      }
    }
    .coupon-data{
      display: flex;
      .coupon-code-container{
        background: image_url("coupon_bg.png") no-repeat;
        background-size: 100%;
        height: 100%;
        margin-left: 15px;
        .coupon-code{
          text-align: center;
          font-size: 20px;
          padding: 30px;
          font-weight: bold;
          color: #333333;
        }
      }
      .description{
        color: white;
        font-weight: bold;
      }
      .coupon-off{
        color: rgb(1,56,67); 
        font-size: 35px;
        font-weight: bold;
      }
    }
    .coupon-left-bar{
      display: flex;
      .coupon-left{
        height: 0px;
        border: 3px solid #2b3749;
        margin-top: 10px;
        margin-left: 30px;
        border-radius: 5px;
        .coupon-used{
          border: 4px solid #e7e7e7;
          margin-top: -4px;
          border-radius: 4px;
          margin-left: -3px;
        }
      }
      .coupon-left-text{
        font-size: 14px;
        color: white;
        font-weight: bold;
        margin-top: 3px;
        margin-left: 7px;
      }
    }
  }
}