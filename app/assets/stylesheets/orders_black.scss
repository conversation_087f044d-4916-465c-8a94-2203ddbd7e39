// Place all the styles related to the Orders controller here.
// They will automatically be included in application.css.
// You can use Sass (SCSS) here: http://sass-lang.com/
@import 'variables';
@import 'black_theme';
body {
  font-family: "Inter", sans-serif !important;
}
.modal-open {
  overflow: hidden !important;
}
#container{
  margin-top: 0.5em !important;
}

#codModal{
  overflow-y: scroll;
  background-color:$body_white;
  top: 0 !important;
  position: fixed;
  width: 100%;
  height: 100%;
  border: 0;
  #cod-confirmation-message{
    color: $text_black;
  }
  #cod-confirmation-message{
    margin-top: 5%;
  }
  .close-reveal-modal{
    font-size: 1.8em;
    padding: 1% 4% 3% 10%;
    right: 0.3em;
    color: white;
  }
  .cod-otp-verification-form{
    font-size: 0.9em;
    text-align: center;
    padding-top: 10%;
    .error-msg-for-otp{
      display: none;
      color: red;
    }
    .cod-otp-input{
      width: 50%;
      font-size: 1.2em;
      text-align: center;
      color: $text_black;
      background-color: inherit;
      border-width: 0 0 3px;
      margin: 5% auto;
    }
    #otp-phone-change-form-and-content{
      display: none;
    }
    .cod-otp-modal-buttons{
      width: 50%;
    }
    #otp-phone-text-value{
      border: 0;
      padding: 0;
      background: 0;
      width: auto;
      color: white;
      font-size: 1.2em;
      font-weight: 800;
      clear: both;
      margin: 0;
      display: inline;
    }
    #phone-change-button{
      font-size: 0.9em;
      margin: 0;
    }
    .button-as-text[type=button]{
      background: 0;
      display: inline;
      padding: 0;
      color: $link_blue;
    }
  }
  .footer
  {
    margin-top:40px;
    float:right;
    #successButton
    {
      background: linear-gradient(to bottom, #0E9A7D, #267363);
      border-radius: 0.1rem;
      font-size: 1rem;
      padding: 18px 52px;
    }
  }
}

#paypalSuccessModal{
  -webkit-text-size-adjust: 100%;
  background: transparent;
  padding: 0px;
  border: 0px;
  #paypalModal{
    background: $menu_background;
    width: 90%;
    margin-right: auto;
    margin-left: auto;
    .modal-body{
      padding: 15px;
      #error-message{
        .error-text{
          font-weight: bold;
          margin-bottom: 10px;
        }
        .paypal-success-reasons{
          margin-left: 10px;
        }
      }
      .text-message{
        margin-bottom: 15px;
      }
      p{
        margin-top: 10px;
        margin-bottom: 10px;
        text-align: center;
      }
      .close-modal{
        float: right;
        line-height: 0px !important;
        font-size: 30px;
        color: $text_white;
        margin-right: -10px;
      }
    }
    .modal-footer{
      text-align: center;
      .retry-payment{
        font-weight: bold;
      }
      .cancel{
        color: white;
        background-color: transparent;
      }
    }
  }
}


#get_app_link{
  display: none;
  .panel{
    background-color: $snow_white;
    box-shadow: $card_box_shadow;
    padding-right: 8px;
    h1{
      color: $text_black;
    }
    .close{
      float: right;
      border-radius: 50%;
      background-color: transparent;
      padding: 2px 6px;
      color: $link_blue;
      font-weight: bold;
    }
    p{
      padding-right: 2rem;
      text-align: justify;
      color: $text_black;
    }
    .success{
      border-radius: 4px;
      background-color: $body_white;
      padding: 12px;
      color: $text_black;
      box-shadow: $card_box_shadow;
      .fi-social-android{
        font-size: 30px;
        color: #a4c639;
        vertical-align: middle;
      }
      .fi-social-apple{
        font-size: 30px;
        color: #cdcdcd;
        vertical-align: middle;
      }
    }
  }
}

#create_orders_block{
  label {
    color: inherit;
  }
  .accordion {
    padding-left: 0em;
    margin-left: 0em;
    .accordion-navigation {
      > a {
        background: inherit;
        color: inherit;
        padding: 0rem;
        &:before {
          content: '»';
          float: left;
          color: $link_blue;
          font-size: 14px;
          font-weight: 700;
          padding-right: 2px;
        }
      }
      > .content {
        padding: 0rem;
        &.active {
          background: inherit;
        }
      }
      &.active > a:before {
        content: '«';
      }
    }
  }
  .item_block {
    border-bottom: 0.1em solid #dcdcdc;
    margin-bottom: .5em;
    color: $text_light_gray;
    font-size: 14px;
    .truncate{
      color: $text_light_gray;
    }
  }
  ul.addons-notes{
    font-size: 14px;
    padding-right: 0;
    li.accordion-navigation{
      a{
        font-size: 14px;
      }
      .text-right{
        float: right;
      }
      .content{
        .row{
          .left{
            margin-left: 5px;
          }
        }
      }
    }
  }
  .order_total {
    padding: 0.2em 1em;
    margin-top: 1em;
  }
  #totals_block{
    color: $text_light_gray;
    font-size: 14px;
    .row{
      .columns{
        padding-right: 0px;
      }
    }
  }
  .grand_total, .grand_total_with_cod{
    color: $text_black;
    font-size: 14px;
    font-weight: 600;
  }
  .shipping_address {
    margin-bottom: 2em;
    padding: 1em;
    .address-text{
      width: 100%;
      font-size: 14px;
      color: $text_light_gray;
    }
    table {
      background: inherit;
      border: none;
      margin-bottom: 0em;
      tr {
        &:nth-of-type(even) {
          background: inherit;
        }
        td {
          color: inherit;
          padding: 0.1em;
        }
      }
    }
  }
}

#order_show_block {
  .order-ack-title{
    color: $text_black;
  }
  .panel_block {
    .panel_content {
      padding: 1em;
    }
  }
  hr {
    border: $gray solid;
    border-width: 0.1em 0 0;
  }
  table {
    width: 100%;
    border: 0em;
    background: inherit;
    tr {
      background: inherit;
      td, th {
        color: inherit;
        line-height: 1em;
        padding: 0.3em;
      }
    }
  }
  .line_item_details {
    font-size: 14px;
    padding: 0.4em;
    opacity: 0.8;
    background-color: $snow_white;
    color: $text_black;
    border: none;
  }
  .designer-order-block{
    background-color: $body_white;
    .panel_content{
      background-color: $body_white;
    }
  }
}

.notice_class {
  padding-left:10px;
  color:orange;
  font-size:14px;
  line-height:15px;
}

.quantity_total {
  margin-right: 0em;
}

table.customer_order {
  border: 1px solid $gray;
  border-collapse: collapse;
}

table.customer_order tr {
  td {
    border: 1px solid $gray;
    color: white;
  }
  th {
    border: 1px solid $gray;
    color: white;
  }
  td.cost {
    text-align: right;
  }
}

table.customer_order tr{
  background: #333333;
  color: white;
}


/* The Modal (background) */
.modalForm {
  display: none; /* Hidden by default */
  position: fixed;
  z-index: 999;
  -webkit-transform: translate3d(0,0,0);
  transform: translate3d(0,0,0);
  padding-top: 80px;
  left: 0;
  top: 0;
  width: 100%;
  height: 800px;
  background-color: rgb(0,0,0);

  /* Modal Content (Iframe) */
  .modal-content {
    margin: auto;
    display: block;
    width: 80%;
    max-width: 100%;
    -webkit-animation-name: zoom;
    -webkit-animation-duration: 0.6s;
    animation-name: zoom;
    animation-duration: 0.6s;
  }

  @-webkit-keyframes zoom {
    from {transform:scale(0)} 
    to {transform:scale(1)}
  }

  @keyframes zoom {
    from {transform:scale(0)} 
    to {transform:scale(1)}
  }
  /* The Close Button */
  .close {
    position: absolute;
    top: 25px;
    right: 5px;
    color: #f1f1f1;
    font-size: 40px;
    font-weight: bold;
    transition: 0.3s;
  }

  .close:hover,
  .close:focus {
    color: #bbb;
    text-decoration: none;
    cursor: pointer;
  }
  /* 100% Image Width on Smaller Screens */
  @media only screen and (max-width: 700px){
    .modal-content {
      width: 100%;
    }
  }

  @media only screen and (min-width: 1200px){
    .modal-content {
      width: auto;
      height: auto;
    }
  }
}

/* Order status on order show page */
#order_status {
  float:left;
  margin-left: 1%;

  #circle_div {
    position: relative;
    float:left;
    width: 16%;

    .circle_stage {
      display: inline-block;
      border-radius:50%;
      -moz-border-radius: 50%;
      -webkit-border-radius: 50%;
      -o-border-radius: 50%;
      font-size:12px;
      line-height:50px;
      text-align:center;
      border: 2px solid;
    }
    .circle_stage_domestic{
      display: inline-block;
      border-radius: 50%;
      -moz-border-radius: 50%;
      -webkit-border-radius: 50%;
      -o-border-radius: 50%;
      color: black;
      text-align: center;
      border: 2px solid white;
      font-size: 0.7em;
      vertical-align: middle;
    }
    .square_stage {
      display: inline-block;
      font-size:12px;
      line-height:50px;
      text-align:center;
      border: 2px solid;
    }

    .step_image {
      background: transparent;
      position: relative;
      top: -4px;
    }

    .step_number {
      position: relative;
      top: -14px;
      color: black;
    }

    .vertical_line {
      width:6px;
      height:40px;
      margin-left: 52px;
      margin-bottom: -8px;
      display: inline-block;
      border: 1px solid
    }
  }

  #notes_div {
    width: 82%;
    margin-left: 80px;

    .status_note {
      margin-left: 18px;
      color: $text_black;
      padding-top: 2px;
      font-size:small;
      margin-top: -3px;
      margin-bottom: 25px;
    }
  }
}

.all_order_stages{
  display: inline-block;
  .horizontal-line{
    width: 2em;
    height: 0.3em;
    background: yellowgreen;
    border: 1px solid white;
    border-radius: 1px;
    float: right;
  }
  .base {
  background: yellowgreen;
  display: inline-block;
  height: 1em;
  margin-left: 5%;
  position: relative;
  width: 2.5em;
  float: left;
  margin-right: 5%;
  .pointer {
    border-left: 15px solid yellowgreen;
    border-top: 8px solid transparent;
    border-bottom: 8px solid transparent;
    content: "";
    height: 0;
    left: 40px;
    position: absolute;
    top: 0px;
    width: 0;
  }
  .uc_base{
    font-size: x-small;
    color: black;
    padding: 2px;
  }
}
}
.status_text{
  margin: 2% 10% 0% 10%;
  width: 80%;
  text-align: center;
  background-color: #E8C55B;
  border: 2px solid #D4A22E;
  color: black;
  border-radius: 2px;
  font-size: 0.8em;
}
#return_panel{
  .bordered_block{
    background-color: transparent;
    box-shadow: none;
    .policy_button{
      background-color: $snow_white;
      box-shadow: $card_box_shadow;
      color: $text_black;
    }
  }
}
/* Return Button */
body.modal-open{
  overflow: hidden;
  position: fixed;
}

.reveal-modal {
  overflow-y: auto;
  height: 100%;
  position: fixed;
  top: 20px !important;

  .close-reveal-modal {
    color: $gray;
  }
}

#bank_deposit_order_box {
    display: none;
    margin-right: 30px;
    background-color: #cecece;
    color: #000000;
    text-align: center;
    padding: 8px;
    position: fixed;
    z-index: 10;
    bottom: 1%;
    font-size: 16px;
    line-height: 21px;
    border: 2px solid #30924A;
    .bank_deposit_order_text_close{
      float: right;
      text-align: right;
      position: relative;
      left: 25px;
      bottom: 25px;
      padding: 0px 3px;
      cursor: pointer;
      border-radius: 50%;
      background-color: #a9a9a9;
    }
    .bank_deposit_order_text{
      span{
        color: #30924A;
        text-transform: uppercase;
        font-weight: 500;
      }
    }
  }

.shipping_options {
  .shipping_option {
    padding: 8px 8px 0px 8px;
    .shipping_radio_button {
      font-size: 14px;
    }
    .shipping_option_price {
      float:right;
      font-size: 14px;
    }
  }
  .delivery_message {
    padding-left: 35px;
    font-size: 13px;
    color: $link_blue;
  }
}