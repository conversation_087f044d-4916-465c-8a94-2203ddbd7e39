@import "variables_red";

@mixin flexWrap() {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

@mixin flexbox($wrap: wrap, $justify: space-between, $align: center) {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;

  -ms-flex-wrap: $wrap;
  flex-wrap: $wrap;

  -webkit-box-align: $align;
  -ms-flex-align: $align;
  align-items: $align;

  -webkit-box-pack: $justify;
  -ms-flex-pack: $justify;
  justify-content: $justify;
}

@mixin fitCover() {
  -o-object-fit: cover;
  object-fit: cover;
  -o-object-position: top;
  object-position: top;
}

@mixin transition($args...) {
  -webkit-transition: $args;
  -o-transition: $args;
  transition: $args;
}

@mixin transform($value) {
  -webkit-transform: ($value);
  -moz-transform: ($value);
  -ms-transform: ($value);
  transform: ($value);
}

@mixin translateCenter() {
  top: 50%;
  left: 50%;
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

@mixin textCut() {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

@mixin lato_medium() {
  font-family: "Inter", sans-serif;
  font-weight: 500;
  font-style: normal;
}

@mixin raleway_semibold() {
  font-family: "Inter", sans-serif;
  font-weight: 500;
}

@mixin scriptMT_bold() {
  font-family: "Inter", sans-serif;
  font-weight: bold;
  font-style: normal;
}

@mixin extrabold() {
  font-family: "Inter", sans-serif !important;
  font-weight: bold;
}

@mixin semibold() {
  font-family: "Inter", sans-serif;
  font-weight: 600;
}

@mixin regular() {
  font-family: "Inter", sans-serif;
  font-weight: normal;
}

@mixin bold() {
  font-family: "Inter", sans-serif;
  font-weight: bold;
}

@mixin medium() {
  font-family: "Inter", sans-serif;
  font-weight: 500;
}

@mixin layoutMax() {
  margin: 0 auto !important;
  max-width: 1366px !important;
}

@mixin commonHeading() {
  font-size: 36px;
  color: $black;
  text-transform: uppercase;
  @include semibold();
}

@mixin commonBuyBtn($width: auto, $fs: 14px, $pd: 10px 15px) {
  width: $width;
  font-size: $fs;
  color: #1f1f1f;
  padding: $pd;
  border-radius: 6px;
  background: $white;
  text-transform: uppercase;
  border: 1px solid #1f1f1f;
  @include bold();
}

@mixin imgFluid() {
  width: 100%;
  height: 100%;
  max-width: 100%;
}

@mixin flc() {
  &::first-letter {
    text-transform: uppercase !important;
  }
}

@mixin btnReset() {
  border: none;
  outline: none;
  box-shadow: none;
}

@mixin moveUp() {
  -webkit-transition: -webkit-transform 0.1s ease;
  transition: -webkit-transform 0.1s ease;
  transition: transform 0.1s ease;
  transition: transform 0.1s ease, -webkit-transform 0.1s ease;

  &:hover {
    -webkit-transform: translateY(-2px);
    transform: translateY(-2px);
  }
}

@mixin iplaceholder($color: $grey) {
  @include transition(border 0.3s);

  &::-webkit-input-placeholder {
    opacity: 0.24;
    color: $color;
  }

  &::-moz-placeholder {
    opacity: 0.24;
    color: $color;
  }

  &:-ms-input-placeholder {
    opacity: 0.24;
    color: $color;
  }

  &:-moz-placeholder {
    opacity: 0.24;
    color: $color;
  }
}

@mixin textNowrap() {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

@mixin modalBottomDrawer() {
  &.modal {
    &.drawer {
      .modal-dialog {
        bottom: 0;
        flex: auto;
        margin: 0px;
        display: flex;
        min-height: 150px;
        position: absolute;
        border-top-left-radius: 16px;
        border-top-right-radius: 16px;
        @include transform(translate(0, 0));

        .modal-content {
          border: none;
          border-radius: 0px;
          border-top-left-radius: 16px;
          border-top-right-radius: 16px;

          .modal-body {
            overflow: auto;
          }
        }
      }

      &:not(.show) {
        .modal-dialog {
          @include transform(translate(0%, 100%));
        }
      }
    }
  }
}

$media-small: 'only screen and (max-width: 767px)';

$black: #000000;
$white: #ffffff;
$lightRed: #f56a6a;
$green: #1daf3a;
$black1: #1f1f1f;
$grey: #636466;
$lightGrey: #9d9d9d;

// All Z-Index
$zIdxMobileHeader: 100;
$zIdxSideNavOverlay: 110;
$zIdxSideNav: 120;
$zIdxSortByBtn: 130;
$zIdxEditProfileSaveBtn: 130;
$zIdxAddNewOrEditAddressSaveBtn: 130;
$zIdxModalBottomBtn: 130;
$zIdxAddressContinueBtn: 130;
$zCartPlaceOrderBtn: 130;
$zIdxAddressActionBtnOverlay: 140;
$zIdxSortByOverlay: 140;
$zIdxLogoutOverlay: 140;
$zIdxSortByDrawer: 150;
$zIdxLogoutDrawer: 150;
$zIdxProductFilterComponent: 160;
$zIdxProductDetailPageAddCartBtn: 160;
$zIdxWhatsappBtn: 160;
$zIdxDesktopHeader: 99; // Because Bootstrap dropdowna and modal
$zIdxCartRightSidenavOverlay: 1035;
$zIdxCartRightSidenavDrawer: 1040;

body {
  font-family: "Inter", sans-serif !important;
}

.desktop-header-component-container {
  left: 0;
  right: 0;
  z-index: 999;
  display: none;
  height: 100px;
  position: fixed;
  background-color: rgba(255, 249, 250, 1);
  box-shadow: 0px 3px 6px #002daa14;

  .offer-deal-message-wrappper {
    padding: 5px;
    font-size: 14px;
    background-color: #fff;
    @include medium();
    @include flexbox($justify: center);

    .message-block {
      @include flexbox($justify: center);
    }

    .offer-txt {
      display: inline-block;
    }

    .offer-countdown-block {
      margin-left: 8px;
      padding: 2px 12px;
      border-radius: 4px;
      display: inline-block;
      background: transparent linear-gradient(180deg, rgba(255, 212, 90, 0.22) 0%, rgba(255, 172, 34, 0.22) 100%) 0% 0% no-repeat padding-box;

      .deal-end-txt {
        font-size: 12px;
      }
    }
  }

  .header-with-searchbar-wrapper {
    font-family: "Inter", sans-serif;
    background-color: rgba(255, 249, 250, 1);
    border-bottom: 1px solid #ECEDED;
    display: flex;
    justify-content: space-between;
    padding: 10px 3rem;
    width: 100%;
    max-width: 1920px;

    .empty-container {
      width: 460px
    }

    .search-and-profile-container {
      display: flex;
      align-items: center;
      gap: 3rem;

      .search-box {
        height: 40px;
      }
    }

    .logo-box {
      min-width: 130px;

      .logo-img {
        width: 130px;
        height: 56px;
      }
    }

    .actions-box {
      display: flex;
      align-items: center;
      gap: 2rem;

      .logo-icon,
      .logo-wrap {
        padding-top: 5px
      }

      .action-btn {
        .inactive-icon {
          display: block;
          position: relative;
        }

        .active-icon {
          display: none;
        }

        &.active-action-btn {
          .active-icon {
            display: block;
          }

          .inactive-icon {
            display: none;
          }
        }
      }
    }

    .searchbar-icon-wrapper {
      margin: 0 0 0 auto;
      position: relative;
      border-radius: 8px;
      width: 250px;
      height: auto;

      form {
        width: 100%;

        .search-css {
          display: flex;
          margin: 0;
          width: 100%;

          .postfix {
            margin: 0;
            padding: 0
          }
        }
      }


      .searchbar-control {
        width: 100%;
        border: none;
        color: $black;
        outline: none;
        font-size: 16px;
        border-radius: 8px;
        padding: 10px 16px;
        padding-right: 70px;
        background: #fff;
        transition: all 0.2s linear;
        @include medium();
        border: 1.5px solid #fff;

        &::-webkit-input-placeholder {
          opacity: 0.24;
          color: #000000cc;
        }

        &::-moz-placeholder {
          opacity: 0.24;
          color: #000000cc;
        }

        &:-ms-input-placeholder {
          opacity: 0.24;
          color: #000000cc;
        }

        &:-moz-placeholder {
          opacity: 0.24;
          color: #000000cc;
        }

        &:focus {
          background-color: $white;
          border: 1.5px solid #fff;
        }
      }

      .svg-search {
        right: 20px;
        bottom: 15px;
        position: absolute;
      }
    }
  }

  .header-mega-menu-wrapper {
    background-color: white;
    border-bottom: 1px solid rgba(236, 237, 237, 1);
    position: relative;
    width: 100%;

    .nav-link-wrap {
      display: flex !important;
      width: 100%;
      max-width: fit-content;
      position: relative;
      margin: 0 auto;
      justify-content: center !important;
      align-items: self-end !important;

      .nav-link-list {
        margin: 0;
        list-style-type: none;

        &:hover {
          .mega-menu-wrapper {
            display: block;
          }
        }
      }

      .action-link {
        font-size: 14px !important;
        margin-right: 5px;
        color: rgba(60, 67, 69, 1);
        padding: 9px 11px 10px 11px;
        display: inline-block;
        text-decoration: none;
        @include raleway_semibold();
        border-bottom: 2px solid transparent;

        &.active--link {
          color: #B10D28;
          border-bottom: 2px solid #B10D28;
        }

        &:hover {
          border-bottom: 2px solid #B10D28;
        }
      }

      .sale-label {
        margin: 0 15px;
        background-color: $black1;
        -webkit-transform: skewX(-24deg);
        -ms-transform: skewX(-24deg);
        transform: skewX(-24deg);

        .action-link {
          color: $white;
          font-size: 16px;
          padding: 2px 15px;
          letter-spacing: 1px;
          -webkit-transform: skewX(30deg);
          -ms-transform: skewX(30deg);
          transform: skewX(30deg);
          @include scriptMT_bold();

          &:hover {
            border-bottom: 2px solid transparent;
          }
        }
      }
    }

    .mega-menu-wrapper {
      left: 0%;
      width: 100%;
      display: none;
      position: absolute;
      background: $white;
      box-shadow: 0px 10px 10px #002daa14;

      .main-flex-container {
        padding: 0 0;
        @include flexbox($align: normal, $justify: normal);

        .category-box {
          &:nth-child(even) {
            background-color: #ECEDED;
          }
        }
      }

      .flex-item.product-card-box {
        width: 40%;
        background-color: $white !important;
        @include flexbox($align: normal, $wrap: nowrap);

        .p-card {
          width: 50%;
          padding: 0 15px;
        }

        .p-img-box {
          height: 275px;
        }

        .p-card-img {
          @include fitCover();
        }

        .p-card-name {
          color: $black1;
          @include semibold();
          font-size: 20px;
          margin-top: 5px;
        }

        .p-card-des {
          font-size: 14px;
          color: $grey;
          @include regular();
          @include textCut();
        }

        .p-card-amount {
          font-size: 20px;
          color: $black1;
          @include semibold();

          .currency-icon {
            font-size: 15px;
          }
        }
      }

      .category-box {
        position: relative;
        padding-left: 10px;
        margin-bottom: 10px;
        padding-right: 10px;
        padding-bottom: 10px;

        &>li {
          list-style-type: none;
        }

        &:last-child::after {
          display: none;
        }
      }

      .parent-category-name {
        display: block;
        font-size: 15px;
        text-align: left;
        color: $black1;
        padding-bottom: 10px;
        padding-top: 14px;
        text-decoration: none;
        @include semibold();
        color: #670B19;
      }

      .child-category-name {
        display: block;
        padding: 0 0;
        font-size: 13px;
        text-align: left;
        color: #00000099;
        text-decoration: none;
        text-transform: capitalize;
        @include raleway_semibold();

        &:hover {
          @include extrabold();
        }
      }
    }
  }

  @media only screen and (min-width: 1000px) {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  @media screen and (min-width: 1023px) and (max-width: 1299px) {
    .header-mega-menu-wrapper {
      .nav-link-wrap {
        .action-link {
          padding: 9px 4px 10px 4px;
        }
      }

      .mega-menu-wrapper .category-box {
        padding-left: 10px;
        padding-right: 10px;
        margin-left: 10px;
        margin-right: 5px;
      }
    }
  }

  @media only screen and (min-width: 1000px) and (max-width: 1280px) and (max-height: 650px) {
    .header-mega-menu-wrapper {
      .nav-link-wrap {
        .action-link {
          font-size: 13px !important;
        }

        .nav-link-list:hover {
          .mega-menu-wrapper {
            overflow: auto;
            height: fit-content;

            .parent-category-name {
              font-size: 14px
            }

            .child-category-name {
              font-size: 12px
            }

            .gap-div {
              margin: 0.25rem 0 0.1875rem;
            }
          }
        }
      }
    }
  }

  @media only screen and (max-height: 480px) {
    .header-mega-menu-wrapper {
      .nav-link-wrap {
        .action-link {
          font-size: 12px !important;
        }

        .nav-link-list:hover {
          .mega-menu-wrapper {
            overflow: auto;
            height: fit-content;

            .parent-category-name {
              font-size: 12px
            }

            .child-category-name {
              font-size: 10px
            }

            .gap-div {
              margin: 0.25rem 0 0.1875rem;
            }
          }
        }
      }
    }
  }
}

.footer-component-wrapper {
  background-color: $white;

  .footer-section-one {
    padding: 40px 0 30px;
    background-color: #f8faff;

    .accordion-wrappper {
      .accordion-item {
        background: transparent;
        border: none;
      }

      .accordion-body {
        padding: 0;
      }
    }

    .desktop-accordion-btn {
      margin-bottom: 0;
      padding: 0;
    }

    .f-link {
      font-size: 20px;
      background: transparent;
      border-bottom: none;
      box-shadow: none;
      text-transform: uppercase;
      @include lato_medium();
      font-weight: 600;
    }

    .list-item {
      list-style-type: none;
      @include lato_medium();
    }

    .a-link {
      font-size: 16px;
      margin: 0px;
      display: block;
      padding-bottom: 4px;
      color: #ffffff;
    }

    .icon-box {
      margin-right: 10px;

      i {
        font-size: 15px;
      }
    }
  }

  .footer-section-two {
    background-image: image_url('theme_bg.png');
    padding: 1px 0 2px 0;

    .columns {
      display: flex;
      justify-content: center;
    }

    .main-flex-one-container {
      width: 60%;
      margin: 0 auto;
      @include flexbox($justify: space-between, $align: normal, $wrap: nowrap);

      .flex-item {
        text-align: center;
      }

      .t-layer {
        background-color: rgba(255, 255, 255, 0.3);
        width: 65px;
        height: 65px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        position: relative;
        justify-content: center;
        margin: 0 auto;
      }

      .f-txt {
        display: block;
        color: $white;
        font-size: 14px;
        margin-top: 10px;
        @include medium();
      }
    }

    ul.footer_press_images {
      padding: 0;
      margin: 0;
      list-style: none;
      display: flex;
      justify-content: space-between;
      width: 100%;
      flex-wrap: wrap;
      margin-top: 20px;

      .footer_press_logo {
        width: 48%;
        margin-bottom: 20px;
      }
    }

    .main-flex-two-container {
      margin-top: 50px;

      @include flexbox($justify: space-between, $wrap: nowrap);

      .flex-item-3 {
        @include flexbox($justify: normal, $wrap: nowrap);

        a:last-child .btn-img {
          margin-right: 0;
        }
      }

      .footer-logo {
        width: 190px;
        height: 56px;
      }

      .f-txt {
        color: $white;
        font-size: 12px;
        @include medium();
      }

      .btn-img {
        width: 128px;
        height: 36px;
        margin-right: 20px;
      }

      .flex-item-1 {
        @include flexbox($justify: space-between, $wrap: nowrap);
      }
    }

    .social-icon-wrap {
      @include flexbox($justify: normal, $wrap: nowrap);

      a {
        display: flex;
        align-items: center;
        margin: 0 2px;
        padding: 2px 0px 2px 0px;
        border-radius: 6px;
        height: 35px;
      }

      svg {
        margin: 0 10px;
      }
    }
  }

  .mobile-accordion-btn {
    display: none;
  }

  @media only screen and (max-width: 999px) {
    .mobile-accordion-btn {
      display: block;
      padding: 20px 0;
    }

    .desktop-accordion-btn {
      display: none;
    }

    .footer-section-one {
      background-color: $white;

      .f-link {
        font-size: 16px;
        color: #ffffff;
      }

      .a-link:first-child {
        margin-top: 0;
      }

      .a-link {
        font-size: 14px;
        color: #ffffff;
      }

      .accordion-button {
        &::after {
          right: 10px;
          width: auto;
          position: absolute;
          background-image: none;
          content: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'%3E%3Cg id='icon_Right_arrow_small' data-name='icon/Right arrow/small' transform='translate(24) rotate(90)'%3E%3Cpath id='Path_102951' data-name='Path 102951' d='M1063.648,1252.947a.651.651,0,0,1,.918,0l3.459,3.471h0a1.3,1.3,0,0,1,0,1.836h0l-3.489,3.478a.651.651,0,0,1-.918-.918l3.2-3.192a.4.4,0,0,0,0-.571l-3.174-3.193A.65.65,0,0,1,1063.648,1252.947Z' transform='translate(-1053.462 -1245.758)' fill='rgba(0,0,0,0.8)'/%3E%3Crect id='Rectangle_21663' data-name='Rectangle 21663' width='24' height='24' fill='none'/%3E%3C/g%3E%3C/svg%3E%0A");
        }
      }
    }

    .footer-section-two {
      padding: 50px 15px;
      background-image: url('../../../img/footer_bg_sm.png');

      .main-flex-one-container {
        width: 100%;

        .flex-item {
          width: 20%;
        }
      }

      .main-flex-two-container {
        text-align: center;
        margin-top: 35px;
        line-height: 15px;

        .flex-item-1 {
          width: 100%;
          justify-content: center;
        }

        .footer-logo {
          display: none;
        }

        .f-txt {
          font-size: 12px;
        }

        .btn-img {
          display: none;
        }
      }

      .social-icon-wrap {
        display: none;
      }
    }
  }

  @media only screen and (min-width: 1000px) {
    .footer-section-one {
      .accordion-wrappper {
        .accordion-collapse.collapse {
          display: block;
        }
      }
    }
  }
}

.footer-item {
  display: flex;
  align-items: center;

  svg {
    margin-right: 10px;
  }
}

#desktop_footer {
  display: none;
}

.desk_web {
  display: none;
}

.m_web {
  display: block;
}

#mobile_footer {
  display: block;
}

@media only screen and (min-width: 1024px) {
  .desk_web {
    display: block;
  }

  #desktop_footer {
    display: block;
  }

  .m_web {
    display: none;
  }

  .row {
    width: 95%;
    max-width: none !important;
  }

  .sticky-button {
    position: relative;
  }

  #mobile_footer {
    display: none;
  }

  .dropbtn {
    color: white;
    font-size: 16px;
    border: none;
  }

  .dropdown {
    position: relative;
    display: inline-block;
  }

  .dropdown-content {
    font-family: "Inter", sans-serif;
    display: none;
    position: absolute;
    background-color: #fff;
    min-width: 175px;
    z-index: 1;
    box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
  }

  .dropdown-content a {
    color: black;
    padding: 12px 16px;
    text-decoration: none;
    display: block;
  }

  .dropdown-content a:hover {
    background-color: #ddd;
  }

  .dropdown:hover .dropdown-content {
    display: block;
  }
}

span.secondary.round.tiny.cart_count {
  position: absolute;
  top: -7px;
  right: -9px;
  background: #670b19;
  height: 20px;
  width: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 100px;
  color: #fff;
  font-size: 12px;
}

.address-heading.delivery {
  padding: 100px 0 50px;
}

.address-heading {
  h1 {
    color: #777;
    padding: 0 0 20px;
    margin-bottom: 30px;
    font-family: "Inter", sans-serif !important;
  }

  .billing_address table tr td,
  .shipping_address table tr td {
    font-size: 16px;
    color: #777;
    width: 50%;
  }

  span.default-address-text {
    color: maroon;
    font-size: 13px;
  }

  .tab-bar>.left-small {
    border: none !important;
  }

  .billing_address {
    border-radius: 10px;
  }

  li {
    width: 100%;
    max-width: 350px;
    margin: auto;
  }

  .ship-here-button {
    background: #fff;
    border: 1px solid #777;
    border-radius: 4px;
    font-weight: 600;
    font-size: 13px;
  }

  a.button.tiny.expand.secondary.back-button {
    background: maroon;
    border: 1px solid;
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 4px;
    color: #fff !important;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 13px;
  }
}

.option-button {
  display: flex;
  width: 100%;
  align-items: baseline;

  .button-custom {
    width: 40%;
    margin-right: 15px;

    .address-bottom-buttons {
      height: 40px;
      display: flex;
      justify-content: center;
      font-size: 16px;
      align-items: center;
      font-weight: 600;
      border-radius: 4px;
    }

    .back {
      background: #fff;
      color: #777;
      border: 1px solid;
    }
  }
}

.close-icon {
  position: relative;
  background-repeat: no-repeat;
  display: inline-block;
  width: 1em;
  height: 1em;
  vertical-align: middle;
  background-size: 1em;
  background-color: black;
  border-radius: 5em;
  left: 8px;
  bottom: 8px
}

@media screen and (max-width:991px) {
  .address-heading {
    ul.small-block-grid-2 {
      width: 100%;
      display: flex;
    }
  }

  .address-heading a.button.tiny.expand.secondary.back-button {
    font-size: 11px;
  }

  .address-heading a.button.tiny.expand.secondary.ship-here-button {
    font-size: 11px;
  }

  .option-button .button-custom {
    width: 50%;
    margin-right: 15px;
  }

  .option-button .button-custom .address-bottom-buttons {
    font-size: 13px;
  }

  .address-heading.delivery {
    padding: 50px 0;
  }

  .logo-action-text {
    color: black;
    font-size: 12px;
    font-family: "Inter", sans-serif !important;
  }

  .profile-action-wrap,
  .logo-wrap {
    display: flex !important;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }

  .logo-wrap svg {
    height: 20px;
  }
}

@media screen and (max-width: 1023px) {
  span.secondary.round.tiny.cart_count {
    top: 11px;
    right: 12px;
  }
}

section.left-small {
  border-right: 0px !important;
}

.main-flex-two-container {
  background: #7b0e1d;
  color: #fff;
  text-align: center;
  padding: 5px 0;
}

.custom_deal_timer,
.plp_dealtimer,
.pdp_dealtimer {
  text-align: center;

  .message-block {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  div#offer_message_countdown {
    padding: 0px 15px;
    background: #fff9fa;
    margin-left: 9px;
    color: #7b0e1d;

    span#offer_message_clock {
      color: #7b0e1d;
      padding: 3px 0px;
      border-radius: 6px;
    }
  }
}

.pdp_dealtimer {
  text-align: left;
  position: relative;
  z-index: 0;

  .alert-box.global-timer.info.radius {
    border: none;
    padding: 0;

    .message-block {
      display: unset;
      font-size: 15px;
    }

    div#offer_message_countdown {
      margin-left: 0;
      padding: 10px 0 0;
      background-color: #fff;
    }

    a.close {
      display: none;
    }

    u {
      text-decoration: none;
    }
  }
}

a.highlight-link {
  padding: 0 5px;
}

.deal_timer_header.sticky {
  display: none;
}

@media screen and (max-width:1199px) {
  .message-block {
    flex-wrap: wrap;
  }
}

.recommendations_product {
  .design-slider {
    .truncate {
      color: #000000;
      font-size: 16px;
      font-weight: 400;
      padding: 5px 0;
      text-transform: capitalize;
    }

    img.swiper-image {
      border-radius: 6px;
    }

    .details_block {
      display: flex;
      align-items: center;
      width: 100%;

      .actual_price,
      .custom_discount_price {
        font-size: 14px;
        margin: 0;
        padding: 0 5px;
      }

      .details_blocks {
        display: flex;
        font-size: 14px;
        padding: 0 5px;
      }

      .custom_discount_price {
        padding: 0;
      }
    }

    .swiper-button-prev,
    .swiper-button-next {
      background: $dark_red ;
      color: #fff;
      padding: 30px;
      top: 35%;
      border-radius: 50px;
    }
  }
}

@media screen and (max-width:767px) {

  .custom_deal_timer .message-block,
  .plp_dealtimer .message-block,
  .pdp_dealtimer .message-block {
    display: unset;
  }

  .custom_deal_timer {
    margin: -20px 14px 0 12px;
    ;
  }
}

.widget-title .design_blocks .blaze-slider .blaze-container .blaze-slide .product-container {
  box-shadow: none;
}

@media screen and (max-width:1300px) {

  .desktop-header-component-container .header-with-searchbar-wrapper {
    .empty-container {
      width: 408px;
    }

    .searchbar-icon-wrapper {
      width: 200px;
    }
  }
}

@media screen and (max-width:1199px) {
  .message-block {
    flex-wrap: wrap;
  }

  .desktop-header-component-container .header-with-searchbar-wrapper {
    .empty-container {
      width: 362px;
    }

    .searchbar-icon-wrapper {
      width: 170px;
    }

    .search-and-profile-container {
      gap: 2rem;
    }
  }
}

.recommendations_product {
  .design-slider {
    .truncate {
      color: #000000;
      font-size: 16px;
      font-weight: 400;
      padding: 5px 0;
      text-transform: capitalize;
    }

    img.swiper-image {
      border-radius: 6px;
    }

    .details_block {
      display: flex;
      align-items: center;
      width: 100%;

      .actual_price,
      .custom_discount_price {
        font-size: 14px;
        margin: 0;
        padding: 0 5px;
      }

      .details_blocks {
        display: flex;
        font-size: 14px;
        padding: 0 5px;
      }

      .custom_discount_price {
        padding: 0;
      }
    }

    .swiper-button-prev,
    .swiper-button-next {
      background: $dark_red ;
      color: #fff;
      padding: 30px;
      top: 35%;
      border-radius: 50px;
    }
  }
}

@media screen and (max-width:767px) {

  .custom_deal_timer .message-block,
  .plp_dealtimer .message-block,
  .pdp_dealtimer .message-block {
    display: unset;
  }

  .custom_deal_timer {
    margin: -20px 14px 0 12px;
    ;
  }
}

.widget-title .design_blocks .blaze-slider .blaze-container .blaze-slide .product-container {
  box-shadow: none;
}

.footer_row {
  display: flex;
  margin-left: auto;
  margin-right: auto;
  max-width: 76.5rem !important;
  justify-content: center;
  align-items: baseline;

  .footer-left {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 100%;

    .footer {
      display: flex;
      justify-content: center;
      padding: 30px 40px;
      font-family: Arial, sans-serif;
      margin: 0 auto;
      width: 100%;

      .footer-section {
        flex: 1;
        margin: 0 10px;

        h3 {
          font-weight: bold;
          font-size: 18px;
          color: white;
        }

        ul {
          list-style: none;
          padding: 0;
          margin: 0;

          li {
            display: flex;
            margin-bottom: 8px;
            color: white;
            font-size: 14px;
            white-space: nowrap;
            align-items: center;

            a {
              display: flex;
              align-items: center;
              text-decoration: none;
              color: white;
            }

            svg {
              width: 16px;
              height: 16px;
              margin-right: 8px;
              fill: #555;
            }

            &:hover .icon {
              fill: #007bff;
            }
          }
        }
      }
    }

    hr {
      opacity: 0.25;
    }

    #footer-banner {
      display: flex;
      flex-direction: column;
      text-align: center;
      justify-content: center;

      h3 {
        font-size: 18px;
        color: white;
        font-weight: bold;
      }

      #share-icon {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        gap: 100px;
        margin-top: 10px;

        .circular-icon {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 90px;
          height: 90px;
          border-radius: 50%;
          background-color: rgba(255, 255, 255, 0.2);
          overflow: hidden;
          margin-bottom: 10px;
        }

        .icon-wrapper {
          display: flex;
          flex-direction: column;
          align-items: center;
          text-align: center;
          margin: 0 15px;
          justify-content: center;

          svg {
            width: 31px;
            height: 42px;
          }

          a {
            text-decoration: none;
            color: white;
            font-size: 14px;

            &:hover {
              color: #ff6347;
            }
          }
        }
      }

      .text-white {
        color: white;
        font-size: 14px;
      }

      .first-item {
        margin-top: 30px;
        background-color: #670b19;
      }

      .text-white {
        color: white;
        font-size: 12px;
      }
    }
  }

  .footer-right {
    display: flex;
    justify-content: center;
    align-items: center;
    font-family: Arial, sans-serif;
    margin: 0 auto;
    width: 50%;

    .footer-section {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      margin: 0 10px;

      h3 {
        font-size: 18px;
        font-weight: bold;
        color: white;
      }

      .footer-media-print {
        margin-top: 50px;

        .third-item-image {
          display: flex;
          align-items: center;
          list-style: none;
        }

        .third-item-image li {
          margin-left: 0;
          margin-right: 5px;

          img {
            height: 50px;
            object-fit: contain;
          }
        }
      }

      .footer-app-icon {
        margin-top: 50px;

        .footer-app {
          display: flex;
          justify-content: flex-start;
          align-items: center;
          margin-left: 0px;

          .footer-app-android {
            width: 34%;
            margin-right: 5px;
          }

          .footer-app-iphone {
            width: 31%;
          }
        }
      }
    }

    .text-white {
      color: white;
      font-size: 12px;
    }
  }
}

.text-white {
  color: white;
  font-size: 12px;
}
