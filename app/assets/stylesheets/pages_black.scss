// Place all the styles related to the Pages controller here.
// They will automatically be included in application.css.
// You can use Sass (SCSS) here: http://sass-lang.com/
@import 'variables';
@import 'black_theme';

body {
  font-family: "Inter", sans-serif !important;
}
.pages_eid{
  #eid-container{
    .design-block{
      margin: 4px 0px;
    }
    .featured-products{
      border: 1px solid gray;
      .bestseller-title, .view-more{
        text-align: center;
      }
    }
    img{
      width: 100%;
    }
  }
}
@media only screen and (max-width: 620px) {
  .pages_eid{
    #eid-container{
      .columns{
        padding: 0px;
      }
      .pad-right{
        padding-right: 4px !important;
      }
      .pad-left{
        padding-left: 4px !important;
      }
      .row{
        .row{
          margin: 0 auto;
        }
      }
    }
  }
}
.pages_stitching_information{
  #container{
    margin-top: 1.8em;
  }
  .stitching_steps{
    background-color: $snow_white;
    box-shadow: $card_box_shadow;
    .title{
      font-size: 150%;
      text-align: center;
      color: $text_black;
    }
    .step_circle{
      display: inline-block;
      width:50px;
      height:50px;
      border-radius:100%;
      font-size:3%;
      line-height:50px;
      text-align:center;
      margin-left: 10%;
      background:#BD3A55;
    }

    .step_text{
      font-size:15px; 
      color: $text_black;
      font: inherit;
    }
  }
  .size_image{
    .button{
      color: $text_black;
      background-color: $snow_white;
      box-shadow: $card_box_shadow;
      font-size: 16px;
      padding: 8px 12px;
      margin-bottom: 0px;
    }
  }

  .listing_panel_block{
    background: $snow_white;
    box-shadow: $card_box_shadow;
    margin-bottom: 20px;

    a:hover, a:focus{
      color: $text_black;
    }

    li, label{
      cursor: auto;
    }

    table{
      border: none;
      background: $snow_white;
      color: $text_black;
    }
    p{
      text-align: left;
      background: none repeat scroll 0% 0%;
      margin: -7px 0px 0px;
      border-radius: 2px 2px 0px 0px;
      padding: 6px;
    }

    ul{
      font-size: 14px;
    }
    
    td{
      vertical-align: baseline;
    }
  }

  .accordion{
    table {
      display:none;
    }
    
    a {
      outline: none;
      color: inherit;
    }  

    .expand{
      display: none;
      float: right;
    }

    .collapse{
      float: right;
    }
  }

  .stitching_details{
    .panel_content{
      padding: 0px;
    }
    .stitching_question{
      font-size:17px; 
      color:$text_black;
      font-weight: bold;
    }

    .stitching_answer{
      font-size:15px; 
      text-align: justify;
      color: $text_black;
      font: inherit;

    }
  }

  .statement{
    border:1px solid;
    text-align:center; 
    color: $text_black;  
    font-size: 14px;
    height:24px;
    padding: 7px;
    margin: 0 auto;
    width: 80%;
    font-style: inherit;
    font-weight: bold;
  }

  .message{
    padding: 2px;
    text-align: center;
    background: $snow_white;
    color: $text_black;
    box-shadow: $card_box_shadow;
    font-weight: bold;
  }
  .stitching_number{
    color: #e47450;
    font-weight: 400;
    text-align: left
  }
}

#trackModal{
  background-color:$body_white;
  padding-top: 50px;
  min-height: 33vh;
  top: 50px;
  .close-reveal-modal{
    padding: 1% 5% 1% 7%;
    right: 0.3rem;
  }
  #order_number_field{
    margin-bottom: 22px;
  }
}

#list_of_order_modal{
overflow: auto;
position: fixed;
height: 100%;
background-color:$body_white;
top: 0px !important;
width: 100%;
.close-reveal-modal{
  padding: 1% 5% 1% 7%;
  right: 0.3rem;
  }
}

.pages_faq{
  .main-section{
    margin-top: 0px !important;
    #container{
      .faq_main_div{
        font-size: 0.65em;
        .questions_ul{
          list-style: none;
          margin-left: 0px;
        }
        .question_li_content{
          h3{
            font-size: 1em;
            &:before{
              content: "[+]  ";
            }
          }
        }
        .question_selected{
          h3{
            font-size: 1em;
            &:before{
              content: "[-]  ";
            }
          }
        }
        #search_ques{
          font-size: 1.5em;
        }
        #search_answer{
          span{
            font-size: 0.9em !important;
          }
        }
        .search_res{
          padding: 12px 8px;
          background-color: $snow_white;
          box-shadow: $card_box_shadow;
        }
        .faq_head{
          font-size: 1.5em;
          padding: 5px;
          margin-bottom: 10px;
        }
        .quick_links{
          text-align: center;
          background-color: $snow_white;
          .quick_link{
            height: 110px;
            border: 1px solid gray;
            h2{
              font-size: 1em;
            }
            }
          }
        .faqs_row{
          font-size: 1.2em;
          .faqs_all{
            margin-top: 30px;
            }
          .view_more{
            margin-left: -17px;
            background-color: #1D2A29;
            padding: 10px;
            border-radius: 12px;
            }
          }

        .faq_link_selected{
          color: $text_black;
          background-color: #7b7a7a;
        }
        .faq_link_selected::before{
          content:"";
          position: absolute;
          right: 40%;
          top: 102px;
          width: 0;
          height: 0;
          border-left:8px solid transparent;
          border-bottom: 10px solid $body_white;
          border-right: 8px solid transparent;
          }
        .answer_selected{
          padding: 12px;
          background-color: $snow_white;
          box-shadow: $card_box_shadow;
          span{
            font-size: 0.9em !important;
          }
        }
        .navigation {
          height: 300px;
          overflow: auto;
        }
        hr{
          margin: 0.7rem 0 0.5rem 0;
        }
        .answer_nav{
          height: 200px;
          overflow: auto;
        }
      }
    }
  }
}
#help_center{
  .accordion_title{
    // margin-left: -0.2em;
    background-color: cadetblue;
    border: solid;
  }
  .tab-title.active{
      a{
        background-color: #6dc1b8;
        border-bottom: none;
      }
    }

  .tab-title{
    width: 50%;
    a{
      border-radius: 4px 4px 0px 0px;
      background-color: ghostwhite;
      border: 1px solid #a2a2a2;
      padding: 0.5em;
      text-align: center;
    }
  }
  .tabs-content{
    border-bottom: 1px solid;
    padding: 5px;
    border-left: 1px solid;
    border-right: 1px solid;
    background-color: grey;
    border-color: gray;
    margin-top: -2px;
  }
  .accordion{
    margin-left: -0.3em;
  }
  #issue_panel{
    .columns{
      padding-left: 0px;
      padding-right: 0px;
    }
  }
  .category-links{
    height: 110px;
    border: 1px solid #6e6e6e;
    text-align: center;
    color: black;
  }
  .category-links.active{
    background-color: #6dc1b8;
  }
  .category-links.active:before{
    content:"";
    position: absolute;
    right: 43%;
    top: 98px;
    width: 0;
    height: 0;
    border-left:8px solid transparent;
    border-bottom: 10px solid $snow_white;
    border-right: 8px solid transparent;
  }
  .content_replace{
    padding: 0.9375rem;
    display: block;
    color: black;
    background-color: #cacaca;
  }
  .category_query{
    list-style: none;
    color: black;
    hr{
      margin: 0.1rem 0 1.1875rem;
    }
  }
  .category_query:first-child{
    padding-top:1em;
  }
  .stage_passed{
    background-color: #009688;
  }
  .stage_passed:before{
    content: '\2713  '
  }
  .category_query:before{
    content: '\203A'
  }
  .category_list{
    margin-left: 10px;
  }
  .blur{
    opacity: 0.6;
  }
  .button.info{
    background-color: #a0d3e8 !important;
  }
  #loadingImage{
    position: fixed;
    left: 45%;
    top: 30%;
    font-size: 4em;
  }
  #error_order_not_found_email{
    color: #D84315;
    padding: 3px;
  }
  .login_hr:after {
  content: "OR"; /* section sign */
  color: #999;
  display: inline; /* for vertical centering and background knockout */
  background-color: #cacaca; /* same as background color */
  padding: 0 0.5em; /* size of background color knockout */
  }
  .login_hr {
  font-family: Arial, sans-serif; /* choose the font you like */
  text-align: center; /* horizontal centering */
  line-height: 1px; /* vertical centering */
  height: 1px; /* gap between the lines */
  font-size: 1em; /* choose font size you like */
  border-width: 1px 0; /* top and bottom borders */
  border-style: solid;
  border-color: #676767;
  margin: 20px 10px; /* 20px space above/below, 10px left/right */

  /* ensure 1px gap between borders */
  -webkit-box-sizing: content-box;
  -moz-box-sizing: content-box;
  -ms-box-sizing: content-box;
  -o-box-sizing: content-box;
  box-sizing: content-box;
  }
  #recent_order_number{
    text-decoration: underline;
    color: cadetblue;
  }
  .track_btn{
    background-color: white;
    height: 100px;
    border: 1px solid black;
    text-align: center;
    color: black;
    padding: 10px;
  }
  #issue_text_help_center,#order_number_text,#email_id_help_center{
    color: black;
    &:hover {
        color:white;
      }
  }
}
#order_table_list{
  li{
    background-color: $snow_white;
    margin-top: 30px;
    list-style: none;
    box-shadow: $card_box_shadow;
    padding: 5px;
    margin-left: -20px;
  }
}