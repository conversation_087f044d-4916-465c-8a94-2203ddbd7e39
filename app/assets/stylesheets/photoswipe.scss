/*! PhotoSwipe main CSS by <PERSON> | photoswipe.com | MIT license */

/*
	Styles for basic PhotoSwipe functionality (sliding area, open/close transitions)
*/

// PhotoSwipe uses Autoprefixer, so vendor prefixed are added automatically when needed.

$pswp__show-hide-transition-duration: 333ms !default;
$pswp__controls-transition-duration: 333ms !default;
$pswp__background-color: #fff !default;
$pswp__placeholder-color: #b3b3b3 !default;
$pswp__box-sizing-border-box: true !default; // disable .pswp * { box-sizing:border-box } (in case you already have it in your site css)
$pswp__root-z-index: 1500 !default;
$pswp__assets-path: '' !default; // path to skin assets folder (preloader, PNG and SVG sprite)
$pswp__error-text-color: #CCC !default; // "Image not loaded" text color
$pswp__include-minimal-style: true !default;
 
/* pswp = photoswipe */
.pswp {
	display: none;
	position:absolute;
	width: 100%;
	height: 100%;
	left:0;
	top:0;
	overflow: hidden;
	-ms-touch-action: none;
	touch-action: none;
	z-index: $pswp__root-z-index;
	-webkit-text-size-adjust: 100%;
	/* create separate layer, to avoid paint on window.onscroll in webkit/blink */
	-webkit-backface-visibility: hidden;
	outline: none;

	@if $pswp__box-sizing-border-box == true {
		* {
			box-sizing: border-box;
		}
	}
	
	img {
		max-width: none;
	}
}

/* style is added when JS option showHideOpacity is set to true */
.pswp--animate_opacity {
	/* 0.001, because opacity:0 doesn't trigger Paint action, which causes lag at start of transition */
	opacity: 0.001;
	will-change:opacity;
	/* for open/close transition */
	transition: opacity $pswp__show-hide-transition-duration cubic-bezier(.4,0,.22,1);
}

.pswp--open {
	display: block;
}

.pswp--zoom-allowed .pswp__img {
	/* autoprefixer: off */
	cursor: -webkit-zoom-in;
	cursor: -moz-zoom-in;
	cursor: zoom-in;
}

.pswp--zoomed-in .pswp__img {
	/* autoprefixer: off */
	cursor: -webkit-grab;
	cursor: -moz-grab;
	cursor: grab;
}

.pswp--dragging  .pswp__img {
	/* autoprefixer: off */
	cursor: -webkit-grabbing;
	cursor: -moz-grabbing;
	cursor: grabbing;
}

/*
	Background is added as a separate element.
	As animating opacity is much faster than animating rgba() background-color.
*/
.pswp__bg {
	position: absolute;
	left: 0;
	top: 0;
	width: 100%;
	height: 100%;
	background: $pswp__background-color;
	opacity: 0;
	transform: translateZ(0);
	-webkit-backface-visibility: hidden;
	will-change:opacity;
}

.pswp__scroll-wrap {
	position: absolute;
	left: 0;
	top: 0;
	width: 100%;
	height: 100%;
	overflow:hidden;
}

.pswp__container,
.pswp__zoom-wrap {
	-ms-touch-action: none; 
	touch-action: none;
	position: absolute;
	left: 0;
	right: 0;
	top: 0;
	bottom: 0;
}

/* Prevent selection and tap highlights */
.pswp__container,
.pswp__img {
	-webkit-user-select: none;
	-moz-user-select: none;
	user-select: none;
	-webkit-tap-highlight-color: rgba(0,0,0,0);
	-webkit-touch-callout: none;
}

.pswp__zoom-wrap {
	position: absolute;
	width: 100%;
	-webkit-transform-origin: left top;
	-moz-transform-origin: left top;
	-ms-transform-origin: left top;
	transform-origin: left top;
	/* for open/close transition */
	transition: transform $pswp__show-hide-transition-duration cubic-bezier(.4,0,.22,1);
}

.pswp__bg {
	will-change: opacity;
	/* for open/close transition */
	transition: opacity $pswp__show-hide-transition-duration cubic-bezier(.4,0,.22,1);
}

.pswp--animated-in {
	.pswp__bg,
	.pswp__zoom-wrap {
		-webkit-transition: none;
		transition: none;
	}
}

.pswp__container,
.pswp__zoom-wrap {
	-webkit-backface-visibility: hidden;
}

.pswp__item {
	position: absolute;
	left: 0;
	right: 0;
	top: 0;
	bottom: 0;
	overflow: hidden;
}

.pswp__img {
	position: absolute;
	width: auto;
	height: auto;
	top: 0;
	left: 0;
}

/*
	stretched thumbnail or div placeholder element (see below)
	style is added to avoid flickering in webkit/blink when layers overlap
*/
.pswp__img--placeholder {
	-webkit-backface-visibility: hidden;
}

/*
	div element that matches size of large image
	large image loads on top of it
*/
.pswp__img--placeholder--blank {
	background: $pswp__placeholder-color;
}

.pswp--ie .pswp__img {
	width: 100% !important;
	height: auto !important;
	left: 0;
	top: 0;
}

/*
	Error message appears when image is not loaded
	(JS option errorMsg controls markup)
*/
.pswp__error-msg {
	position: absolute;
	left: 0;
	top: 50%;
	width: 100%;
	text-align: center;
	font-size: 14px;
	line-height: 16px;
	margin-top: -8px;
	color: $pswp__error-text-color;
}

.pswp__error-msg a {
	color: $pswp__error-text-color;
	text-decoration: underline;
}