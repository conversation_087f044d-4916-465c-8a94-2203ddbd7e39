html {
  scroll-behavior: smooth;
}
.container {
  display: flex;
  flex-direction: row;
}
.designer-page {
  width: 100%;
  margin: auto;
}

[id^="alphabet"] {
  scroll-margin-top: 15rem;
}

.designer-page-heading h1 {
  color: #8f1b1d;
  padding-bottom: 20px;
}

.designers-name {
  display: flex;
  background: #fff9fa;
  padding: 20px;
  margin-bottom: 20px;
  border-radius: 15px;
}

.designer-alphabet {
  ul {
    list-style: none;
    display: flex;
    width: 100%;
    background: #fff9fa;
    padding: 20px;
    border-radius: 15px;
    flex-wrap: wrap;
    justify-content: center;
  }
}

.designers-name {
  ul {
    list-style: none;
    padding: 0;
  }
}

span.main-letter {
  margin-left: 3rem;
  font-size:2rem;
  margin-top:-10px;
  font-weight: bolder;
}

.designer-name-list {
  width: 100%;

  ul {
    display: flex;
    flex-wrap: wrap;
    margin-left: 5rem;

    li {
      flex-basis: 25%;
      font-size: 15px;
      margin: 0 0 15px 0px;
      text-align: start;

      a {
        color: #000;
        text-decoration: none;
        text-transform: capitalize;
      }
    }
  }
}

.alphabet-list-wrapper {
  position: sticky;
  max-height: 75vh;
  top:130px;
  overflow-y: auto;
  box-shadow: -1px 0 1px rgba(0, 0, 0, 0.1);
  scrollbar-width: none;
  -ms-overflow-style: none;

  &::-webkit-scrollbar {
    display: none;
  }

  .alphabet-list {
    list-style-type: none;

    .list-item {
      background-color: white;
      padding: 0px 1px 3px 1px;

      .alphabet {
        font-size: 18px;
        font-weight: 700;
        text-align: center;
        text-transform: uppercase;
        text-decoration: none;

        a:hover {
          color: rgb(245, 56, 56) !important;
        }
        
        a.active {
          color: rgb(245, 56, 56) !important;
        }
      }
    }
  }
}

@media (max-width: 1000px) {
  .designer-name-list {
    ul {
      margin-left: 3rem;
      li {
        flex-basis: 100%;
      }
    }
  }
    span.main-letter {
      margin-left: 0 !important;
    }
}

@media (max-width: 550px) {
  .designer-name-list {
    ul {
      li {
        font-size: 12px !important;
      }
    }
  }
  span.main-letter {
    font-size: 1.5rem !important;
    font-weight: bolder;
  }
  .alphabet-list-wrapper {  
    width:15%;
    .alphabet {
      font-size: 12px !important;
      font-weight: bold;
    }
  }
}

