@import 'variables_red';
@import 'red_theme';
$gray_colour:#eeeeee81;
$font_helpcenter:<PERSON><PERSON>, "Helvetica Neue", Helvetica, Arial, sans-serif;
.store_dynamic_landing_page{
  #main-container{
    .design-block{
      margin: 4px 0px;
    }
    .featured-products{
      .view-more{
        text-align: center;
      }
    }
    img{
      width: 100%;
      border-radius: 6px;
    }
  }
}
@media only screen and (min-width: 1024px) {
  .store_dynamic_landing_page{
    #container{
      margin-top: 1.5em;
    }
    .box-margin-top{
      margin-top: 20px;
    }
    .margin-top-bottom{
      margin-top: 10px;
      margin-bottom: 10px;
    }
  }
}
@media only screen and (max-width: 620px) {
  .store_dynamic_landing_page{
    #main-container{
      .columns{
        padding: 0px;
      }
      .pad-right{
        padding-right: 4px !important;
      }
      .pad-left{
        padding-left: 4px !important;
      }
      .row{
        .row{
          margin: 0 auto;
        }
      }
    }
  }
}
.red-button {
  -webkit-appearance: none;
  -moz-appearance: none;
  font-weight: normal;
  background-color: $dark_red !important;
  border-color: $dark_red;
  color: #FFFFFF;
  transition: background-color 300ms ease-out;
}
.bestseller-title {
h1{
  font-size: 24px;
  padding: 10px 0;
  font-weight: 600;
}
}
ul#bestsellers {
  display: flex;
  width: 100%;
  flex-wrap: wrap;
  max-width: 1500px;
  margin: auto;
  justify-content: center;
  li {
    width: 20%;
    .fr_page.ga_design_container {
      width: 100%;
      max-width: 250px;
  }
  .discount-wishlist {
    color: #670b19;
}
.details_block {
  height: 50px;
}
}
  .design-col2.details_block {
    display: none;
  }
  .view-more a.button.primary.tiny.red-button {
    background: #fff9fa !important;
    color: #670b19;
    border: 1px solid #670b19;
    padding: 15px 35px;
    border-radius: 6px;
    font-size: 16px;
    font-weight: 600;
}
}
@media screen and (max-width:991px) {
  ul#bestsellers {
    justify-content: unset;
    li {
      width: 50%;
  }
  }
}