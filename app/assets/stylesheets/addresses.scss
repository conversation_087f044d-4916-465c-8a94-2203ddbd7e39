// Place all the styles related to the Addresses controller here.
// They will automatically be included in application.css.
// You can use Sass (SCSS) here: http://sass-lang.com/
@import 'variables';
@import 'white_theme';

#container{
  margin-top: 0.5em !important;
  padding-top: 1% !important;
  #address_collect_submit{
    font-size: 0.9em;
    box-shadow: $green_btn_box;
    background-color: $green_btn;
    font-weight: 700;
  }
}

.address_field_div{
  display: none;
}
.billing_address, .shipping_address {
  padding: 0 8px;
  label {
    color: $green_font;
    display:none;
  }
  select {
    background-image: image-url('down.png') !important;
    background-size: 10px;
  }
  #dial_code{
    margin-top: 15%;
  }
  .save_btn {
    background-color: $green_btn;
  }
  table {
    background: inherit;
    border: none;
    margin-bottom: 0em;
    tr {
      &:nth-of-type(even) {
        background: inherit;
      }
      td {
        color: inherit;
        padding: 0.1em;
      }
    }
  }
  a {
    margin-bottom: 0em;
  }
  .shipping_confirm_checkbox{
    display: block;
    margin-top: 1%;
  }
}
input[type='submit'] {
  margin-top: 1em;
}
.payment_options {
  label {
    color: inherit;
  }
}
.payment_btn {
  .cont_payment {
    background-color: $green_btn;
  }
}
.pincode_format {
  margin: 0px 0px 5px 0px;
  margin-left: inherit;
  font-size: 12px;
  color: $text_black;
  font-weight: bold;
  padding-left: 0.7em;
}