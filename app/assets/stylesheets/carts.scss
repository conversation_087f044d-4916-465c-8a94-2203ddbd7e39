// Place all the styles related to the Carts controller here.
// They will automatically be included in application.css.
// You can use Sass (SCSS) here: http://sass-lang.com/
@import 'variables';
@import 'white_theme';

$gray: #e3e3e3;
$light_gray: #b9b9b9;
$blue: #70DBFF; 

body {
  font-family: "Inter", sans-serif !important;
}
.carts_show.page{
  #container{
    margin-top: 0 !important;
  }
  .font-gray{
    color: $text_light_gray;
  }
}
.enableLink{
  color: #c13960 !important;
  font-weight: bold !important;
  cursor: pointer !important;
}
#carts_block {
  h2.heading{
    color: $text_black;
  }
  ul.addons-notes.accordion {
    padding-left: 0em;
    color: $text_light_gray;
    margin-left: 0em;
    font-size: 14px;
    padding-right: 0;
    .accordion-navigation {
      > a {
        background: inherit;
        color: inherit;
        font-size: 14px;
        padding: 0rem;
        &:before {
          content: '»';
          float: left;
          color: $link_blue;
          font-size: 14px;
          font-weight: 700;
          padding-right: 2px;
        }
      }
      .text-right{
        float: right;
      }
      > .content {
        padding: 0rem;
        &.active {
          background: inherit;
        }
      }
      &.active > a:before {
        content: '«';
      }
    }
  }
  .item_block {
    background-color: $body_white;
    margin-bottom: 1em;
    font-size: 14px;
    padding: 0.5em 0em;
    box-shadow: $card_box_shadow;
    margin-left: 0;
    margin-right: 0;
    .image-box{
      padding: 0 6px 0 4px;
      .truncate{
        color: $text_light_gray;
        text-align: center;
      }
    }
    .design_quantity{
      color: $text_black;
      .quantity_list{
        width: 42%;
      }
    }
    .design-title{
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .right{
      padding-right: 2px;
      text-align: right;
    }
    .item-price-font{
      font-size: 12px;
      margin-top: 2px;
    }
  }
  .close {
    margin-top: -6px;
    line-height: 1em;
    font-weight: bolder;
    font-size: 20px;
  }

  .panel_block {
    margin-top: 0px !important;
    #fixed_checkout_button {
      right: 0;
      left: 0;
      top: auto;
      background-color: $body_white;
      padding: 0px !important;
      user-select: none;
      -o-user-select:none;
      -moz-user-select: none;
      -khtml-user-select: none;
      -webkit-user-select: none;
      a {
        margin: 0 !important;
        color: white;
      }
      .cart_checkout_button {
        background-color: $green_cart_checkout;
      }
      .cart-checkout-btn{
        margin-top: 10px;
      }
      .view_details_button {
        background-color: $body_white;
        color: black;
        font-size: 16px;
        .view_details_text {
          color: $link_blue;
          font-size: 12px;
          letter-spacing: 1px;
          font-weight: normal;
          margin-top: 7px;
        }
      }
      .button {
        padding: 15px 10px 15px 10px;
        margin: 0px;
        width: 100%;
        font-weight: bold;
      }
    }
    .fixed_cart_button {
      position: fixed;
      bottom: 0;
      z-index: 10;
    }
    .panel_heading {
      padding: 0.4rem 0.9375rem;
      margin-top: 1rem;
      .save_email {
        padding: 4px 8px 2px 8px;
        margin-bottom: 0px;
        background-color: $body_white;
        color: $text_black;
        border: none;
        box-shadow: $gray_btn_box;
        font-size: 0.8rem;
        float: right;
        width: 100%;
      }
      .save_email_box {
        padding: 0px 5px 0px 5px;
        margin: 0px 0px 0px 5px;
        border: none;
        box-shadow: $gray_btn_box;
        height: 1.8rem;
        line-height: 0px;
        width: 95%;
      }
      input[type="email"] {
        display: inline;
        padding: 0.1rem 0.5rem;
        margin: 0px;
        height: 1.8rem;
        &:focus {
          box-shadow: 0 1px 0 0 $input_focus_color;
          border-bottom: 1px solid $input_focus_color;
        }
      }
      .email_form {
        margin-bottom: 0.5em;
        display: none;
      }
      .email_display {
        padding: 1px 2px;
        font-size: 0.9rem;
        display: none;
      }
    }
    .panel_content {
      padding-top: 1em;
      img {
        width: 100%;
        border-radius: 2px;
      }
      .minimum_cart_value_message{
        background-color: $light_red;
        color: white;
        padding: 12px;
        font-size: 13px;
        border-radius: 3px;
        margin-bottom: 10px;
      }
      .cart-discount-message {
        margin: 2px 0px;
        h6.text-center {
          margin-bottom: 0.2rem;
        }
      }
      .design_quantity {
        padding: 0;
        select {
          padding: 0.1em;
          height: inherit;
        }
      }
    }
  }
  #totals_block{
    table{
      border: none !important;
      float: right;
      width: 90%;
      background: none !important;
      margin-bottom: 0px !important;
    }
    tr{
      background: none !important;
    }
    td, th{
      color: $text_black !important;
      text-align: right !important;
      padding: 0px !important;
    }
  }
  .item-total{
    font-size: 95%;
    padding-left: 0px !important;
    padding-right: 0px !important;
  }
  .coupon, .wallet{
    background: $body_white;
    border-radius: 10px;
    box-shadow: $gray_btn_box;
    color: $text_black;
    font-weight: 700;
    padding: 0.65rem;
  }
  input[name=coupon_code], .small-4 > .button{
    margin-bottom: 0.5rem;
    border: none;
    border-bottom: 1px solid #9e9e9e;
    box-shadow: none;
    &:focus{
      box-shadow: 0 1px 0 0 $input_focus_color;
      border-bottom: 1px solid $input_focus_color;
    }
  }
  .small-4 > .button{
    padding: 0px;
    box-shadow: $gray_btn_box;
    border-radius: 10px;
  }
  .coupon-box, .wallet-box{
    display: none;
  }
  .item-text{
    padding: 0px;
  }
  #action_buttons{
    .add_place_order{
      font-weight: 700;
    }
  }
}

#apply-coupon-block{
  .remove-coupon-container {
    margin-top: 4px;
    margin-bottom: 10px;
    height: 51px;
    width: 99%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border: 2px solid #ccc;
    padding: 14px;
    margin-left: 2px;
  }
  .coupon-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    line-height: 1.2;
    margin-top: 4px;
  }
  .saved-amount {
    color: green;
    font-size: 14px;
  }
  .remove-button {
    margin-left: 10px;
  }
  .coupon-button-css{
    margin-left: 2px;
    background: none;
    color: #721220;
    width: 99%;
    border: 1px solid #721220;
    padding: 7px;
  }
  .coupon-button-css:hover {
    margin-left: 2px;
    cursor: pointer;
  }
  .apply_coupon {
    margin-bottom: -15px;
  }
  .remove-coupon-button-css{
    margin-top: 18px;
    font-size: 15px;
    background-color: transparent;
    color: #681e19;
    padding: 5px;
    margin-left: 2px;
  }
  .remove-coupon-button-css:hover {
    margin-left: 2px;
    background-color: #eaeaec;
    color: black;
    cursor: pointer;
  }
  label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
  }
}

.apply_wallet{
  .usable-reward-text{
    font-size: 14px;
    padding-right: 10px;
    span{
      font-weight: bold;
    }
  }
}
.coupon-box,.wallet-box{
  display: none;
}
.use-msg{
  font-size: 12px;
  .apply-btn-link{
    background: transparent !important;
    border: none !important;
    padding: 0px !important;
    box-shadow: none !important;
    font-weight: 400 !important;
    &:focus{outline: none;}
  }
}
.giftwrap {
  margin-left: 2px;
  width: 99%;
  margin-bottom: 15px;
  border-radius: 10px;
  text-align: center;
  padding: 5px;
  font-size: 15px;
  background-color: $body_white;
  box-shadow: 0 4px 2px -2px gray;
  .giftwrapped-text {
    border-right: 1px solid;
  }
  .giftwrap-remove {
    color: $light_orange !important;
  }
  a.giftwrapped {
    .giftwrapped-col {
      color: $text_filter_color;
    }
  }
  .f_gift_wrap {
    background: image-url('sprite.png') no-repeat;
    display: inline-block;
    width: 35px;
    height: 17px;
    background-position: -144px -981px;;
  }
}

.best_offer {
  margin-top: 10px;
  margin-bottom: 5px;
  border-radius: 5px;
  text-align: center;
  padding: 5px;
  font-size: 15px;
  background-color: $body_white;
  display: flex;
  color: $cobalt_blue;
  font-size: 14px;
  .info_icon{
    border-radius: 50%;
    background-color: $cobalt_blue;
    padding: 1px 8px;
    font-size: 13px;
    font-style: italic;
    font-weight: 700;
    font-family: 'Times New Roman';
    color: white;
    height: 50%;
    margin-left: 5px;
  }
  .bmgnx_cart_offer_msg {
    color: $blue;

  }
  span{
    margin-left: 5px;
    .hyperlink_text {
      font-style: italic;
      text-decoration: underline;
    }
  }
}

.offer_messages_block {
  background-color: $body_white;
  color: black;
  padding: 5px;
  li {
    padding: 5px;
  }
  i{
    font-size: 12px;
  }
}

@import 'unbxd_recommendations'