@import 'variables';
@import 'white_theme';
body {
  font-family: "Inter", sans-serif !important;
}
.split.button {
  width: 100%;
  
  &.facebook {
    background: $facebook-color;
    text-transform: $text-transform;
    
    span {
      background: darken( $facebook-color, 10% );
      
      &:after { 
        border: none;
        font-family: "foundation-icons";
        content: "\f1c4";
        font-size: $icon-size;
        line-height: 0.03em;
        margin-left: -0.5em;
      }
    }
  }
  
  &.twitter {
    background: $twitter-color;
    text-transform: $text-transform;
    
    span {
      background: darken( $twitter-color, 10% );
      
      &:after { 
        border: none;
        font-family: "foundation-icons";
        content: "\f1e4";
        font-size: $icon-size;
        line-height: 0.03em;
        margin-left: -0.5em;
      }
    }
  }

  &.google {
    background: $google-color;
    text-transform: $text-transform;
    
    
    span {
      background: darken( $google-color, 10% );
      
      &:after { 
        border: none;
        font-family: "foundation-icons";
        content: "\f1ca";
        font-size: $icon-size;
        line-height: 0.03em;
        margin-left: -0.5em;
      }
    }
  }
}
.bordered_block {
  label {
    color:#3C4345;
  }
  .login_btn {
    background-color: $dark_green;
  }
}
hr {
  border: #bdb3b3 solid;
  border-width: 0.1em 0 0;
  margin-top: 0.4em;
}
.change-password {
  padding: 20px 0;
}
#new_account{
  label{
    color: $text_black;
  }
}