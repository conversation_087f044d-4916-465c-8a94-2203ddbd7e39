@import 'variables_red';
@import 'red_theme';
@import 'blaze';
body {
  font-family: "Inter", sans-serif !important;
}

.title-block{
  h1{
    color: $text_black;
    font-size: $font-size;
    font-family: "Inter", sans-serif !important;
    &:after{
      content: '»';
      margin-left: 4px;
      color: $text_black;
    }
  } 
}
#message-box, #designer_discounts_block{
  margin: 0 auto;
  max-width: 65.5rem;
  width: 98%;
  padding: 16px 6px;
  margin-bottom: 10px;
}

#message-box{
  .alert-box.message{
    background-color: #d3b9be;
    border-color: $dark_red;
    color: $dark_red;
    font-weight: bold;
    margin-bottom: 0px;
  }
}

#designer_discounts_block{
  li{
    .designer_block{
      font-size: $cart_font;
      text-align: center;
      background: transparent;
      padding: 5px 0px;
      border-radius: 2px;
      box-shadow: $card_box_shadow;
      .image-circle{
        border-radius: 5px;
        height: 100px;
        width: 100px;
      }
      .designer-name{
        color: $text_black;
        text-transform: capitalize;
        font-weight: 700;
        font-family: "Inter", sans-serif;
      }
      .percent-off{
        padding: 4px 16px;
        background-color: transparent;
        color: $dark_red;
        margin: 5px 0px;
        font-weight: 700;
        display: inline-block;
        border-top: 2px solid $dark_red;
        border-bottom: 2px solid $dark_red;
        font-family: "Inter", sans-serif;
      }
      .end-date{
        padding: 2px 24px;
        color: $text_black;
        font-family: "Inter", sans-serif;
      }
    }
  }
}
#discounted-designs{
  box-shadow: 0 0 6px 0px #8e8e8e;
  margin: 0 auto;
  max-width: 65.5rem;
  width: 98%;
  padding: 8px 6px;
  margin-bottom: 10px;
  .discounted_prodcucts{
    padding: 2px 10px;
    .blaze-flex-container{
      display: flex;
      justify-content: space-around;
    }
    .ribbon {
      font-size: 14px !important;
      font-weight: 700;
      width: 56%;
      position: relative;
      background: $light_red;
      color: #fff;
      text-align: center;
      padding: 0.5em 0.5em;
      margin: 0.5em 1.5em 0.5em;

      &:before, &:after {
        content: "";
        position: absolute;
        display: block;
        bottom: -0.5em;
        border: 1em solid #b11f2dc9;
        z-index: -1;
      }
      &:before {
        left: -1.5em;
        border-right-width: 1em;
        border-left-color: transparent;
      }
      &:after {
        right: -1.5em;
        border-left-width: 1em;
        border-right-color: transparent;
      }
      .ribbon-label:before, .ribbon-label:after {
        content: "";
        position: absolute;
        display: block;
        border-style: solid;
        border-color: $dark_red transparent transparent transparent;
        bottom: -0.6em;
      }
      .ribbon-label:before {
        left: 0;
        border-width: 0.6em 0 0 0.55em;
      }
      .ribbon-label:after {
        right: 0;
        border-width: 0.6em 0.5em 0 0;
      }
    }
    .price{
      text-align: center;
      margin-left: -25px;
      font-weight: 700;
      color: $text_black;
    }
  }
}
#coupons-show{
  #show-more-coupons{
    background-color: $dark_red;
    box-shadow: $card_box_shadow;
    border-radius: 0px;
    a{
      font-size: 14px;
      font-weight: 600;
      color: $text_white;
    }
  }
}

.orbit-container{
  .orbit-prev, .orbit-next{
    top: 38%;
    display: inline-block !important;
  }
  .orbit-bullets-container{
    .orbit-bullets{
      display: block;
    }
  }
}

@media only screen and (min-width: 768px){
  #coupons-show{
    width: 50% !important;
  }
  #discounted-designs{
    img{
      margin: 0.5em auto 1em;
    }
    .ribbon{
      margin: 0.5em auto 0.5em !important;
    }
    .price{
      margin-left: 0px;
    }
  }
}