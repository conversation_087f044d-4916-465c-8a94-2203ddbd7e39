numbers = Shipment.where(shipper_id: 28).where.not(shipment_state: ['delivered', 'rto', 'processing_abandoned', 'intransit_abandoned']).where("created_at > ?", '2024-02-04 03:33:15.432196').pluck(:number)
shipper_id= 28
response_mapper = {
  'In Transit'  => :in_transit,
  'OK DELIVERY' => :delivered,
}
Shipment.where(number: numbers, shipper_id: shipper_id).find_in_batches(batch_size: 50) do |shipments|
      shipments.each do |shipment|
        begin
          response = AtlanticApi.new.track(shipment.number)
          response = response["Response"]
          if response["Tracking"].present? && (status = response["Tracking"].first["Status"])
            if response["Events"].present? && last_event_data = response["Events"].first
              shipment.last_event_timestamp = Time.zone.parse("#{last_event_data["EventDate"]} #{last_event_data["EventTime1"]}") || Time.current
              shipment.last_event = last_event_data["Status"]
            end
            if response_mapper[status] == :delivered
              shipment.delivered_on = Time.zone.parse("#{response["Tracking"].first["DeliveryDate"]} #{response["Tracking"].first["DeliveryTime"]}") || Time.current
            end
            shipment.update_shipment_state(response_mapper[status]) if response_mapper[status]
          else
            shipment.update_column(:track_error,'Invalid Shipment number')
          end
        rescue => error
          # AtlanticApi.new.notify("Atlantic Track Error", "Track Shipment Failed", shipment, response, error)
        end
      end
    end