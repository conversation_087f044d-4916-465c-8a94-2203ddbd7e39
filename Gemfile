source 'https://rubygems.org'
ruby "2.3.8"

# Bundle edge Rails instead: gem 'rails', github: 'rails/rails'
gem 'rails', '4.2.5'
# Use postgresql as the database for Active Record
gem 'pg'
# Use SCSS for stylesheets
gem 'sass-rails', '~> 5.0'
# Use Uglifier as compressor for JavaScript assets
gem 'uglifier', '>= 1.3.0'
gem 'terser'
# Use CoffeeScript for .coffee assets and views
gem 'coffee-rails', '~> 4.1.0'
# See https://github.com/rails/execjs#readme for more supported runtimes for deployment to run
# gem 'therubyracer', platforms: :ruby

# Use jquery as the JavaScript library
gem 'jquery-rails'
# Turbolinks makes following links in your web application faster. Read more: https://github.com/rails/turbolinks
gem 'turbolinks', '~> 5.0.0'
# Build JSON APIs with ease. Read more: https://github.com/rails/jbuilder
gem 'jbuilder'
gem 'gon'
gem 'stripe'
# cors fix
gem 'rack-cors', group: [:development, :staging, :production]

# Remove for rails 5
gem 'jbuilder_cache_multi'

gem 'oj', '~> 3.7', '>= 3.7.12'
gem 'oj_mimic_json'
# Use for normalizing the attributte
gem 'attribute_normalizer', '1.2.0'
# To make User & Designer followable
gem 'acts_as_follower'
gem 'social-share-button'
#razorpay this may be not being used
gem 'razorpay',git: 'https://github.com/guinex/razorpay-ruby.git'
group :doc do
  # bundle exec rake doc:rails generates the API under doc/api.
  gem 'sdoc', '~> 0.4.0'

  # Documenting model db attributes
  gem 'annotate'

  # Better Than RDoc - Documentation
  gem 'yard'
end

# Use Unicorn as the app server
# gem 'unicorn'
#Client library for Amazon's Simple Storage Service's REST API
gem 'aws-sdk',  '~> 2'
gem 'paperclip', '~> 5.0'
gem 'puma', '~> 3.7.0'
gem 'request_store'
# Unicorn family Rack handlers for `rails s`
# gem 'rack-handlers'

# Used to logically delete the null orders
gem "paranoia", "~> 2.0"

# Use ActiveModel has_secure_password
# gem 'bcrypt', '~> 3.1.7'
gem 'promise'
# Use Capistrano for deployment
# gem 'capistrano-rails', group: :development

gem 'faker'
gem 'spring'

group :development, :test do
  gem 'pry'
  # Call 'byebug' anywhere in the code to stop execution and get a debugger console
  gem 'byebug'

  # Access an IRB console on exception pages or by using <%= console %> in views
  gem 'web-console', '~> 2.0'

  # Spring speeds up development by keeping your application running in the background. Read more: https://github.com/rails/spring
  #gem 'spring'

  # Helpful for debugging. Pulls up console on error in browser request
  gem 'better_errors'

  # To keep a check on database query performance
  gem 'bullet'

  # Check code quality
  gem "reek", require: false

  # Required for rails panel
  gem 'meta_request'

  # Testing framework
  gem 'rspec-rails'
  gem 'factory_girl_rails'

  # Profiler
  # gem 'rack-mini-profiler'
end

group :test do
  gem 'capybara'
  gem 'shoulda-matchers', require: false
  gem 'simplecov'
  gem 'selenium-webdriver'
  gem 'database_cleaner'
  gem 'sunspot_test'
end

# Ruby Like SQL Queries
gem 'squeel'

# SOLR
# gem 'sunspot_rails'
gem 'sunspot_rails', git: 'https://bitbucket.org/blacklife/sunspot.git', branch: 'grouping_in_solr_with_master'
# State Machine
gem 'state_machines-activerecord'

# Templating
gem "haml-rails"
gem 'foundation-rails'
gem 'foundation_rails_helper'

# Generate HTML Components From JSON
gem 'react-rails', "~> 1.2.0"

# Adding tags
gem 'acts-as-taggable-on'

# Account authentication
gem 'devise', "~> 3.5.10"

# Authentication with Facebook
gem 'omniauth-facebook', '~> 5.0'

# Authentication with Google
gem 'omniauth-google-oauth2', '~> 0.6.0'

# Account authentication token helper
gem 'devise_token_auth', "0.1.43"

# HTTP requests
gem 'httparty'

# Detect Request Agent
gem 'browser', '2.2.0'

# For nice looking urls
gem "friendly_id"

# To find location from ip
gem 'maxminddb', '0.1.7'

# Storing papertrail
gem 'paper_trail'

# consider the deskop versions of delayed job before changing
# gem 'delayed_job_active_record', '4.1.1'

# Send account emails in background
gem 'devise-async'

# Controller action caching
gem 'actionpack-action_caching'

# For Memcache
gem 'dalli'
gem 'connection_pool'

# Pagination
gem 'kaminari'

group :production, :staging do

  # Memcachier gem for heroku memcached addon
  gem 'memcachier'

  # Use Puma as the app server
  # gem 'puma'


  # Enable gzip compression on heroku
  # gem 'heroku-deflater'
end

# Controlling max time per request
gem 'rack-timeout'

# SSL redirection
gem 'rack-ssl-enforcer'

# Awesome nested set to express hierarchical relationship
gem 'awesome_nested_set'

#gem 'paypal-express', git: 'git://github.com/DevilalDheer/paypal-express.git', branch: 'pre_fill_address_info'
gem 'paypal-express', git: 'https://<EMAIL>/blacklife/paypal-express.git', branch: 'paypal-credit-enable'



gem 'paypal-sdk-rest', git: 'https://<EMAIL>/blacklife/paypal-ruby-sdk.git', branch: 'cert_update'
# gem 'scout_apm'
gem 'puma_worker_killer'

gem "braintree", "~> 2.80.1"

gem 'wisper', '~> 2.0'
gem 'wisper-activejob', '~> 1.0'
gem 'rack-attack', '=5.0.1'

gem 'cleavejs-rails', git: 'https://github.com/raybradley/cleave-rails.git'
gem 'redis-rails'

gem 'redis'
gem 'hiredis'
gem 'redis-rack-cache'
gem 'rack-cache', :require => 'rack/cache'
gem 'redis-activesupport'

gem 'url_safe_base64'

gem 'newrelic_rpm'

gem "lograge"

gem 'sidekiq'
gem 'sidekiq-scheduler'
gem 'sidekiq-failures'
