GIT
  remote: https://<EMAIL>/blacklife/paypal-express.git
  revision: 6d795b651a3d5d51bca21977b82b51020de7a6ad
  branch: paypal-credit-enable
  specs:
    paypal-express (0.8.1)
      activesupport (>= 2.3)
      attr_required (>= 0.0.5)
      rest-client

GIT
  remote: https://github.com/guinex/razorpay-ruby.git
  revision: 334fbd3ec447e5ea4ec9a408dfc554e2780a6f71
  specs:
    razorpay (1.1.0)
      httparty (~> 0.8)

GIT
  remote: https://github.com/raybradley/cleave-rails.git
  revision: 10ff74b9de667ac7ef94a096d56db7d77f19e539
  specs:
    cleavejs-rails (1.3.7)
      railties (>= 3.1)

GIT
  remote: https://<EMAIL>/blacklife/paypal-ruby-sdk.git
  revision: b4f99c04dab75c323ca2531e14b3834884bc50bc
  branch: cert_update
  specs:
    paypal-sdk-rest (1.3.3)
      multi_json (~> 1.0)
      uuidtools (~> 2.1)
      xml-simple

GIT
  remote: https://bitbucket.org/blacklife/sunspot.git
  revision: 3f2027e1955b3f5d5df4458e667b2505b8afa6d1
  branch: grouping_in_solr_with_master
  specs:
    sunspot (2.2.7)
      pr_geohash (~> 1.0)
      rsolr (>= 1.1.1, < 3)
    sunspot_rails (2.2.7)
      nokogiri
      rails (>= 3)
      sunspot (= 2.2.7)

GEM
  remote: https://rubygems.org/
  specs:
    abstract_type (0.0.7)
    actionmailer (4.2.5)
      actionpack (= 4.2.5)
      actionview (= 4.2.5)
      activejob (= 4.2.5)
      mail (~> 2.5, >= 2.5.4)
      rails-dom-testing (~> 1.0, >= 1.0.5)
    actionpack (4.2.5)
      actionview (= 4.2.5)
      activesupport (= 4.2.5)
      rack (~> 1.6)
      rack-test (~> 0.6.2)
      rails-dom-testing (~> 1.0, >= 1.0.5)
      rails-html-sanitizer (~> 1.0, >= 1.0.2)
    actionpack-action_caching (1.1.1)
      actionpack (>= 4.0.0, < 5.0)
    actionview (4.2.5)
      activesupport (= 4.2.5)
      builder (~> 3.1)
      erubis (~> 2.7.0)
      rails-dom-testing (~> 1.0, >= 1.0.5)
      rails-html-sanitizer (~> 1.0, >= 1.0.2)
    activejob (4.2.5)
      activesupport (= 4.2.5)
      globalid (>= 0.3.0)
    activemodel (4.2.5)
      activesupport (= 4.2.5)
      builder (~> 3.1)
    activerecord (4.2.5)
      activemodel (= 4.2.5)
      activesupport (= 4.2.5)
      arel (~> 6.0)
    activesupport (4.2.5)
      i18n (~> 0.7)
      json (~> 1.7, >= 1.7.7)
      minitest (~> 5.1)
      thread_safe (~> 0.3, >= 0.3.4)
      tzinfo (~> 1.1)
    acts-as-taggable-on (3.5.0)
      activerecord (>= 3.2, < 5)
    acts_as_follower (0.2.1)
    adamantium (0.2.0)
      ice_nine (~> 0.11.0)
      memoizable (~> 0.4.0)
    annotate (2.6.10)
      activerecord (>= 3.2, <= 4.3)
      rake (~> 10.4)
    arel (6.0.4)
    ast (2.0.0)
    attr_required (1.0.1)
    attribute_normalizer (1.2.0)
    awesome_nested_set (3.0.2)
      activerecord (>= 4.0.0, < 5)
    aws-eventstream (1.1.1)
    aws-sdk (2.11.632)
      aws-sdk-resources (= 2.11.632)
    aws-sdk-core (2.11.632)
      aws-sigv4 (~> 1.0)
      jmespath (~> 1.0)
    aws-sdk-resources (2.11.632)
      aws-sdk-core (= 2.11.632)
    aws-sigv4 (1.2.3)
      aws-eventstream (~> 1, >= 1.0.2)
    babel-source (5.8.35)
    babel-transpiler (0.7.0)
      babel-source (>= 4.0, < 6)
      execjs (~> 2.0)
    bcrypt (3.1.13)
    better_errors (2.1.1)
      coderay (>= 1.0.0)
      erubis (>= 2.6.6)
      rack (>= 0.9.0)
    binding_of_caller (0.7.2)
      debug_inspector (>= 0.0.1)
    braintree (2.80.1)
      builder (>= 2.0.0)
    browser (2.2.0)
    builder (3.2.4)
    bullet (5.2.0)
      activesupport (>= 3.0.0)
      uniform_notifier (~> 1.10.0)
    byebug (6.0.2)
    callsite (0.0.11)
    capybara (2.5.0)
      mime-types (>= 1.16)
      nokogiri (>= 1.3.3)
      rack (>= 1.0.0)
      rack-test (>= 0.5.4)
      xpath (~> 2.0)
    childprocess (0.5.9)
      ffi (~> 1.0, >= 1.0.11)
    climate_control (0.2.0)
    coderay (1.1.0)
    coffee-rails (4.1.0)
      coffee-script (>= 2.2.0)
      railties (>= 4.0.0, < 5.0)
    coffee-script (2.4.1)
      coffee-script-source
      execjs
    coffee-script-source (*******)
    concord (0.1.5)
      adamantium (~> 0.2.0)
      equalizer (~> 0.0.9)
    concurrent-ruby (1.1.10)
    connection_pool (2.2.5)
    crass (1.0.6)
    dalli (2.7.4)
    database_cleaner (1.5.1)
    debug_inspector (0.0.2)
    devise (3.5.10)
      bcrypt (~> 3.0)
      orm_adapter (~> 0.1)
      railties (>= 3.2.6, < 5)
      responders
      thread_safe (~> 0.1)
      warden (~> 1.2.3)
    devise-async (0.10.1)
      devise (~> 3.2)
    devise_token_auth (0.1.43)
      devise (> 3.5.2, < 4.5)
      rails (< 6)
    diff-lcs (1.2.5)
    docile (1.1.5)
    domain_name (0.5.20190701)
      unf (>= 0.0.5, < 1.0.0)
    e2mmap (0.1.0)
    equalizer (0.0.11)
    erubis (2.7.0)
    et-orbi (1.2.7)
      tzinfo
    execjs (2.5.2)
    factory_girl (4.5.0)
      activesupport (>= 3.0.0)
    factory_girl_rails (4.5.0)
      factory_girl (~> 4.5.0)
      railties (>= 3.0.0)
    faker (1.6.1)
      i18n (~> 0.5)
    faraday (0.15.4)
      multipart-post (>= 1.2, < 3)
    ffi (1.9.10)
    foundation-rails (*******)
      railties (>= 3.1.0)
      sass (>= 3.3.0, < 3.5)
    foundation_rails_helper (1.0.0)
      actionpack (~> 4.1, >= 4.1.1)
      activemodel (~> 4.1, >= 4.1.1)
      activesupport (~> 4.1, >= 4.1.1)
      railties (~> 4.1, >= 4.1.1)
      tzinfo (~> 1.2, >= 1.2.2)
    friendly_id (5.1.0)
      activerecord (>= 4.0.0)
    fugit (1.8.1)
      et-orbi (~> 1, >= 1.2.7)
      raabro (~> 1.4)
    get_process_mem (0.2.1)
    git-version-bump (0.15.1)
    globalid (0.4.2)
      activesupport (>= 4.2.0)
    gon (6.3.2)
      actionpack (>= 3.0.20)
      i18n (>= 0.7)
      multi_json
      request_store (>= 1.0)
    haml (4.0.6)
      tilt
    haml-rails (0.9.0)
      actionpack (>= 4.0.1)
      activesupport (>= 4.0.1)
      haml (>= 4.0.6, < 5.0)
      html2haml (>= 1.0.1)
      railties (>= 4.0.1)
    hashie (3.6.0)
    hiredis (0.6.3)
    html2haml (2.0.0)
      erubis (~> 2.7.0)
      haml (~> 4.0.0)
      nokogiri (~> 1.6.0)
      ruby_parser (~> 3.5)
    http-accept (1.7.0)
    http-cookie (1.0.3)
      domain_name (~> 0.5)
    httparty (0.14.0)
      multi_xml (>= 0.5.2)
    i18n (0.9.5)
      concurrent-ruby (~> 1.0)
    ice_nine (0.11.1)
    jbuilder (2.4.1)
      activesupport (>= 3.0.0, < 5.1)
      multi_json (~> 1.2)
    jbuilder_cache_multi (0.1.0)
      jbuilder (>= 1.5.0, < 3)
    jmespath (1.4.0)
    jquery-rails (4.0.4)
      rails-dom-testing (~> 1.0)
      railties (>= 4.2.0)
      thor (>= 0.14, < 2.0)
    json (1.8.6)
    jwt (2.1.0)
    kaminari (0.16.3)
      actionpack (>= 3.0.0)
      activesupport (>= 3.0.0)
    lograge (0.11.2)
      actionpack (>= 4)
      activesupport (>= 4)
      railties (>= 4)
      request_store (~> 1.0)
    loofah (2.18.0)
      crass (~> 1.0.2)
      nokogiri (>= 1.5.9)
    mail (2.7.1)
      mini_mime (>= 0.1.1)
    maxminddb (0.1.7)
    memcachier (0.0.2)
    memoizable (0.4.2)
      thread_safe (~> 0.3, >= 0.3.1)
    meta_request (0.3.4)
      callsite (~> 0.0, >= 0.0.11)
      rack-contrib (~> 1.1)
      railties (>= 3.0.0, < 5.0.0)
    method_source (1.0.0)
    mime-types (3.4.1)
      mime-types-data (~> 3.2015)
    mime-types-data (3.2022.0105)
    mimemagic (0.3.10)
      nokogiri (~> 1)
      rake
    mini_mime (1.1.2)
    mini_portile2 (2.1.0)
    minitest (5.15.0)
    multi_json (1.13.1)
    multi_xml (0.6.0)
    multipart-post (2.3.0)
    netrc (0.11.0)
    newrelic_rpm (8.10.1)
    nokogiri (*******)
      mini_portile2 (~> 2.1.0)
    oauth2 (1.4.1)
      faraday (>= 0.8, < 0.16.0)
      jwt (>= 1.0, < 3.0)
      multi_json (~> 1.3)
      multi_xml (~> 0.5)
      rack (>= 1.2, < 3)
    oj (3.7.12)
    oj_mimic_json (1.0.1)
    omniauth (1.9.0)
      hashie (>= 3.4.6, < 3.7.0)
      rack (>= 1.6.2, < 3)
    omniauth-facebook (5.0.0)
      omniauth-oauth2 (~> 1.2)
    omniauth-google-oauth2 (0.6.0)
      jwt (>= 2.0)
      omniauth (>= 1.1.1)
      omniauth-oauth2 (>= 1.5)
    omniauth-oauth2 (1.6.0)
      oauth2 (~> 1.1)
      omniauth (~> 1.9)
    orm_adapter (0.5.0)
    paper_trail (4.0.0)
      activerecord (>= 3.0, < 6.0)
      activesupport (>= 3.0, < 6.0)
      request_store (~> 1.1)
    paperclip (5.3.0)
      activemodel (>= 4.2.0)
      activesupport (>= 4.2.0)
      mime-types
      mimemagic (~> 0.3.0)
      terrapin (~> 0.6.0)
    paranoia (2.1.5)
      activerecord (~> 4.0)
    parser (*******)
      ast (>= 1.1, < 3.0)
    pg (0.18.2)
    polyamorous (1.1.0)
      activerecord (>= 3.0)
    pr_geohash (1.0.0)
    private_attr (1.1.0)
    procto (0.0.2)
    promise (0.3.1)
    pry (0.14.1)
      coderay (~> 1.1)
      method_source (~> 1.0)
    puma (3.7.1)
    puma_worker_killer (0.1.0)
      get_process_mem (~> 0.2)
      puma (>= 2.7, < 4)
    raabro (1.4.0)
    rack (1.6.13)
    rack-attack (5.0.1)
      rack
    rack-cache (1.9.0)
      rack (>= 0.4)
    rack-contrib (1.3.0)
      git-version-bump (~> 0.15)
      rack (~> 1.4)
    rack-cors (1.0.3)
    rack-protection (2.2.4)
      rack
    rack-ssl-enforcer (0.2.9)
    rack-test (0.6.3)
      rack (>= 1.0)
    rack-timeout (0.3.2)
    rails (4.2.5)
      actionmailer (= 4.2.5)
      actionpack (= 4.2.5)
      actionview (= 4.2.5)
      activejob (= 4.2.5)
      activemodel (= 4.2.5)
      activerecord (= 4.2.5)
      activesupport (= 4.2.5)
      bundler (>= 1.3.0, < 2.0)
      railties (= 4.2.5)
      sprockets-rails
    rails-deprecated_sanitizer (1.0.4)
      activesupport (>= 4.2.0.alpha)
    rails-dom-testing (1.0.9)
      activesupport (>= 4.2.0, < 5.0)
      nokogiri (~> 1.6)
      rails-deprecated_sanitizer (>= 1.0.1)
    rails-html-sanitizer (1.4.3)
      loofah (~> 2.3)
    railties (4.2.5)
      actionpack (= 4.2.5)
      activesupport (= 4.2.5)
      rake (>= 0.8.7)
      thor (>= 0.18.1, < 2.0)
    rainbow (2.0.0)
    rake (10.5.0)
    rdoc (4.3.0)
    react-rails (1.2.0)
      babel-transpiler (>= 0.7.0)
      coffee-script-source (~> 1.8)
      connection_pool
      execjs
      rails (>= 3.2)
      tilt
    redis (4.1.1)
    redis-actionpack (5.1.0)
      actionpack (>= 4.0, < 7)
      redis-rack (>= 1, < 3)
      redis-store (>= 1.1.0, < 2)
    redis-activesupport (5.2.0)
      activesupport (>= 3, < 7)
      redis-store (>= 1.3, < 2)
    redis-rack (2.0.6)
      rack (>= 1.5, < 3)
      redis-store (>= 1.2, < 2)
    redis-rack-cache (2.1.2)
      rack-cache (>= 1.6, < 2)
      redis-store (>= 1.6, < 2)
    redis-rails (5.0.2)
      redis-actionpack (>= 5.0, < 6)
      redis-activesupport (>= 5.0, < 6)
      redis-store (>= 1.2, < 2)
    redis-store (1.8.1)
      redis (>= 4, < 5)
    reek (3.3.1)
      parser (~> *******)
      private_attr (~> 1.1)
      rainbow (~> 2.0)
      unparser (~> 0.2.2)
    request_store (1.2.0)
    responders (2.4.1)
      actionpack (>= 4.2.0, < 6.0)
      railties (>= 4.2.0, < 6.0)
    rest-client (2.1.0)
      http-accept (>= 1.7.0, < 2.0)
      http-cookie (>= 1.0.2, < 2.0)
      mime-types (>= 1.16, < 4.0)
      netrc (~> 0.8)
    rsolr (2.3.0)
      builder (>= 2.1.2)
      faraday (>= 0.9.0)
    rspec-core (3.4.1)
      rspec-support (~> 3.4.0)
    rspec-expectations (3.4.0)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.4.0)
    rspec-mocks (3.4.0)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.4.0)
    rspec-rails (3.4.0)
      actionpack (>= 3.0, < 4.3)
      activesupport (>= 3.0, < 4.3)
      railties (>= 3.0, < 4.3)
      rspec-core (~> 3.4.0)
      rspec-expectations (~> 3.4.0)
      rspec-mocks (~> 3.4.0)
      rspec-support (~> 3.4.0)
    rspec-support (3.4.1)
    ruby_parser (3.7.0)
      sexp_processor (~> 4.1)
    rubyzip (1.1.7)
    rufus-scheduler (3.9.1)
      fugit (~> 1.1, >= 1.1.6)
    sass (3.4.16)
    sass-rails (5.0.3)
      railties (>= 4.0.0, < 5.0)
      sass (~> 3.1)
      sprockets (>= 2.8, < 4.0)
      sprockets-rails (>= 2.0, < 4.0)
      tilt (~> 1.1)
    sdoc (0.4.1)
      json (~> 1.7, >= 1.7.7)
      rdoc (~> 4.0)
    selenium-webdriver (2.50.0)
      childprocess (~> 0.5)
      multi_json (~> 1.0)
      rubyzip (~> 1.0)
      websocket (~> 1.0)
    sexp_processor (4.6.0)
    shoulda-matchers (2.8.0)
      activesupport (>= 3.0.0)
    sidekiq (5.2.8)
      connection_pool (~> 2.2, >= 2.2.2)
      rack (< 2.1.0)
      rack-protection (>= 1.5.0)
      redis (>= 3.3.5, < 5)
    sidekiq-failures (1.0.4)
      sidekiq (>= 4.0.0)
    sidekiq-scheduler (3.2.2)
      e2mmap
      redis (>= 3, < 5)
      rufus-scheduler (~> 3.2)
      sidekiq (>= 3)
      thwait
      tilt (>= 1.4.0)
    simplecov (0.11.1)
      docile (~> 1.1.0)
      json (~> 1.8)
      simplecov-html (~> 0.10.0)
    simplecov-html (0.10.0)
    social-share-button (0.6.0)
      coffee-rails
      sass-rails
    spring (1.3.6)
    sprockets (3.7.2)
      concurrent-ruby (~> 1.0)
      rack (> 1, < 3)
    sprockets-rails (3.2.2)
      actionpack (>= 4.0)
      activesupport (>= 4.0)
      sprockets (>= 3.0.0)
    squeel (1.2.3)
      activerecord (>= 3.0)
      activesupport (>= 3.0)
      polyamorous (~> 1.1.0)
    state_machines (0.4.0)
    state_machines-activemodel (0.3.0)
      activemodel (~> 4.1)
      state_machines (>= 0.4.0)
    state_machines-activerecord (0.3.0)
      activerecord (~> 4.1)
      state_machines-activemodel (>= 0.3.0)
    stripe (11.2.0)
    sunspot_test (0.4.0)
      sunspot_rails (>= 1.2.1)
    terrapin (0.6.0)
      climate_control (>= 0.0.3, < 1.0)
    terser (1.1.14)
      execjs (>= 0.3.0, < 3)
    thor (1.2.1)
    thread_safe (0.3.6)
    thwait (0.2.0)
      e2mmap
    tilt (1.4.1)
    turbolinks (5.0.1)
      turbolinks-source (~> 5)
    turbolinks-source (5.0.3)
    tzinfo (1.2.9)
      thread_safe (~> 0.1)
    uglifier (2.7.1)
      execjs (>= 0.3.0)
      json (>= 1.8.0)
    unf (0.1.4)
      unf_ext
    unf_ext (*******)
    uniform_notifier (1.10.0)
    unparser (0.2.4)
      abstract_type (~> 0.0.7)
      adamantium (~> 0.2.0)
      concord (~> 0.1.5)
      diff-lcs (~> 1.2.5)
      equalizer (~> 0.0.9)
      parser (~> 2.2.2)
      procto (~> 0.0.2)
    url_safe_base64 (0.2.2)
    uuidtools (2.2.0)
    warden (1.2.7)
      rack (>= 1.0)
    web-console (2.2.1)
      activemodel (>= 4.0)
      binding_of_caller (>= 0.7.2)
      railties (>= 4.0)
      sprockets-rails (>= 2.0, < 4.0)
    websocket (1.2.2)
    wisper (2.0.0)
    wisper-activejob (1.0.0)
      activejob (>= 4.0.0)
      wisper
    xml-simple (1.1.8)
    xpath (2.0.0)
      nokogiri (~> 1.3)
    yard (0.8.7.6)

PLATFORMS
  ruby

DEPENDENCIES
  actionpack-action_caching
  acts-as-taggable-on
  acts_as_follower
  annotate
  attribute_normalizer (= 1.2.0)
  awesome_nested_set
  aws-sdk (~> 2)
  better_errors
  braintree (~> 2.80.1)
  browser (= 2.2.0)
  bullet
  byebug
  capybara
  cleavejs-rails!
  coffee-rails (~> 4.1.0)
  connection_pool
  dalli
  database_cleaner
  devise (~> 3.5.10)
  devise-async
  devise_token_auth (= 0.1.43)
  factory_girl_rails
  faker
  foundation-rails
  foundation_rails_helper
  friendly_id
  gon
  haml-rails
  hiredis
  httparty
  jbuilder
  jbuilder_cache_multi
  jquery-rails
  kaminari
  lograge
  maxminddb (= 0.1.7)
  memcachier
  meta_request
  newrelic_rpm
  oj (~> 3.7, >= 3.7.12)
  oj_mimic_json
  omniauth-facebook (~> 5.0)
  omniauth-google-oauth2 (~> 0.6.0)
  paper_trail
  paperclip (~> 5.0)
  paranoia (~> 2.0)
  paypal-express!
  paypal-sdk-rest!
  pg
  promise
  pry
  puma (~> 3.7.0)
  puma_worker_killer
  rack-attack (= 5.0.1)
  rack-cache
  rack-cors
  rack-ssl-enforcer
  rack-timeout
  rails (= 4.2.5)
  razorpay!
  react-rails (~> 1.2.0)
  redis
  redis-activesupport
  redis-rack-cache
  redis-rails
  reek
  request_store
  rspec-rails
  sass-rails (~> 5.0)
  sdoc (~> 0.4.0)
  selenium-webdriver
  shoulda-matchers
  sidekiq
  sidekiq-failures
  sidekiq-scheduler
  simplecov
  social-share-button
  spring
  squeel
  state_machines-activerecord
  stripe
  sunspot_rails!
  sunspot_test
  terser
  turbolinks (~> 5.0.0)
  uglifier (>= 1.3.0)
  url_safe_base64
  web-console (~> 2.0)
  wisper (~> 2.0)
  wisper-activejob (~> 1.0)
  yard

RUBY VERSION
   ruby 2.3.8p459

BUNDLED WITH
   1.17.3
