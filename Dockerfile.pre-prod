# Base image
FROM ruby:2.3-alpine

ENV APP_HOME /production

LABEL maintainer="<EMAIL>"

RUN apk update && apk add --no-cache libev libev-dev openssl postgresql-libs postgresql-dev musl-dev poppler-qt4-dev qt-dev nodejs bash git make gcc g++ pkgconfig build-base libxml2-dev libxslt-dev tzdata file shared-mime-info
RUN apk add ca-certificates
RUN rm -rf /var/cache/apk/*

RUN mkdir $APP_HOME
WORKDIR $APP_HOME

COPY Gemfile* $APP_HOME/
RUN bundle config git.allow_insecure true
RUN gem install bundler -v 1.17.3
RUN bundle config build.nokogiri --use-system-libraries
RUN bundle update --minor

COPY . $APP_HOME
RUN bundle install --without development test
RUN bundle exec spring binstub --remove --all
RUN bundle exec spring binstub --all
RUN export $(cat .env.alpine | xargs) && yes | bundle exec rake rails:update:bin	


COPY ./DigiCertSHA2ExtendedValidationServerCA.crt /usr/local/share/ca-certificates/DigiCertSHA2ExtendedValidationServerCA.crt
COPY ./DigiCertGlobalRootG2.crt /usr/local/share/ca-certificates/DigiCertGlobalRootG2.crt
COPY ./DigiCertHighAssuranceEVRootCA.crt.pem /usr/local/share/ca-certificates/DigiCertHighAssuranceEVRootCA.crt

RUN update-ca-certificates

RUN export $(cat .env.pre-prod | xargs) && bundle exec rake assets:precompile

COPY ./bin/docker-entrypoint.sh /bin/docker-entrypoint.sh
RUN chmod +x /bin/docker-entrypoint.sh
ENTRYPOINT ["/bin/docker-entrypoint.sh"]

# From paperclip dockerfile
RUN mkdir -p /tmp/build
WORKDIR /tmp/build
ADD https://mirraw.s3.amazonaws.com/libraries/image-magick/ImageMagick-6.9.2-10.tar.gz /tmp/build/ImageMagick-6.9.2-10.tar.gz
RUN tar xvf ImageMagick-6.9.2-10.tar.gz
RUN cd ImageMagick-6.9.2-10 && ./configure --disable-static --with-webp --with-quantum-depth=8 && make && make install
RUN ldconfig /usr/local/lib
WORKDIR $APP_HOME


# Run our app
# CMD ["bundle", "exec", "rails", "server", "-p", "80", "-b", "0.0.0.0"]
