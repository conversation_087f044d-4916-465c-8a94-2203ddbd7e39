test: &default
  widget_url: "https://static-eu.payments-amazon.com/cba/js/in/sandbox/PaymentWidgets.js"
  merchant_id: "A1DEYASDZHXKA3"
  aws_access_key: "AKIAJEQ2FEGXLDNBFVEQ"
  secret_key: <%= ENV["PAY_WITH_AMAZON_SECRET_KEY"] %>
  return_url: "http://staging-mirraw-mobile.herokuapp.com/order/amazon_success"
  cancel_url: "http://staging-mirraw-mobile.herokuapp.com/cart"

development:
  <<: *default

staging:
  <<: *default

production:
  <<: *default
  widget_url: "https://static-eu.payments-amazon.com/cba/js/in/PaymentWidgets.js"
  return_url: "https://<%= ENV['MOBILE_SITE_URL'] %>/order/amazon_success"
  cancel_url: "https://<%= ENV['MOBILE_SITE_URL'] %>/cart"